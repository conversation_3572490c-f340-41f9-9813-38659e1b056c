from fastapi import FastAP<PERSON>, HTTPException, UploadFile, File
from pydantic import BaseModel, field_validator,Field
from mode import MqttConfig, StartTaskRequest, StopTaskRequest
from model_manager import ModelManager
from typing import Dict, Any, List,Optional
import uvicorn
from videio_hander import VideoProcessor
import os
from predict_method import get_process

os.environ["CUDA_DEVICE_ORDER"]="PCI_BUS_ID"

#os.environ["CUDA_VISIBLE_DEVICES"] = "1" 
taks ={}

app = FastAPI()
model_manager = ModelManager()
model_manager.load_all_models()

@app.get("/models")
async def list_models():
    """获取所有已加载模型信息，包括输入尺寸要求"""
    return model_manager.get_loaded_models()

@app.get("/model/{model_id}/input_size")
async def get_model_input_size(model_id: str):
    """获取指定模型的输入尺寸要求"""
    try:
        h, w = model_manager.get_model_input_size(model_id)
        return {
            "model_id": model_id,
            "input_height": h,
            "input_width": w,
            "input_size": f"{w}x{h}"
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))



import json
@app.post('/start')
async def start_task(task:StartTaskRequest):
    #input_rtmp_url: rtmp://11************:19403/live/stream1
    #out_rtmp_url: rtmp://11************:19403/live/stream2
    key_list = task.app_code.split(",")
    print(task.app_code)
    models={k:  get_process(v.model_config,task) for k, v in model_manager.loaded_models.items() if k in key_list}
    # if(len(models)==0):
    #     return {"status": "未找到匹配的模型"}
    if taks.get(task.task_id) is not None:
        return {"status": f"任务 {task.task_id} 已存在"}
    taks[task.task_id] =VideoProcessor()
    if(task.mqtt_config is None):
        task.mqtt_config = MqttConfig(host="***************",port=31883,username="admin",password="Nuchina@1521")
    taks[task.task_id].start_processing(task.task_id,task.device_sn,task.callback_url,task.input_rtmp_url,task.out_rtmp_url,models,max_parallel_frames=4,mqtt_config= task.mqtt_config)

    return {"status": f"任务 {task.task_id} 已启动"}
@app.post('/stop')
async def stop(task:StopTaskRequest):
    if taks.get(task.task_id) is None:
        return {"status": f"任务 {task.task_id} 不存在"}
    taks[task.task_id].stop_processing(task.task_id)
    del taks[task.task_id]
    return  {"status": f"任务 {task.task_id} 已停止"}

def start_server(host: str = "0.0.0.0", port: int = 8009):
    """启动服务"""
    uvicorn.run(app, host=host, port=port)

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description="启动多模型预测服务")
    parser.add_argument("--host", type=str, default="0.0.0.0", help="服务监听地址")
    parser.add_argument("--port", type=int, default=8009, help="服务监听端口")
    args = parser.parse_args()
    
    start_server(host=args.host, port=args.port)