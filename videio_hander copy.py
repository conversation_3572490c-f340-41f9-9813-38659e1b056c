import cv2
import subprocess
import threading
from typing import Dict, Op<PERSON>, <PERSON><PERSON>,List
from dataclasses import dataclass
from collections import deque, OrderedDict
import numpy as np
import queue
import time

@dataclass
class VideoProcessingTask:
    task_id: str
    input_path: str
    output_path: str
    models: Dict
    process: Optional[threading.Thread] = None
    stop_event: Optional[threading.Event] = None
    ffmpeg_process: Optional[subprocess.Popen] = None
    max_parallel_frames: int = 4
    frame_buffer: Optional[OrderedDict] = None  # 改为有序字典存储帧
    next_frame_idx: int = 0
    frame_queue: Optional[queue.Queue] = None
    processed_frame_queue: Optional[queue.Queue] = None
    writer_thread: Optional[threading.Thread] = None
    worker_threads: Optional[list] = None
    error_occurred: bool = False

class VideoProcessor:
    def __init__(self):
        self.active_tasks: Dict[str, VideoProcessingTask] = {}
        self.lock = threading.Lock()

    def start_processing(
        self,
        task_id: str,
        input_path: str,
        output_path: str,
        model_list: Dict,
        max_parallel_frames: int = 6
    ) -> bool:
        """Start a new video processing task in a separate thread"""
        with self.lock:
            if task_id in self.active_tasks:
                print(f"Task ID {task_id} already exists")
                return False

            stop_event = threading.Event()
            frame_queue = queue.Queue(maxsize=max_parallel_frames * 2)
            processed_frame_queue = queue.Queue()
            
            task = VideoProcessingTask(
                task_id=task_id,
                input_path=input_path,
                output_path=output_path,
                models=model_list,
                stop_event=stop_event,
                max_parallel_frames=max_parallel_frames,
                frame_buffer=OrderedDict(),  # 使用有序字典存储缓冲帧
                frame_queue=frame_queue,
                processed_frame_queue=processed_frame_queue,
                worker_threads=[]
            )
            
            # Start the processing thread
            process_thread = threading.Thread(
                target=self._process_video,
                args=(task,),
                daemon=True
            )
            process_thread.start()
            task.process = process_thread
            
            self.active_tasks[task_id] = task
            return True

    def stop_processing(self, task_id: str) -> bool:
        """Stop an ongoing video processing task"""
        with self.lock:
            task = self.active_tasks.get(task_id)
            if not task:
                print(f"Task ID {task_id} not found")
                return False
            
            # 设置停止事件
            task.stop_event.set()
            
            # 清理FFmpeg进程
            if task.ffmpeg_process:
                try:
                    task.ffmpeg_process.stdin.close()
                    task.ffmpeg_process.terminate()
                    task.ffmpeg_process.wait(timeout=2.0)
                except Exception as e:
                    print(f"Error stopping ffmpeg process: {e}")
                    try:
                        task.ffmpeg_process.kill()
                    except:
                        pass
            
            # 等待线程结束
            if task.process and task.process.is_alive():
                task.process.join(timeout=2.0)
                if task.process.is_alive():
                    print(f"Warning: Thread for task {task_id} did not stop gracefully")
            
            # 清理工作线程
            for worker in task.worker_threads:
                if worker.is_alive():
                    worker.join(timeout=1.0)
            
            # 清理写入线程
            if task.writer_thread and task.writer_thread.is_alive():
                task.writer_thread.join(timeout=1.0)
            
            # 从活动任务中移除
            if task_id in self.active_tasks:
                del self.active_tasks[task_id]
            return True

    def _process_frame(self, task: VideoProcessingTask, frame: np.ndarray, frame_idx: int) -> Tuple[int, np.ndarray]:
        """Process a single frame with all models"""
        try:
            processed_frame = frame.copy()
            
            for model_id, model in task.models.items():
                # 添加resize_with_padding预处理
                resized_frame = frame
                # Run model prediction
                predictions = model.predict_pipeline(resized_frame)
                # Apply visualization
                processed_frame = model.visualize(processed_frame, predictions)
                
            return frame_idx, processed_frame
        except Exception as e:
            print(f"Error processing frame {frame_idx}: {e}")
            # 返回原始帧作为回退
            return frame_idx, frame

    def _resize_with_padding(self, image, target_size=(384, 640)):
        """Resize image with padding (used in _process_frame)"""
        h, w = image.shape[:2]
        target_h, target_w = target_size

        scale = min(target_w / w, target_h / h)
        new_w, new_h = int(w * scale), int(h * scale)
        resized = cv2.resize(image, (new_w, new_h))

        padded = np.full((target_h, target_w, 3), 114, dtype=np.uint8)
        pad_top = (target_h - new_h) // 2
        pad_left = (target_w - new_w) // 2
        padded[pad_top:pad_top+new_h, pad_left:pad_left+new_w] = resized

        return padded

    def _frame_processor_worker(self, task: VideoProcessingTask):
        """Worker thread for processing frames"""
        while not task.stop_event.is_set() and not task.error_occurred:
            try:
                # Get frame to process from the queue
                frame_data = task.frame_queue.get(timeout=0.5)
                if frame_data is None:  # 收到停止信号
                    task.frame_queue.task_done()
                    break
                    
                frame_idx, frame = frame_data
                
                # Process the frame
                result = self._process_frame(task, frame, frame_idx)
                
                # Put the result in the processed queue
                task.processed_frame_queue.put(result)
                
                task.frame_queue.task_done()
            except queue.Empty:
                continue
            except Exception as e:
                print(f"Error in frame processor worker: {e}")
                task.error_occurred = True
                task.stop_event.set()
                break

    def _frame_writer(self, task: VideoProcessingTask):
        """Write processed frames to ffmpeg in order"""
        while not task.stop_event.is_set() and not task.error_occurred:
            try:
                # Get processed frame from the queue
                frame_idx, processed_frame = task.processed_frame_queue.get(timeout=0.5)
                
                # 检查是否是预期的下一帧
                if frame_idx == task.next_frame_idx:
                    # 写入帧
                    self._write_frame_to_ffmpeg(task, processed_frame)
                    task.next_frame_idx += 1
                    
                    # 检查缓冲区中是否有后续帧可以写入
                    while task.next_frame_idx in task.frame_buffer:
                        buffered_frame = task.frame_buffer.pop(task.next_frame_idx)
                        self._write_frame_to_ffmpeg(task, buffered_frame)
                        task.next_frame_idx += 1
                else:
                    # 不是预期帧，存入缓冲区
                    task.frame_buffer[frame_idx] = processed_frame
                    
                task.processed_frame_queue.task_done()
            except queue.Empty:
                continue
            except Exception as e:
                print(f"Error in frame writer: {e}")
                task.error_occurred = True
                task.stop_event.set()
                break

    def _write_frame_to_ffmpeg(self, task: VideoProcessingTask, frame: np.ndarray):
        """Helper method to write frame to ffmpeg with error handling"""
        try:
            if task.ffmpeg_process and task.ffmpeg_process.stdin:
                task.ffmpeg_process.stdin.write(frame.tobytes())
                task.ffmpeg_process.stdin.flush()
        except (BrokenPipeError, ConnectionResetError, OSError) as e:
            print(f"Task {task.task_id} - FFmpeg process error: {str(e)}")
            task.error_occurred = True
            task.stop_event.set()
            raise

    def _process_video(self, task: VideoProcessingTask):
        """Internal method for video processing running in a separate thread"""
        try:
            # Start worker threads
            for _ in range(task.max_parallel_frames):
                worker = threading.Thread(
                    target=self._frame_processor_worker,
                    args=(task,),
                    daemon=True
                )
                worker.start()
                task.worker_threads.append(worker)
            
            # Start frame writer thread
            task.writer_thread = threading.Thread(
                target=self._frame_writer,
                args=(task,),
                daemon=True
            )
            task.writer_thread.start()

            # Open video capture
            cap = self._open_video_capture(task)
            if not cap:
                return

            # Get video properties
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            if fps <= 0:
                fps = 25
            print(f'Task {task.task_id} - fps: {fps}')

            # Start FFmpeg process
            task.ffmpeg_process = self._start_ffmpeg_process(task, width, height, fps)
            if not task.ffmpeg_process:
                return

            # Main processing loop
            frame_idx = 0
            satart=time.time()
            while cap.isOpened() and not task.stop_event.is_set() and not task.error_occurred:
                ret, frame = cap.read()
                end = time.time()
                print('read:'+str(end-satart))
                satart=end
                if not ret:
                    print(f"Task {task.task_id} - Reached end of input video")
                    break
                frame=cv2.resize(frame, (960, 720))
                # Put frame in the processing queue
                try:
                    task.frame_queue.put((frame_idx, frame), timeout=1.0)
                    frame_idx += 1
                except queue.Full:
                    if task.stop_event.is_set():
                        break
                    print(f"Task {task.task_id} - Frame queue full, waiting...")
                    time.sleep(0.1)

            # Wait for all frames to be processed
            self._wait_for_completion(task, frame_idx)

        except Exception as e:
            print(f"Task {task.task_id} - Processing error: {str(e)}")
            task.error_occurred = True
        finally:
            self._cleanup_resources(task, cap)
            print(f"Task {task.task_id} - Processing completed")

    def _open_video_capture(self, task: VideoProcessingTask):
        """Helper method to open video capture"""
        cap = cv2.VideoCapture(task.input_path)
        if not cap.isOpened():
            print(f"Task {task.task_id} - Failed to open input video: {task.input_path}")
            task.error_occurred = True
            return None
        return cap

    def _start_ffmpeg_process(self, task: VideoProcessingTask, width: int, height: int, fps: float):
        """Helper method to start FFmpeg process"""
        command = [
           'ffmpeg',
           "-re",
      '-y',                   # 覆盖输出文件
    '-f', 'rawvideo',       # 输入格式
    '-vcodec', 'rawvideo',  # 输入编解码器
    '-pix_fmt', 'bgr24',    # 像素格式(与OpenCV匹配)
    '-s', '960x720',       # 分辨率
    '-r', str(fps),             # 输入帧率
    '-i', '-',              # 从标准输入读取
    '-c:v', 'libx264',      # 视频编码器
    '-preset', 'fast',      # 编码速度/质量平衡
    '-tune', 'zerolatency', # 低延迟模式
    '-pix_fmt', 'yuv420p',  # 输出像素格式
    '-crf', '23',           # 质量(0-51, 越低越好)
    '-g', '60',             # GOP大小
    '-f', 'flv',            # 输出格式
            task.output_path
        ]

        try:
            return subprocess.Popen(
                command,
                stdin=subprocess.PIPE
            )
        except Exception as e:
            print(f"Task {task.task_id} - Failed to start FFmpeg: {e}")
            task.error_occurred = True
            return None

    def _wait_for_completion(self, task: VideoProcessingTask, total_frames: int):
        """Wait for all frames to be processed"""
        # 等待所有帧被处理
        task.frame_queue.join()
        
        # 等待所有处理后的帧被写入
        task.processed_frame_queue.join()
        
        # 等待缓冲区中的帧被写入
        while len(task.frame_buffer) > 0 and not task.stop_event.is_set():
            time.sleep(0.1)
        
        print(f"Task {task.task_id} - Processed {total_frames} frames")

    def _cleanup_resources(self, task: VideoProcessingTask, cap):
        """Clean up all resources"""
        # 发送停止信号给工作线程
        for _ in range(len(task.worker_threads)):
            task.frame_queue.put(None)
        
        # 释放视频捕获
        if cap:
            cap.release()
        
        # 关闭FFmpeg进程
        if task.ffmpeg_process:
            try:
                if task.ffmpeg_process.stdin:
                    task.ffmpeg_process.stdin.close()
                task.ffmpeg_process.wait(timeout=2.0)
            except:
                pass
        
        # 从活动任务中移除
        with self.lock:
            if task.task_id in self.active_tasks:
                del self.active_tasks[task.task_id]

    def list_tasks(self) -> List[str]:
        """List all active task IDs"""
        with self.lock:
            return list(self.active_tasks.keys())