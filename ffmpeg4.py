import cv2
import numpy as np
from datetime import datetime
from ultralytics import YOL<PERSON>
from collections import OrderedDict, deque
import subprocess
import os
import logging
import time
import threading
import queue

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ViolationTracker:
    """违规车辆追踪器，负责管理和记录违规车辆信息"""
    
    def __init__(self, display_time=5):
        # 存储违规车辆信息 {vehicle_id: {'box_data': (x1,y1,x2,y2,cls), 'start_frame': frame_count}}
        self.violating_vehicles = OrderedDict()
        # 存储已消失但还需要显示的违规车辆信息
        self.inactive_violations = OrderedDict()
        # 显示时间（秒）
        self.display_time = display_time
        # 存储活跃告警信息 {vehicle_id: {'frame': 触发告警的帧号, 'start_time': 触发告警的时间}}
        self.active_alerts = {}
        # 存储已完成的告警信息
        self.completed_alerts = OrderedDict()
        # 告警持续时间（秒）
        self.alert_duration = 5
        # 存储车辆开始违规的帧号
        self.start_frames = {}
        
    def register(self, track_id, box_data, current_frame):
        """注册新的违规车辆"""
        self.violating_vehicles[track_id] = {
            'box_data': box_data,
            'start_time': current_frame
        }
        self.start_frames[track_id] = current_frame

    def deregister(self, track_id, current_frame):
        """注销违规车辆并保存信息"""
        if track_id in self.violating_vehicles:
            # 保存到非活跃违规车辆列表
            self.inactive_violations[track_id] = {
                'start_frame': self.start_frames[track_id],
                'end_frame': current_frame
            }
            # 从当前追踪中移除
            del self.violating_vehicles[track_id]
            del self.start_frames[track_id]

        # 如果车辆有告警，标记为已完成
        if track_id in self.active_alerts:
            alert_info = self.active_alerts[track_id]
            alert_info['end_frame'] = current_frame
            self.completed_alerts[track_id] = alert_info
            del self.active_alerts[track_id]

    def update(self, tracked_vehicles, current_frame):
        """更新违规车辆状态"""
        # 获取当前活跃的追踪ID
        current_ids = set(tracked_vehicles.keys())

        # 检查消失的车辆
        for track_id in list(self.violating_vehicles.keys()):
            if track_id not in current_ids:
                # 车辆消失，注销
                self.deregister(track_id, current_frame)

        # 更新现有车辆信息
        for track_id, vehicle_info in tracked_vehicles.items():
            if track_id in self.violating_vehicles:
                # 更新已存在的违规车辆
                self.violating_vehicles[track_id]['box_data'] = vehicle_info['box_data']
            else:
                # 注册新违规车辆
                self.register(track_id, vehicle_info['box_data'], current_frame)

        return self.violating_vehicles

    def get_all_violations(self):
        """获取所有违规车辆信息（包括活跃和非活跃的）"""
        return list(self.violating_vehicles.items()) + list(self.inactive_violations.items())

    def get_duration(self, track_id, current_frame, frame_rate):
        """获取违规车辆停留时长（秒）"""
        if track_id in self.violating_vehicles:
            # 活跃车辆：计算从开始帧到当前帧的时长
            start_frame = self.start_frames[track_id]
            frames_elapsed = current_frame - start_frame
            return frames_elapsed / frame_rate
        elif track_id in self.inactive_violations:
            # 非活跃车辆：使用存储的开始帧和结束帧
            data = self.inactive_violations[track_id]
            frames_elapsed = data['end_frame'] - data['start_frame']
            return frames_elapsed / frame_rate
        return 0.0

    def check_and_trigger_alert(self, track_id, current_frame, duration):
        """检查并触发告警"""
        if duration >= 5.0:  # 停留时长超过5秒
            if track_id not in self.active_alerts:
                # 首次触发告警
                self.active_alerts[track_id] = {
                    'trigger_frame': current_frame,
                    'start_time': datetime.now().strftime('%H:%M:%S')
                }
                return True
        return False

    def update_alerts(self, current_frame, frame_rate):
        """更新告警状态"""
        completed = []
        for track_id, alert_info in list(self.active_alerts.items()):
            # 计算告警持续时间（帧）
            elapsed_frames = current_frame - alert_info['trigger_frame']
            elapsed_time = elapsed_frames / frame_rate

            # 检查告警是否超过显示时间
            if elapsed_time > self.alert_duration:
                # 添加到已完成告警
                alert_info['end_frame'] = current_frame
                self.completed_alerts[track_id] = alert_info
                del self.active_alerts[track_id]
                completed.append(track_id)
        return completed

    def get_active_alerts(self):
        """获取当前活跃告警"""
        return self.active_alerts

    def get_completed_alerts(self):
        """获取已完成告警"""
        return self.completed_alerts


class FireLaneDetector:
    """消防车道违规检测系统主类"""
    
    def __init__(self, fire_lane_model_path, vehicle_model_path, config=None):
        # 使用YOLO类加载模型
        self.fire_lane_model = YOLO(fire_lane_model_path, task="segment")
        self.vehicle_model = YOLO(vehicle_model_path)

        # 设置模型参数
        self.fire_lane_model.conf = 0.3  # 置信度阈值
        if self.vehicle_model:
            self.vehicle_model.conf = 0.5   # 置信度阈值

        # 车辆类别ID列表
        self.vehicle_classes = [1]  # 汽车、摩托车、公交车、卡车

        # 创建违规车辆追踪器
        self.tracker = ViolationTracker(display_time=5)  # 显示5秒
        
        # 应用配置
        self.config = config or {}
        self._apply_config()
        
        # 初始化其他组件
        self._init_resources()

    def _apply_config(self):
        """应用配置参数"""
        # 视频处理参数
        self.scale_factor = self.config.get('scale_factor', 0.7)  # 增加默认缩放因子，提高清晰度
        self.target_fps = self.config.get('target_fps', 15)
        self.skip_frames = self.config.get('skip_frames', 1)
        
        # 性能监控
        self.performance_monitor = self.config.get('performance_monitor', False)
        self.monitor_interval = self.config.get('monitor_interval', 5)  # 秒
        
        # 告警配置
        self.alert_interval = self.config.get('alert_interval', 5)  # 秒
        self.max_history_alerts = self.config.get('max_history_alerts', 3)
        
        # 多线程处理
        self.use_multithreading = self.config.get('use_multithreading', False)
        self.queue_size = self.config.get('queue_size', 20)
        
        # 文字显示配置
        self.font_scale = self.config.get('font_scale', 0.7)  # 字体大小
        self.font_thickness = self.config.get('font_thickness', 2)  # 字体粗细
        self.line_thickness = self.config.get('line_thickness', 2)  # 线条粗细

    def _init_resources(self):
        """初始化资源和数据结构"""
        # 性能监控
        self.processing_times = deque(maxlen=100)
        self.frames_processed = 0
        self.last_monitor_time = time.time()
        
        # 多线程处理
        if self.use_multithreading:
            self.frame_queue = queue.Queue(maxsize=self.queue_size)
            self.result_queue = queue.Queue(maxsize=self.queue_size)
            self.processing_thread = threading.Thread(target=self._processing_worker, daemon=True)
            self.processing_thread.start()
            
    def _processing_worker(self):
        """多线程处理工作线程"""
        while True:
            try:
                frame, frame_count = self.frame_queue.get()
                if frame is None:  # 退出信号
                    break
                
                # 处理帧
                result = self._process_frame(frame, frame_count)
                self.result_queue.put((frame.copy(), result, frame_count))
                self.frame_queue.task_done()
            except Exception as e:
                logger.error(f"处理线程错误: {str(e)}")
                self.frame_queue.task_done()

    def detect_vehicles(self, frame):
        """检测车辆并追踪"""
        if not self.vehicle_model:
            return {}
            
        # 使用ByteTrack追踪车辆
        results = self.vehicle_model.track(frame, persist=True, verbose=False)

        tracked_vehicles = {}

        if results[0].boxes.id is not None:
            boxes = results[0].boxes.xyxy.cpu().numpy().astype(int)
            classes = results[0].boxes.cls.cpu().numpy().astype(int)
            track_ids = results[0].boxes.id.cpu().numpy().astype(int)

            for box, cls, track_id in zip(boxes, classes, track_ids):
                if cls in self.vehicle_classes:
                    x1, y1, x2, y2 = box
                    tracked_vehicles[track_id] = {
                        'box_data': (x1, y1, x2, y2, cls),
                        'centroid': ((x1 + x2) // 2, (y1 + y2) // 2)
                    }

        return tracked_vehicles

    def detect_fire_lane(self, frame):
        """检测消防车道区域"""
        fire_lane_mask = np.zeros((frame.shape[0], frame.shape[1]), dtype=np.uint8)
        fire_results = self.fire_lane_model(frame, verbose=False)
        
        # 创建用于彩色分割的叠加层
        overlay = frame.copy()

        contours = []
        if fire_results[0].masks is not None:
            # 处理每个检测到的消防车道区域
            for ci, c in enumerate(fire_results[0]):
                # 获取物体轮廓
                contour = c.masks.xy.pop().astype(np.int32).reshape(-1, 1, 2)
                contours.append(contour)
                # 在掩码上填充轮廓区域
                cv2.fillPoly(fire_lane_mask, [contour], 255)
                # 在叠加层上绘制消防车道区域
                cv2.fillPoly(overlay, [contour], (0, 255, 0))

        # 将原始图像与彩色分割叠加层融合
        alpha = 0.3  # 透明度因子
        processed_frame = cv2.addWeighted(overlay, alpha, frame, 1 - alpha, 0)

        return {
            'fire_lane_mask': fire_lane_mask,
            'processed_frame': processed_frame,
            'contours': contours
        }
    
    def _is_in_fire_lane(self, vehicle_box, fire_lane_mask):
        """检查车辆是否在消防车道上（基于重叠率）"""
        x1, y1, x2, y2 = vehicle_box
        vehicle_area = (x2 - x1) * (y2 - y1)

        # 创建车辆掩码
        vehicle_mask = np.zeros(fire_lane_mask.shape, dtype=np.uint8)
        cv2.rectangle(vehicle_mask, (x1, y1), (x2, y2), 255, -1)  # -1表示填充矩形

        # 计算与消防车道的重叠部分
        overlapped = cv2.bitwise_and(vehicle_mask, fire_lane_mask)
        overlap_area = np.count_nonzero(overlapped)

        # 计算重叠率
        overlap_ratio = overlap_area / vehicle_area if vehicle_area > 0 else 0

        # 如果重叠率超过10%，则视为违规
        return overlap_ratio > 0.1
    
    def _process_frame(self, frame, frame_count):
        """处理单帧图像"""
        # 分离消防车道检测和车辆追踪
        fire_lane_result = self.detect_fire_lane(frame)
        
        # 使用原始帧进行车辆检测，避免受消防车道绘制的影响
        tracked_vehicles = self.detect_vehicles(frame)

        # 筛选出在消防车道上的违规车辆
        violating_vehicles = {}
        for track_id, vehicle_info in tracked_vehicles.items():
            box_data = vehicle_info['box_data']
            x1, y1, x2, y2, cls = box_data
            if self._is_in_fire_lane((x1, y1, x2, y2), fire_lane_result['fire_lane_mask']):
                violating_vehicles[track_id] = vehicle_info

        # 更新违规车辆追踪器
        self.tracker.update(violating_vehicles, frame_count)

        return {
            'tracked_vehicles': tracked_vehicles,
            'violating_vehicles': violating_vehicles,
            'fire_lane_result': fire_lane_result
        }
    
    def _draw_vehicles(self, frame, tracked_vehicles, violating_vehicles, frame_count):
        """绘制车辆检测结果"""
        # 绘制所有检测到的车辆（绿色细框）
        for track_id, vehicle_info in tracked_vehicles.items():
            x1, y1, x2, y2, cls = vehicle_info['box_data']
            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), self.line_thickness)  # 增加线条粗细

        # 绘制活跃的违规车辆
        violation_count = 0
        for track_id, vehicle_info in self.tracker.violating_vehicles.items():
            violation_count += 1
            box_data = vehicle_info.get('box_data')
            if not box_data:
                continue

            try:
                x1, y1, x2, y2, cls = box_data
            except (ValueError, TypeError):
                continue

            # 计算停留时间（基于帧率）
            duration = self.tracker.get_duration(track_id, frame_count, self.target_fps)

            # 检查并触发告警
            if self.tracker.check_and_trigger_alert(track_id, frame_count, duration):
                timestamp = datetime.now().strftime('%H:%M:%S')
                logger.warning(f"告警：违规车辆 ID:{track_id} 停留时长已超过5秒 ({duration:.1f}s)")

            # 在车辆上方显示ID和停留时间 - 增加字体大小和粗细
            info_text = f"ID:{track_id} {duration:.1f}s"
            cv2.putText(frame, info_text, (x1, y1 - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, self.font_scale, (0, 0, 255), self.font_thickness)

            # 红色粗边框（违规车辆）- 增加线条粗细
            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 0, 255), self.line_thickness + 1)

        return violation_count
    
    def _draw_status_info(self, frame, violation_count, process_time, frame_count):
        """绘制状态信息"""
        height, width = frame.shape[:2]
        
        # 在左上角显示所有违规车辆信息（包括活跃和非活跃的）
        all_violations = self.tracker.get_all_violations()
        y_offset = 30  # 从视频顶部开始
        line_height = int(25 * self.font_scale)  # 根据字体大小调整行高

        # 显示每辆违规车辆信息
        for violation_id, violation_info in all_violations:
            # 对于活跃车辆，获取当前时长
            duration = self.tracker.get_duration(violation_id, frame_count, self.target_fps)

            # 检查该车辆是否活跃（当前在画面中）
            is_active = violation_id in self.tracker.violating_vehicles

            # 设置文本颜色：活跃车辆为红色，非活跃车辆为灰色
            color = (0, 0, 255) if is_active else (255, 0, 0)

            # 状态指示符
            status = "" if is_active else " (LEAVE)"

            info_text = f"ID:{violation_id} T:{duration:.1f}s{status}"

            # 在左上角显示车辆信息 - 增加字体大小和粗细
            cv2.putText(frame, info_text, (15, y_offset),
                        cv2.FONT_HERSHEY_SIMPLEX, self.font_scale, color, self.font_thickness)

            y_offset += line_height

        # 更新告警状态
        self.tracker.update_alerts(frame_count, self.target_fps)

        # 获取当前活跃告警
        active_alerts = self.tracker.get_active_alerts()
        # 获取完成告警
        completed_alerts = self.tracker.get_completed_alerts()

        # 显示告警标题 - 增加字体大小和粗细
        alert_title = "alert>5s"
        cv2.putText(frame, alert_title, (width - 220, 30),
                    cv2.FONT_HERSHEY_SIMPLEX, self.font_scale, (0, 0, 255), self.font_thickness)

        # 显示告警计数器 - 增加字体大小和粗细
        alert_count_text = f"alerts: {len(active_alerts)}"
        cv2.putText(frame, alert_count_text, (width - 220, 60),
                    cv2.FONT_HERSHEY_SIMPLEX, self.font_scale, (0, 0, 255), self.font_thickness)

        # 显示活跃告警 - 增加字体大小和粗细
        alert_y = 90
        for track_id, alert_info in active_alerts.items():
            duration = self.tracker.get_duration(track_id, frame_count, self.target_fps)
            alert_text = f"ID:{track_id} T:{duration:.1f}s"
            cv2.putText(frame, alert_text, (width - 220, alert_y),
                        cv2.FONT_HERSHEY_SIMPLEX, self.font_scale * 0.9, (0, 0, 255), self.font_thickness)
            alert_y += line_height

        # 显示最近N条已完成告警 - 增加字体大小和粗细
        completed_y = alert_y + 15
        if completed_alerts:
            cv2.putText(frame, "history:", (width - 220, completed_y),
                        cv2.FONT_HERSHEY_SIMPLEX, self.font_scale * 0.9, (0, 165, 255), self.font_thickness)
            completed_y += line_height

            # 只显示最近N条已完成告警
            recent_alerts = list(completed_alerts.items())[-self.max_history_alerts:]
            for track_id, alert_info in recent_alerts:
                alert_text = f"ID:{track_id} {alert_info['start_time']}"
                cv2.putText(frame, alert_text, (width - 220, completed_y),
                            cv2.FONT_HERSHEY_SIMPLEX, self.font_scale * 0.9, (0, 165, 255), self.font_thickness)
                completed_y += line_height

        # 计算处理时间和帧率
        current_fps = 1.0 / process_time if process_time > 0 else 0
        
        # 显示状态信息 - 增加字体大小和粗细
        status = f"FPS: {current_fps:.1f} | Violations: {violation_count}/{len(all_violations)}"
        cv2.putText(frame, status, (15, height - 20),
                    cv2.FONT_HERSHEY_SIMPLEX, self.font_scale, (0, 0, 255), self.font_thickness)

        return frame
    
    def _monitor_performance(self, frame_count, start_time):
        """监控性能"""
        if not self.performance_monitor:
            return
            
        process_time = time.time() - start_time
        self.processing_times.append(process_time)
        self.frames_processed += 1
        
        current_time = time.time()
        if current_time - self.last_monitor_time > self.monitor_interval:
            avg_fps = self.frames_processed / (current_time - self.last_monitor_time)
            avg_process_time = sum(self.processing_times) / len(self.processing_times) if self.processing_times else 0
            logger.info(f"性能监控: 平均FPS: {avg_fps:.1f}, 平均处理时间: {avg_process_time*1000:.1f}ms, 总帧: {frame_count}")
            self.frames_processed = 0
            self.last_monitor_time = current_time
    
    def process_video(self, video_path, rtmp_url="rtmp://**************:1935/live/stream4"):
        """处理视频流并推送到RTMP服务器"""
        # 打开视频文件
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            logger.error(f"无法打开视频文件 {video_path}")
            return

        # 获取视频属性
        original_fps = cap.get(cv2.CAP_PROP_FPS)
        original_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        original_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        # 计算压缩后的尺寸 - 使用更大的缩放因子提高清晰度
        # scaled_width = int(original_width * self.scale_factor)
        # scaled_height = int(original_height * self.scale_factor)
        # 计算新的缩放尺寸
        target_height=720
        # 否则按比例缩放
        scale_ratio = target_height / original_height
        scaled_width = int(original_width * scale_ratio)
        scaled_height = target_height
        frame_count = 0
        frame_skip_counter = 0

        # 设置FFmpeg推流命令 - 增加视频比特率提高清晰度
        command = [
            'ffmpeg',
            '-y',  # 覆盖输出文件（如果有）
            '-f', 'rawvideo',  # 输入格式为原始视频
            '-vcodec', 'rawvideo',  # 视频编解码器
            '-pix_fmt', 'bgr24',  # OpenCV的像素格式
            '-s', f'{scaled_width}x{scaled_height}',  # 缩放后的视频尺寸
            '-r', str(self.target_fps),  # 目标帧率
            '-i', '-',  # 从标准输入读取
            '-c:v', 'libx264',  # 输出视频编码器
            '-preset', 'ultrafast',  # 编码速度预设
            '-tune', 'zerolatency',  # 零延迟调优
            '-pix_fmt', 'yuv420p',  # 输出像素格式
            '-b:v', '2500k',  # 增加视频比特率，提高清晰度
            '-maxrate', '3500k',  # 最大比特率
            '-bufsize', '2000k',  # 缓冲区大小
            '-g', '30',  # GOP大小
            '-f', 'flv',  # 输出格式为FLV
            rtmp_url  # RTMP服务器地址
        ]
        
        # 启动FFmpeg进程
        ffmpeg_process = subprocess.Popen(command, stdin=subprocess.PIPE)

        logger.info(f"开始处理视频: {video_path}")
        logger.info(f"原始分辨率: {original_width}x{original_height}, 原始帧率: {original_fps:.2f}")
        logger.info(f"压缩分辨率: {scaled_width}x{scaled_height}, 目标帧率: {self.target_fps}")
        logger.info(f"跳帧设置: 每{self.skip_frames+1}帧处理1帧")
        logger.info(f"推流地址: {rtmp_url}")

        # 处理每一帧
        while cap.isOpened():
            ret, frame = cap.read()
            if not ret:
                logger.info("视频处理完成")
                break
            
            frame_skip_counter += 1
            if frame_skip_counter <= self.skip_frames:
                # 跳过帧，不处理
                continue
            frame_skip_counter = 0
            
            # 开始计时
            start_time = time.time()
            
            # 压缩帧大小
            compressed_frame = cv2.resize(frame, (scaled_width, scaled_height), interpolation=cv2.INTER_AREA)
            
            # 最终输出帧
            output_frame = compressed_frame.copy()
            
            # 单线程处理
            if not self.use_multithreading:
                result = self._process_frame(compressed_frame, frame_count)
                
                # 使用消防车道处理后的帧作为基础
                output_frame = result['fire_lane_result']['processed_frame']
                
                # 绘制车辆和状态信息
                violation_count = self._draw_vehicles(
                    output_frame, 
                    result['tracked_vehicles'], 
                    result['violating_vehicles'],
                    frame_count
                )
                
                output_frame = self._draw_status_info(
                    output_frame, 
                    violation_count, 
                    time.time() - start_time,
                    frame_count
                )
                
                # 将帧写入FFmpeg管道进行推流
                try:
                    ffmpeg_process.stdin.write(output_frame.tobytes())
                except BrokenPipeError:
                    logger.error("FFmpeg管道已断开，停止推流")
                    break
                except Exception as e:
                    logger.error(f"推流错误: {str(e)}")
                    break
            else:
                # 多线程处理
                try:
                    # 将帧放入处理队列
                    self.frame_queue.put((compressed_frame, frame_count), timeout=1)
                    
                    # 尝试获取处理结果
                    try:
                        processed_frame, result, result_frame_count = self.result_queue.get_nowait()
                        
                        # 使用消防车道处理后的帧作为基础
                        output_frame = result['fire_lane_result']['processed_frame']
                        
                        # 绘制车辆和状态信息
                        violation_count = self._draw_vehicles(
                            output_frame, 
                            result['tracked_vehicles'], 
                            result['violating_vehicles'],
                            result_frame_count
                        )
                        
                        output_frame = self._draw_status_info(
                            output_frame, 
                            violation_count, 
                            time.time() - start_time,
                            result_frame_count
                        )
                        
                        # 将帧写入FFmpeg管道进行推流
                        ffmpeg_process.stdin.write(output_frame.tobytes())
                        self.result_queue.task_done()
                    except queue.Empty:
                        # 没有可用的处理结果，使用原始帧
                        ffmpeg_process.stdin.write(compressed_frame.tobytes())
                except queue.Full:
                    # 队列已满，使用原始帧
                    ffmpeg_process.stdin.write(compressed_frame.tobytes())
            
            frame_count += 1
            
            # 性能监控
            self._monitor_performance(frame_count, start_time)

        # 释放资源
        cap.release()
        
        # 停止多线程处理
        if self.use_multithreading:
            self.frame_queue.put((None, None))  # 发送退出信号
            self.processing_thread.join(timeout=5)
        
        # 关闭FFmpeg进程
        try:
            ffmpeg_process.stdin.close()
            ffmpeg_process.wait()
        except Exception as e:
            logger.error(f"关闭FFmpeg进程时出错: {str(e)}")
        
        logger.info("处理完成！RTMP推流已结束")



    def _t(self, frame, rtmp_url="rtmp://**************:1935/live/stream4"):
            """处理视频流并推送到RTMP服务器"""
            # 打开视频文件
            

            new_height, new_width = frame.shape[:2]
            # 计算压缩后的尺寸 - 使用更大的缩放因子提高清晰度
            # scaled_width = int(original_width * self.scale_factor)
            # scaled_height = int(original_height * self.scale_factor)
            # 计算新的缩放尺寸
            target_height=720
            # 否则按比例缩放
            scale_ratio = target_height / new_height
            scaled_width = int(new_width * scale_ratio)
            scaled_height = target_height
            frame_count = 0
            frame_skip_counter = 0
            start_time = time.time()
            command = [
                'ffmpeg',
                '-y',  # 覆盖输出文件（如果有）
                '-f', 'rawvideo',  # 输入格式为原始视频
                '-vcodec', 'rawvideo',  # 视频编解码器
                '-pix_fmt', 'bgr24',  # OpenCV的像素格式
                '-s', f'{scaled_width}x{scaled_height}',  # 缩放后的视频尺寸
                '-r', str(30),  # 目标帧率
                '-i', '-',  # 从标准输入读取
                '-c:v', 'libx264',  # 输出视频编码器
                '-preset', 'ultrafast',  # 编码速度预设
                '-tune', 'zerolatency',  # 零延迟调优
                '-pix_fmt', 'yuv420p',  # 输出像素格式
                '-b:v', '2500k',  # 增加视频比特率，提高清晰度
                '-maxrate', '3500k',  # 最大比特率
                '-bufsize', '2000k',  # 缓冲区大小
                '-g', '30',  # GOP大小
                '-f', 'flv',  # 输出格式为FLV
                rtmp_url  # RTMP服务器地址
            ]
            
            # 启动FFmpeg进程
            ffmpeg_process = subprocess.Popen(command, stdin=subprocess.PIPE)
                # 压缩帧大小
            compressed_frame = cv2.resize(frame, (scaled_width, scaled_height), interpolation=cv2.INTER_AREA)
            
            # 最终输出帧
            output_frame = compressed_frame.copy()
            
            # 单线程处理
            if not self.use_multithreading:
                result = self._process_frame(compressed_frame, frame_count)
                
                # 使用消防车道处理后的帧作为基础
                output_frame = result['fire_lane_result']['processed_frame']
                
                # 绘制车辆和状态信息
                violation_count = self._draw_vehicles(
                    output_frame, 
                    result['tracked_vehicles'], 
                    result['violating_vehicles'],
                    frame_count
                )
                
                output_frame = self._draw_status_info(
                    output_frame, 
                    violation_count, 
                    time.time() - start_time,
                    frame_count
                )
                
                # 将帧写入FFmpeg管道进行推流
                try:
                    ffmpeg_process.stdin.write(output_frame.tobytes())
                except BrokenPipeError:
                    logger.error("FFmpeg管道已断开，停止推流")
                
                except Exception as e:
                    logger.error(f"推流错误: {str(e)}")
            else:
                # 多线程处理
                try:
                    # 将帧放入处理队列
                    self.frame_queue.put((compressed_frame, frame_count), timeout=1)
                    
                    # 尝试获取处理结果
                    try:
                        processed_frame, result, result_frame_count = self.result_queue.get_nowait()
                        
                        # 使用消防车道处理后的帧作为基础
                        output_frame = result['fire_lane_result']['processed_frame']
                        
                        # 绘制车辆和状态信息
                        violation_count = self._draw_vehicles(
                            output_frame, 
                            result['tracked_vehicles'], 
                            result['violating_vehicles'],
                            result_frame_count
                        )
                        
                        output_frame = self._draw_status_info(
                            output_frame, 
                            violation_count, 
                            time.time() - start_time,
                            result_frame_count
                        )
                        
                        # 将帧写入FFmpeg管道进行推流
                        ffmpeg_process.stdin.write(output_frame.tobytes())
                        self.result_queue.task_done()
                    except queue.Empty:
                        # 没有可用的处理结果，使用原始帧
                        ffmpeg_process.stdin.write(compressed_frame.tobytes())
                except queue.Full:
                    # 队列已满，使用原始帧
                    ffmpeg_process.stdin.write(compressed_frame.tobytes())


if __name__ == "__main__":
    # 模型路径
    fire_lane_model_path = "/home/<USER>/project/yellow-grid/yellow_grid_v3-seg.engine"
    vehicle_model_path = "/home/<USER>/project/yellow-grid/car_type_v5.engine"

    # 视频路径
    video_path = "/home/<USER>/project/yellow-grid/yellowgrid.mp4"
    # video_path = "/home/<USER>/project/yellow-grid/DJI_20250617085549_0001_V.mp4"
    rtmp_url = "rtmp://**************:1935/live/stream4"

    # 配置参数 - 调整字体和分辨率相关参数
    config = {
        'scale_factor': 0.6,           # 增大缩放因子，提高分辨率
        'target_fps': 15,              # 目标推流帧率
        'skip_frames': 0,              # 跳帧设置
        'performance_monitor': True,   # 启用性能监控
        'use_multithreading': True,    # 启用多线程处理
        'max_history_alerts': 5,       # 显示的历史告警数量
        'font_scale': 1,             # 增大字体大小
        'font_thickness': 2,           # 增加字体粗细
    }

    # 创建检测器并处理视频
    detector = FireLaneDetector(fire_lane_model_path, vehicle_model_path, config)
    detector.process_video(video_path, rtmp_url)