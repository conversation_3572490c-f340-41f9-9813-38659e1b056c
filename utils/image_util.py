import cv2 
import numpy as np
from PIL import Image, ImageDraw, ImageFont
# fontStyle = ImageFont.truetype("simhei.ttf", 16, encoding="utf-8")
import time
font = ImageFont.truetype("simsun.ttc", 16)

def cv2AddChineseText(img, text, position, textColor=(255, 255, 255), textSize=30, outlineWidth=2):
    if (isinstance(img, np.ndarray)):  # 判断是否OpenCV图片类型
        img = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
    
    # 创建一个可以在给定图像上绘图的对象
    # draw = ImageDraw.Draw(img)
    # 字体的格式

    
    x, y = position
    # # 先绘制白色描边（在多个方向偏移绘制）
    # for dx in [-outlineWidth, 0, outlineWidth]:
    #     for dy in [-outlineWidth, 0, outlineWidth]:
    #         if dx != 0 or dy != 0:  # 不绘制中心位置
    #             draw.text((x + dx, y + dy), text, (255, 255, 255), font=fontStyle)
    # for dx, dy in [(0, 0), (1, 0), (0, 1), (1, 1)]:
    #    draw.text((position[0] + dx, position[1] + dy), text, textColor, font=fontStyle)
    # 然后在中心位置绘制原始颜色的文字
    start_time = time.time()  # 记录开始时间
    add_text_with_cache(img,text, x, y,textColor,textSize)
    end_time = time.time()
    elapsed_time = end_time - start_time  # 计算耗时（秒）
    
    
    # 转换回OpenCV格式
    return cv2.cvtColor(np.asarray(img), cv2.COLOR_RGB2BGR)
chinese_cache = {}
def get_chinese_pixel(ch,textColor):
    if ch in chinese_cache:
        return chinese_cache[ch]
    else:
        image = Image.new("RGB", (50, 50),textColor)
        draw = ImageDraw.Draw(image)
        draw.text((0, 0), ch, font=font, fill=(0, 0, 0))
        pixel = image.load()[0, 0]
        chinese_cache[ch] = pixel
        return pixel

# 使用缓存添加文字到图片上的代码
def add_text_with_cache(image, text,x,y,textColor,textSize):
    draw = ImageDraw.Draw(image)
    for ch in text:
        pixel = get_chinese_pixel(ch,textColor)
        draw.text((x,y), ch, font=font, fill=pixel)
        x += 16

def ahash(image, hash_size=8):
    """计算图片的平均哈希"""
    resized = cv2.resize(image, (hash_size, hash_size))
    gray = cv2.cvtColor(resized, cv2.COLOR_BGR2GRAY)
    avg = gray.mean()
    return (gray > avg).flatten().astype('uint8')

def are_images_similar_fast(img1, img2):
    """最快的图片相似性判断方法"""
    if img1 is None or img2 is None:
        return False
    
    # 检查图片尺寸是否相同
    if img1.shape != img2.shape:
        return False
    
    # 逐像素比较
    difference = cv2.subtract(img1, img2)
    b, g, r = cv2.split(difference)
    return cv2.countNonZero(b) == 0 and cv2.countNonZero(g) == 0 and cv2.countNonZero(r) == 0