# 车牌匹配服务

## 功能说明

车牌匹配服务提供HTTP接口，使用大模型将传入的车牌列表与白名单车牌列表进行匹配，找出最相似的车牌。

## 主要特性

- 使用大模型进行智能车牌匹配
- 支持自定义白名单或使用默认白名单
- 返回最相似的2个车牌及相似度评分
- 提供详细的匹配原因说明
- RESTful API接口，易于集成

## 文件说明

- `plate_match_service.py` - 主服务文件
- `test_plate_match.py` - 测试脚本
- `/home/<USER>/modelManger_deply/car_plate` - 默认白名单文件

## 启动服务

```bash
cd /home/<USER>/modelManger_deply
python plate_match_service.py
```

服务启动后：
- 服务地址: http://localhost:8001
- API文档: http://localhost:8001/docs
- 健康检查: http://localhost:8001/health

## API接口

### 1. 车牌匹配 `/match_plates`

**POST** `/match_plates`

请求体：
```json
{
  "detected_plates": ["赣AE28Q3", "川ADM8396"],
  "whitelist_plates": ["赣AE28Q2", "川ADM8397", "沪B67890"]
}
```

响应：
```json
{
  "success": true,
  "matched_plates": [
    {
      "detected_plate": "赣AE28Q3",
      "whitelist_plate": "赣AE28Q2",
      "similarity": 0.95,
      "reason": "车牌前缀相同，仅最后一位数字不同"
    },
    {
      "detected_plate": "川ADM8396",
      "whitelist_plate": "川ADM8397",
      "similarity": 0.92,
      "reason": "车牌前缀相同，仅最后一位数字不同"
    }
  ],
  "message": "找到最相似的 2 个车牌匹配"
}
```

### 2. 获取白名单 `/whitelist`

**GET** `/whitelist`

响应：
```json
{
  "success": true,
  "whitelist": ["赣AE28Q2", "川ADM8397", ...],
  "count": 10
}
```

### 3. 健康检查 `/health`

**GET** `/health`

响应：
```json
{
  "status": "healthy",
  "service": "plate_match_service"
}
```

### 4. 测试接口 `/test_match`

**POST** `/test_match`

使用预设数据进行测试匹配。

## 测试

运行测试脚本：
```bash
python test_plate_match.py
```

测试内容包括：
1. 健康检查
2. 获取默认白名单
3. 车牌匹配测试
4. 自定义数据测试

## 配置说明

服务配置在 `plate_match_service.py` 中：

```python
# OpenAI配置
client = OpenAI(
    api_key="gpustack_5e7a262b5803b6bc_16b314c2d6113ca26b285e5190180f24",
    base_url="http://**************:30001/v1"
)

# 服务端口
port=8001
```

## 使用示例

### Python客户端示例

```python
import requests

# 车牌匹配请求
data = {
    "detected_plates": ["赣AE28Q3", "川ADM8396"],
    "whitelist_plates": ["赣AE28Q2", "川ADM8397", "沪B67890"]
}

response = requests.post(
    "http://localhost:8001/match_plates",
    json=data
)

result = response.json()
print(result)
```

### curl示例

```bash
curl -X POST "http://localhost:8001/match_plates" \
     -H "Content-Type: application/json" \
     -d '{
       "detected_plates": ["赣AE28Q3"],
       "whitelist_plates": ["赣AE28Q2", "赣AE28Q4"]
     }'
```

## 注意事项

1. 确保大模型服务可用
2. 白名单文件路径正确
3. 服务端口8001未被占用
4. 网络连接正常

## 错误处理

服务包含完整的错误处理：
- 输入验证
- 大模型调用异常
- JSON解析错误
- 文件读取错误

所有错误都会返回详细的错误信息。
