import av
import numpy as np
import time
from av.codec.hwaccel import HWAccel, hwdevices_available

class AVVideoCapture:
    def __init__(self, source, target_format='bgr24', full_range=False):
        """
        初始化视频捕获对象
        
        参数:
            source: 视频文件路径或流媒体URL
            target_format: 目标像素格式 ('rgb24', 'bgr24'等)
            full_range: 是否强制转换为全范围(0-255)
        """
        self.container = None
        self.video_stream = None
        self.current_frame = None
        self.grabbed = False
        self.target_format = target_format
        self.full_range = full_range
        print(hwdevices_available())
        hwaccel = HWAccel(device_type='cuda', allow_software_fallback=False)
        try:
            self.container = av.open(source,timeout=10,hwaccel=hwaccel, buffer_size=(1024*1024)*5)
            self.container.streams.video[0].thread_type = "AUTO"
        
            self.video_stream = next(s for s in self.container.streams if s.type == 'video')

            # 在初始化视频流后添加

    
            self.decoder = self.container.decode(self.video_stream)
            self.grabbed = True
            # 打印视频信息用于调试
            print(f"Video stream format: {self.video_stream.format}")
            print(f"Video stream color range: {self.video_stream.color_range}")
        except Exception as e:
            print(f"Error opening video source: {e}")
            self.release()
    
    def isOpened(self):
        """检查视频源是否成功打开"""
        return self.container is not None and self.video_stream is not None
    
        
    def read(self):
        if not self.isOpened():
            return False, None
        
        try:
            start_decode = time.time()
            frame = next(self.decoder)
            decode_time = time.time() - start_decode
            
            start_convert = time.time()
            img = frame.to_ndarray(format=self.target_format)
            
            convert_time = time.time() - start_convert
            
            # 处理颜色范围
            range_time = 0
            if self.full_range and frame.format.name.startswith('yuv'):
                start_range = time.time()
                if img.min() >= 16 and img.max() <= 235:
                    img = ((img.astype(np.float32) - 16) * (255.0 / (235 - 16)))
                    img = np.clip(img, 0, 255).astype(np.uint8)
                range_time = time.time() - start_range
            
            # print(f"Timing - Decode: {decode_time:.3f}s, Convert: {convert_time:.3f}s, Range: {range_time:.3f}s")
            
            self.current_frame = img
            self.grabbed = True
            return True, self.current_frame
        except (StopIteration, av.error.EOFError):
            self.grabbed = False
            return False, None
    def grab(self):
        """
        获取下一帧但不解码
        对于这个简单实现，我们直接调用read()
        """
        return self.read()[0]
    
    def retrieve(self):
        """
        解码并返回grab()捕获的帧
        对于这个简单实现，我们直接返回当前帧
        """
        return self.grabbed, self.current_frame
    
    def get(self, prop_id):
        """
        获取视频属性
        
        参数:
            prop_id: 属性ID (可以使用OpenCV的cv2.CAP_PROP_*常量)
        
        返回:
            属性值或-1(如果属性不可用)
        """
        if not self.isOpened():
            return -1
        
        try:
            if prop_id == 3:  # cv2.CAP_PROP_FRAME_WIDTH
                return self.video_stream.width
            elif prop_id == 4:  # cv2.CAP_PROP_FRAME_HEIGHT
                return self.video_stream.height
            elif prop_id == 5:  # cv2.CAP_PROP_FPS
                return float(self.video_stream.average_rate)
            elif prop_id == 7:  # cv2.CAP_PROP_FRAME_COUNT
                return self.video_stream.frames
            elif prop_id == 21:  # cv2.CAP_PROP_CODEC_PIXEL_FORMAT
                return self.video_stream.format
            else:
                return -1
        except:
            return -1
    
    def release(self):
        """释放视频资源"""
        if self.container is not None:
            try:
                self.container.close()
            except:
                pass
        self.container = None
        self.video_stream = None
        self.current_frame = None
        self.grabbed = False
    
    def __del__(self):
        self.release()