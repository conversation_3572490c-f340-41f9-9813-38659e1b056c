from typing import Union, Any, Dict, List, Tuple

import cv2
from PIL import Image
import numpy as np
from pathlib import Path

from sklearn.cluster import DBSCAN

from predict_method.base_predict import BasePredictor
from utils.image_util import cv2AddChineseText

from runtime import EngineModelRuntime
from utils.image_util import cv2AddChineseText


class PersonCrowdEngine_2(BasePredictor):
    def _initialize_from_config(self):
        """从配置文件初始化目标检测模型和相关参数"""
        # 初始化模型参数
        self.confidence_threshold = self.config.get('confidence_threshold', 0.5)
        self.predict_params = self.config.get('predict_params', {})
        self.input_size = tuple(self.config['input_size'])  # 期望的输入尺寸 (height, width)
        self.classes = self.config['classes']  # 类别名称列表
        self.person_model = EngineModelRuntime(self._get_model_file().read(), input_size=self.input_size,
                                          predict_params=self.predict_params)
        print(f"Initialized detector with: {self.config}")
        self.min_samples = 5 #聚类最小样本数
        self.base_h = 30 #基准框高
        self.base_eps = 100 #基准eps
        self.frame_count=0
        self.is_crowd=False
        self.is_crowd_count=0

    def preprocess(self, image_input: Union[str, Image.Image, np.ndarray]) -> np.ndarray:
        """预处理图像数据以适应模型输入要求"""
        # 如果输入是文件路径，加载图像
        if isinstance(image_input, str):
            image = cv2.imread(image_input)
        elif isinstance(image_input, Path):
            image = cv2.imread(str(image_input))
        elif isinstance(image_input, Image.Image):
            image = np.array(image_input)

        else:
            image = image_input
        # self.img_height, self.img_width = image.shape[:2]
        #         # 将图像颜色空间从 BGR 转换为 RGB
        # img_rgb = cv.cvtColor(image, cv.COLOR_BGR2RGB)

        # # 保持宽高比，进行 letterbox 填充, 使用模型要求的输入尺寸
        # img, self.ratio, (self.dw, self.dh) = self.letterbox(img_rgb, new_shape=(self.session.input_width, self.session.input_height))
        return image

    def callback(self, task, processed_frame, predictions, timestamp,original_frame):
        if len(predictions) == 0:
            return
        import time
        if not hasattr(self, '_last_call_time'):
            self._last_call_time = 0

        current_time = time.time()  # 获取当前系统时间（秒）
        if current_time - self._last_call_time >= 3 and self.is_crowd:
            s=self.call_ai_hook_interface(task.callback_url, processed_frame, timestamp, task.latitude, task.longitude)
            self._last_call_time = current_time
            self.is_crowd = False
            # print(f'回调成功:{s}')

    def predict(self, image_array: np.ndarray) -> np.ndarray:
        """执行目标检测预测"""
        # return [{}]
        return self.person_model.predict(image_array)

    def visualize(self, frame: np.ndarray, out) -> np.ndarray:
        """执行目标检测预测"""
        self.is_crowd_count=0
        centers=[]
        box_heights=[]
        box_widths=[]
        output_frame = frame.copy()
        # print(out)
        # 自定义绘制每个检测框

        for i, box in enumerate(out):
            x1, y1, x2, y2 = map(int, box['bbox'])
            center_x = (x1 + x2) / 2
            center_y = (y1 + y2) / 2
            box_height = y2 - y1
            box_width = x2 - x1
            centers.append((center_x, center_y))
            box_heights.append(box_height)
            box_widths.append(box_width)
            # 框颜色和样式

        avg_height_global = np.mean(box_heights) if box_heights else 50

        # 转换为NumPy数组
        points = np.array(centers)
        if len(points) == 0:
            return output_frame  # 直接返回原图，不执行聚类

        # 确保points是二维数组 (n, 2)
        if points.ndim == 1:
            points = points.reshape(-1, 2)

        # 动态调整DBSCAN参数（基于全局框高）
        # base_eps = 100  # 基准eps
        # base_h = 30  # 基准框高
        eps = self.base_eps * (avg_height_global / self.base_h)  # 动态eps
        # min_samples = 5  # 计数阈值：单位：人
        avg_width=np.mean(box_widths) if box_widths else 0
        # 全局DBSCAN聚类
        dbscan = DBSCAN(eps=eps, min_samples=self.min_samples, metric='euclidean')
        labels = dbscan.fit_predict(points)

        # 分离聚类点和噪声点
        cluster_points = points[labels != -1]
        noise_points = points[labels == -1]
        unique_labels = set(labels) - {-1}

        # 为每个聚类分配颜色
        # color = (255, 42, 4)  # 蓝色
        # cluster_colors = {}
        # for label, c in zip(unique_labels, colors):
        #     bgr_color = (int(c[2] * 255), int(c[1] * 255), int(c[0] * 255))
        #     cluster_colors[label] = bgr_color

        # 可视化聚类结果
        for label in unique_labels:
            cluster_pts = points[labels == label].astype(np.float32)

            # 计算最小包围圆
            if len(cluster_pts) >= self.min_samples:  # minEnclosingCircle需要至少5个点
                self.is_crowd_count+=1
                center, radius = cv2.minEnclosingCircle(cluster_pts)
                center = (int(center[0]), int(center[1]))
                radius = int(radius)

                # 绘制包围圆和中心点
                # color = cluster_colors[label]
                cv2.circle(output_frame, center, int(radius+avg_width/2), (94, 53, 255), 1)
                cv2.circle(output_frame, center, 3, (94, 53, 255), -1)
                text=f'人员聚集:{len(cluster_pts)}人'
                # 添加聚类标签
                # (text_width, text_height), _ = cv2.getTextSize(str(text), cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)
                # cv2.rectangle(output_frame, ((center[0]-radius-10), center[1] - radius - text_height-10), (center[0]-radius + text_width-10, center[1]-radius), (94, 53, 255), -1)

                output_frame=cv2AddChineseText(output_frame, str(text),
                            (center[0] - 50, center[1] - radius - 20),
                            (255, 53, 94), 16 )

                # output_frame = cv2AddChineseText(output_frame, str(box['class_name']), (x1 + 8, y1 - text_height - 8),
                #                           (255, 255, 255), 16)
        # 绘制噪声点（黄色）
        for (x, y) in noise_points:
            cv2.circle(output_frame, (int(x), int(y)), 3, (0, 255, 255), -1)

        # cv2.imwrite(r'1.png', display_img)
        self.frame_count+=1
        if self.is_crowd_count !=0:
            self.is_crowd=True
        elif self.is_crowd_count==0:
            self.is_crowd=False


        # print(f'人员聚集已处理:{self.frame_count}')
        # cv2.imwrite(f'E:/A_worker/W_XM/ZLT_api_test/modelManger_deply/test_video/test{self.frame_count}.png', output_frame)
        return output_frame

            # thickness = 2

            # 绘制边界框
            # cv.rectangle(output_frame, (x1, y1), (x2, y2), color, thickness)
            #
            # # 置信度和类别标签
            # # label = f"{results[0].names[int(cls[i])]} {conf[i]:.2f}"
            #
            # # 计算文本背景
            # (text_width, text_height), _ = cv.getTextSize(str(box['class_name']), cv.FONT_HERSHEY_SIMPLEX, 0.5, 1)
            # cv.rectangle(output_frame, (x1, y1 - text_height - 10), (x1 + text_width, y1), color, -1)
            # output_frame = cv2AddChineseText(output_frame, str(box['class_name']), (x1 + 8, y1 - text_height - 8),
            #                                  (255, 255, 255), 16)
        # return output_frame

    def postprocess(self, raw_prediction: np.ndarray) -> List[Dict[str, Any]]:
        """后处理预测结果，返回易读的检测结果"""
        detections = []
        for result in raw_prediction:
            for box in result.boxes:
                # 获取坐标 (x1, y1, x2, y2)
                x1, y1, x2, y2 = box.xyxy[0].tolist()

                # 获取置信度和类别ID
                conf = box.conf.item()
                cls_id = box.cls.item()
                # 构建检测结果字典
                detection = {
                    'bbox': [float(x1), float(y1), float(x2), float(y2)],
                    'conf': conf,
                    'class_id': int(cls_id),
                    'class_name': self.classes[int(cls_id)]  # 使用 model.names 获取类别名称
                }
                detections.append(detection)
        return detections


if __name__ == "__main__":
    model_path = "/home/<USER>/project/carTrack/people_dr/train7/weights/best.onnx"
    obj = ObjectDetector('/home/<USER>/project/modelManger/models/poeple/config.yaml')
    print(obj.predict('/home/<USER>/project/carTrack/people_v4/images/Train/frame_000030.png'))