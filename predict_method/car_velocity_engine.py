import cv2
import numpy as np
import logging
from typing import Union, Any, Dict, List
from runtime import EngineModelRuntime
from PIL import Image
from pathlib import Path
import time
from predict_method.base_predict import BasePredictor
from utils.image_util import cv2AddChineseText

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class CarVelocityEngine(BasePredictor):

    def _initialize_from_config(self):
        """从配置文件初始化目标检测模型和相关参数"""
        # 初始化模型参数
        self.vehicle_model = EngineModelRuntime(
            self._get_file_by_key("model_filename"),
            predict_params=self.config.get("vehicle_predict_params", {}),
            num_workers=1
        )
        # 配置参数 - 调整字体和分辨率相关参数
        config = {
            "target_fps": 15,  # 目标推流帧率
            "max_history_alerts": 5,  # 显示的历史告警数量
            "font_scale": 1,  # 增大字体大小
            "font_thickness": 2,  # 增加字体粗细
        }
        self.monitor = VehicleSpeedEstimator(
            self.vehicle_model, config
        )
        self.frame_skip_counter = 0
        self.frame_count = 0
        self.skip_frames = 1  # 跳帧设置
        self.status = False
        self.start_time = time.time()
        print(f"Initialized detector with: {self.config}")

    def preprocess(
        self, image_input: Union[str, Image.Image, np.ndarray]
    ) -> np.ndarray:
        """预处理图像数据以适应模型输入要求"""
        # 如果输入是文件路径，加载图像
        if isinstance(image_input, str):
            image = cv2.imread(image_input)
        elif isinstance(image_input, Path):
            image = cv2.imread(str(image_input))
        elif isinstance(image_input, Image.Image):
            image = np.array(image_input)
        else:
            image = image_input
        return image

    def callback(self, task, processed_frame, predictions, timestamp, original_frame):
        if not self.status:
            return
        import time
        if not hasattr(self, '_last_call_time'):
            self._last_call_time = 0

        current_time = time.time()  # 获取当前系统时间（秒）
        if current_time - self._last_call_time >= 5:
            logger.info(self._last_call_time)
            print("================================")
            self.call_ai_hook_interface(task.callback_url, processed_frame, timestamp, task.latitude, task.longitude)
            self._last_call_time = current_time

    def predict(self, image_array: np.ndarray) -> np.ndarray:
        """执行目标检测预测"""
        # self.frame_skip_counter += 1
        # if self.frame_skip_counter <= self.skip_frames:
        # 跳过帧，不处理
        # return image_array
        # self.frame_skip_counter = 0
        frame, self.status = self.monitor.yolo_inference(image_array, self.start_time)
        self.frame_count += 1
        return frame

    def visualize(self, frame: np.ndarray, out) -> np.ndarray:
        return out

    def postprocess(self, raw_prediction: np.ndarray) -> List[Dict[str, Any]]:
        """后处理预测结果，返回易读的检测结果"""
        return raw_prediction


class VehicleSpeedEstimator:
    def __init__(self, car_model, config):
        self.car_model = car_model
        self.vehicle_tracks = {}  # 存储车辆跟踪数据
        self.vehicle_speeds = {}  # 存储车辆速度数据
        self.pixels_per_meter = 25  # 每米转换为像素值
        self.window_size = 10  # 计算平均速度的的窗口大小
        self.current_time = 0.0  # 当前时间，读取帧计算
        # 应用配置
        self.config = config or {}
        self._apply_config()
        self.violation = False
        # 类别名称映射
        self.color = (125, 125, 125)
        self.class_names = {
            0: '自行车', 1: '车', 2: '面包车', 3: '卡车',
            4: '三轮车', 5: '带蓬三轮车', 6: '公交车', 7: '摩托车'
        }


    def _apply_config(self):
        """应用配置参数"""
        # 视频处理参数
        self.target_fps = self.config.get("target_fps", 30)
        self.skip_frames = self.config.get("skip_frames", 1)

        # 性能监控
        self.performance_monitor = self.config.get("performance_monitor", False)
        self.monitor_interval = self.config.get("monitor_interval", 5)  # 秒

        # 告警配置
        self.alert_interval = self.config.get("alert_interval", 5)  # 秒
        self.max_history_alerts = self.config.get("max_history_alerts", 3)
        self.max_speed = self.config.get("max_speed", 45)

        # 多线程处理
        self.use_multithreading = self.config.get("use_multithreading", False)
        self.queue_size = self.config.get("queue_size", 20)

        # 文字显示配置
        self.font_scale = self.config.get("font_scale", 0.7)  # 字体大小
        self.font_thickness = self.config.get("font_thickness", 2)  # 字体粗细
        self.line_thickness = self.config.get("line_thickness", 2)  # 线条粗细


    def yolo_inference(self, frame, start_time):
        """
        Returns:图像或视频检测结果
        """
        # 获取视频属性
        if frame is None:
            print(f"无法读取图像: {frame}")
            return None

        self.violation = False
        self.current_time = time.time() - start_time

        results = self.car_model.track(frame)

        # plot = results[0].plot() # 绘制原模型跟踪结果

        # 提取检测结果
        if results[0].boxes.id is not None:
            boxes = results[0].boxes.xyxy.cpu().numpy()
            track_ids = results[0].boxes.id.cpu().numpy().astype(int)
            class_ids = results[0].boxes.cls.cpu().numpy().astype(int)

            # 处理每个检测到的车辆
            for i, box in enumerate(boxes):
                class_id = class_ids[i]
                track_id = track_ids[i]

                x1, y1, x2, y2 = box
                center_x = (x1 + x2) / 2
                center_y = (y1 + y2) / 2

                # 初始化轨迹记录
                if track_id not in self.vehicle_tracks:
                    self.vehicle_tracks[track_id] = []
                    self.vehicle_speeds[track_id] = []

                # 添加当前点
                self.vehicle_tracks[track_id].append((self.current_time, center_x, center_y))

                if len(self.vehicle_tracks[track_id]) > self.window_size:
                    self.vehicle_tracks[track_id].pop(0)

                # 计算速度（至少需要2个点）
                if len(self.vehicle_tracks[track_id]) >= 5:
                    # 准备回归数据
                    times = np.array([t[0] for t in self.vehicle_tracks[track_id]])
                    x_pos = np.array([t[1] for t in self.vehicle_tracks[track_id]])
                    y_pos = np.array([t[2] for t in self.vehicle_tracks[track_id]])

                    # 执行线性回归 (x = a + b*t)
                    A = np.vstack([times, np.ones(len(times))]).T
                    bx, _, _, _ = np.linalg.lstsq(A, x_pos, rcond=None)
                    by, _, _, _ = np.linalg.lstsq(A, y_pos, rcond=None)

                    # 计算速度分量（像素/秒）
                    vx_pixels = bx[0]
                    vy_pixels = by[0]

                    # 转换为米/秒
                    vx_mps = vx_pixels / self.pixels_per_meter
                    vy_mps = vy_pixels / self.pixels_per_meter

                    # 计算合成速度
                    speed_mps = np.sqrt(vx_mps ** 2 + vy_mps ** 2)
                    speed_kmph = speed_mps * 3.6  # 转换为km/h

                    # 存储当前速度
                    self.vehicle_speeds[track_id].append(speed_kmph)

                    # 计算平均速度（用于显示）
                    avg_speed = np.mean(self.vehicle_speeds[track_id][-5:]) if len(
                        self.vehicle_speeds[track_id]) > 0 else 0

                    if avg_speed >= self.max_speed:
                        self.color = (94, 53, 255)
                        self.violation = True
                    else:
                        self.color = (125, 125, 125)

                    # 在帧上绘制结果
                    class_name = self.class_names[class_id]
                    cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), self.color, self.line_thickness)
                    text = f"{class_name}[{track_id}] : {avg_speed:.1f} km/h"
                    text_width = len(text) * 16
                    cv2.rectangle(frame, (int(x1), int(y1) - 22),
                                  (int(x1) + text_width, int(y1)), self.color, -1)
                    frame = cv2AddChineseText(frame, text, (int(x1) + 2, int(y1) - 20), (255, 255, 255),
                                             textSize=20)

                    # cv2.putText(frame, f"{class_name}[{track_id}] : {avg_speed:.1f} km/h", (int(x1), int(y1) - 10),
                    #             cv2.FONT_HERSHEY_SIMPLEX, self.font_scale, (94, 53, 255), self.font_thickness)

                    # 绘制轨迹
                    for j in range(1, len(self.vehicle_tracks[track_id])):
                        prev = self.vehicle_tracks[track_id][j - 1]
                        curr = self.vehicle_tracks[track_id][j]
                        cv2.line(frame,
                                 (int(prev[1]), int(prev[2])),
                                 (int(curr[1]), int(curr[2])),
                                 self.color, self.line_thickness)

        return frame , self.violation
