
from typing import Union, Any, Dict, List, Tuple
from PIL import Image
import numpy as np
from pathlib import Path

from predict_method.base_predict import BasePredictor
from utils.image_util import cv2AddChineseText

from runtime import OnnxModelRuntime
import cv2 as cv
class ObjectDetector(BasePredictor):
    def _initialize_from_config(self):
        """从配置文件初始化目标检测模型和相关参数"""
        # 初始化模型参数
        self.confidence_threshold = self.config.get('confidence_threshold', 0.5)
        self.iou_threshold = self.config.get('iou_threshold', 0.4)
        self.input_size = tuple(self.config['input_size'])  # 期望的输入尺寸 (height, width)
        self.classes = self.config['classes']  # 类别名称列表
        self.session = OnnxModelRuntime(self._get_model_file().read())
        print(f"Initialized detector with: {self.config}")

    def preprocess(self, image_input: Union[str, Image.Image, np.ndarray]) -> np.ndarray:
        """预处理图像数据以适应模型输入要求"""
        # 如果输入是文件路径，加载图像
        if isinstance(image_input, str):
            image = cv.imread(image_input)
        elif isinstance(image_input, Path):
            image = cv.imread(str(image_input))
        elif isinstance(image_input, Image.Image):
            image =  np.array(image_input)  
           
        else:
            image = image_input
        self.img_height, self.img_width = image.shape[:2]
                # 将图像颜色空间从 BGR 转换为 RGB
        img_rgb = cv.cvtColor(image, cv.COLOR_BGR2RGB)
 
        # 保持宽高比，进行 letterbox 填充, 使用模型要求的输入尺寸
        img, self.ratio, (self.dw, self.dh) = self.letterbox(img_rgb, new_shape=(self.session.input_width, self.session.input_height))
        return img
    

    def letterbox(self, img, new_shape=(640, 640), color=(114, 114, 114), auto=False, scaleFill=False, scaleup=True):
        """
        将图像进行 letterbox 填充，保持纵横比不变，并缩放到指定尺寸。
        """
        shape = img.shape[:2]  # 当前图像的宽高
        print(f"Original image shape: {shape}")
 
        if isinstance(new_shape, int):
            new_shape = (new_shape, new_shape)
 
        # 计算缩放比例
        r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])  # 选择宽高中最小的缩放比
        if not scaleup:  # 仅缩小，不放大
            r = min(r, 1.0)
 
        # 缩放后的未填充尺寸
        new_unpad = (int(round(shape[1] * r)), int(round(shape[0] * r)))
 
        # 计算需要的填充
        dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]  # 计算填充的尺寸
        dw /= 2  # padding 均分
        dh /= 2
 
        # 缩放图像
        if shape[::-1] != new_unpad:  # 如果当前图像尺寸不等于 new_unpad，则缩放
            img = cv.resize(img, new_unpad, interpolation=cv.INTER_LINEAR)
 
        # 为图像添加边框以达到目标尺寸
        top, bottom = int(round(dh)), int(round(dh))
        left, right = int(round(dw)), int(round(dw))
        img = cv.copyMakeBorder(img, top, bottom, left, right, cv.BORDER_CONSTANT, value=color)
        print(f"Final letterboxed image shape: {img.shape}")
 
        return img, (r, r), (dw, dh)


    def predict(self, image_array: np.ndarray) -> np.ndarray:
        """执行目标检测预测"""
        #return [{}]
        return self.session.predict(image_array)

    def visualize(self, frame: np.ndarray,out) -> np.ndarray:
        
        """执行目标检测预测"""
        output_frame = frame.copy()
        # 自定义绘制每个检测框
        for i, box in enumerate(out):
            x1, y1, w, h = box['bbox']
        # 获取类别对应的颜色
            color = (0, 0, 255)  # 绿色
            # 在图像上绘制边界框
            cv.rectangle(output_frame, (int(x1), int(y1)), (int(x1 + w), int(y1 + h)), color, 2)
            # output_frame=cv2AddChineseText(output_frame,str(box['class_name']),(x1+8, y1-text_height-8),(255, 255, 255),16)
        return output_frame



    def postprocess(self, raw_prediction: np.ndarray) -> List[Dict[str, Any]]:
        """后处理预测结果，返回易读的检测结果"""
        detections = []
        outputs = np.transpose(np.squeeze(raw_prediction[0]))
        rows = outputs.shape[0]
        boxes, scores, class_ids = [], [], []
        # 计算缩放比例和填充
        for i in range(rows):
            classes_scores = outputs[i][4:]
            max_score = np.amax(classes_scores)
            if max_score >= self.confidence_threshold:
                class_id = np.argmax(classes_scores)
                x, y, w, h = outputs[i][0], outputs[i][1], outputs[i][2], outputs[i][3]
                # 将框调整到原始图像尺寸，考虑缩放和填充
                x -= self.dw  # 移除填充
                y -= self.dh
                x /= self.ratio[0]  # 缩放回原图
                y /= self.ratio[1]
                w /= self.ratio[0]
                h /= self.ratio[1]
                left = int(x - w / 2)
                top = int(y - h / 2)
                width = int(w)
                height = int(h)
 
                boxes.append([left, top, width, height])
                scores.append(max_score)
                class_ids.append(class_id)
 
        indices = cv.dnn.NMSBoxes(boxes, scores, self.confidence_threshold, self.iou_threshold)
        # print(1)
        for i in indices:
            box = boxes[i]
            x1, y1, x2, y2 = box
            score = scores[i]
            class_id = class_ids[i]
            detections.append({
                'bbox': [float(x1), float(y1), float(x2), float(y2)],  # 确保坐标是浮点数
                'conf': score,         # 检测置信度
                'class_id': int(class_id),    # 类别ID转换为整数
                'class_name': self.classes[int(class_id)]  # 类别名称
            })
        return detections



if __name__ == "__main__":
    model_path = "/home/<USER>/project/carTrack/people_dr/train7/weights/best.onnx"
    obj=ObjectDetector('/home/<USER>/project/modelManger/models/poeple/config.yaml')
    print(obj.predict('/home/<USER>/project/carTrack/people_v4/images/Train/frame_000030.png'))