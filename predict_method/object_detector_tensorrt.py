
from typing import Union, Any, Dict, List, Tuple
from PIL import Image
import numpy as np
from pathlib import Path

from predict_method.base_predict import BasePredictor
from utils.image_util import cv2AddChineseText

from runtime import TensorRTModelRunTime
import cv2 as cv
class ObjectDetectorTensorrt(BasePredictor):
    def _initialize_from_config(self):
        """从配置文件初始化目标检测模型和相关参数"""
        # 初始化模型参数
        self.confidence_thres = self.config.get('confidence_threshold', 0.5)
        self.iou_thres = self.config.get('iou_threshold', 0.4)
        self.input_size = tuple(self.config['input_size'])  # 期望的输入尺寸 (height, width)
        self.classes = self.config['classes']  # 类别名称列表
        #self.session = 
        print(f"Initialized detector with: {self.config}")


    
    def preprocess(self, image_path):
        """
        优化后的预处理方法，整合了图像加载、letterbox处理和归一化
        参数：
            image_path: 图像路径或numpy数组
            target_shape: 目标形状 (batch, channel, height, width)
        返回：
            np.ndarray: 预处理后的图像数据
        """
        # 1. 图像加载优化
        if isinstance(image_path, (str, Path)):
            # 使用cv2.imread直接读取，避免中间转换
            img = cv.imread(str(image_path))
            if img is None:
                raise ValueError(f"无法加载图像: {image_path}")
        elif isinstance(image_path, Image.Image):
            # PIL.Image转numpy优化
            img = np.array(image_path)[..., ::-1]  # RGB转BGR
        else:
            img = image_path
        
        # 记录原始尺寸
        self.img_height, self.img_width = img.shape[:2]
        
        # 2. letterbox处理优化
        # 使用更高效的letterbox实现
        img, self.ratio, (self.dw, self.dh) = self.optimized_letterbox(
            img
        )
        # 3. 颜色空间转换和归一化优化
        # 使用更高效的转换方式
        img = img.astype(np.float32) / 255.0  # 归一化
        img = np.transpose(img, (2, 0, 1))    # HWC转CHW
        img = np.expand_dims(img, axis=0)     # 添加batch维度
        return img

    def optimized_letterbox(self, img, new_shape=(640, 640), color=(114, 114, 114)):
        """
        优化的letterbox实现，减少不必要的计算和内存分配
        """
        # 获取原始尺寸
        h, w = img.shape[:2]
        self.input_width, self.input_height = new_shape
        
        # 计算缩放比例
        scale = min(new_shape[0] / h, new_shape[1] / w)
        
        # 计算新尺寸
        new_unpad = (int(round(w * scale)), int(round(h * scale)))
        
        # 直接使用cv2.resize的dsize参数，避免额外计算
        if (w, h) != new_unpad:
            img = cv.resize(img, new_unpad, interpolation=cv.INTER_LINEAR)
        
        # 计算填充
        dw = new_shape[1] - new_unpad[0]
        dh = new_shape[0] - new_unpad[1]
        dw /= 2
        dh /= 2
        
        # 使用更高效的填充方式
        top, bottom = int(round(dh)), int(round(dh))
        left, right = int(round(dw)), int(round(dw))
        
        # 使用cv2.copyMakeBorder的常量填充
        img = cv.copyMakeBorder(img, top, bottom, left, right, 
                            cv.BORDER_CONSTANT, value=color)
        
        return img, (scale, scale), (dw, dh)

    def postprocess(self, output):
        """
        优化后的后处理方法，使用向量化操作加速处理
        参数：
            output (numpy.ndarray): 模型的输出
        返回：
            list: 包含检测结果的字典列表
        """
        # 转置并压缩输出，以匹配预期形状
        outputs = np.transpose(np.squeeze(output[0]))
        
        # 向量化操作提取所有框和分数
        boxes_xywh = outputs[:, :4]
        classes_scores = outputs[:, 4:]
        
        # 向量化计算最大分数和类别ID
        max_scores = np.amax(classes_scores, axis=1)
        class_ids = np.argmax(classes_scores, axis=1)
        
        # 提前过滤低置信度结果
        mask = max_scores >= self.confidence_thres
        boxes_xywh = boxes_xywh[mask]
        max_scores = max_scores[mask]
        class_ids = class_ids[mask]
        
        if len(boxes_xywh) == 0:
            return []
        
        # 向量化调整框到原始图像尺寸
        boxes_xywh[:, 0] = (boxes_xywh[:, 0] - self.dw) / self.ratio[0]  # x
        boxes_xywh[:, 1] = (boxes_xywh[:, 1] - self.dh) / self.ratio[1]  # y
        boxes_xywh[:, 2] = boxes_xywh[:, 2] / self.ratio[0]             # w
        boxes_xywh[:, 3] = boxes_xywh[:, 3] / self.ratio[1]             # h
        
        # 转换为(left, top, width, height)格式
        boxes = np.empty_like(boxes_xywh)
        boxes[:, 0] = boxes_xywh[:, 0] - boxes_xywh[:, 2] / 2  # left
        boxes[:, 1] = boxes_xywh[:, 1] - boxes_xywh[:, 3] / 2  # top
        boxes[:, 2] = boxes_xywh[:, 2]                         # width
        boxes[:, 3] = boxes_xywh[:, 3]                         # height
        boxes = boxes.astype(np.int32)
        
        # 执行NMS - 使用OpenCV的NMSBoxes
        indices = cv.dnn.NMSBoxes(boxes.tolist(), max_scores.tolist(), 
                                self.confidence_thres, self.iou_thres)
        # 构建结果列表
        if isinstance(indices, np.ndarray):
            indices = indices.flatten()
            detections = [{
                'box': boxes[i].tolist(),
                'score': float(max_scores[i]),
                'class_id': int(class_ids[i])
            } for i in indices]
        else:
            detections = []
        return detections
 

    def predict(self, image_array: np.ndarray) -> np.ndarray:
        """执行目标检测预测"""
        #return [{}]
        return TensorRTModelRunTime(self._get_model_file().read()).predict(image_array)

    def visualize(self, frame: np.ndarray,out) -> np.ndarray:
        """执行目标检测预测"""
        output_frame = frame.copy()
        # 自定义绘制每个检测框
        for i, box in enumerate(out):
            x1, y1, x2, y2 = map(int, box['bbox'])
            
            # 框颜色和样式
            color = (0, 0, 255)  # 绿色
            thickness = 2
            
            # 绘制边界框
            cv.rectangle(output_frame, (x1, y1), (x2, y2), color, thickness)
            
            # 置信度和类别标签
            #label = f"{results[0].names[int(cls[i])]} {conf[i]:.2f}"
            
            # 计算文本背景
            (text_width, text_height), _ = cv.getTextSize(str(box['class_name']), cv.FONT_HERSHEY_SIMPLEX, 0.5, 1)
            cv.rectangle(output_frame, (x1, y1 - text_height - 10), (x1 + text_width, y1), color, -1)
            output_frame=cv2AddChineseText(output_frame,str(box['class_name']),(x1+8, y1-text_height-8),(255, 255, 255),16)
        return output_frame




