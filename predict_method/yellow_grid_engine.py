from turtle import width
from runtime import EngineModelRuntime, YoloModelRuntime
import cv2
import numpy as np
from datetime import datetime
from collections import OrderedDict, deque
from predict_method.base_predict import BasePredictor
from typing import Union, Any, Dict, List, Tuple
from PIL import Image
from pathlib import Path
import logging
import time
from utils.image_util import cv2AddChineseText

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class YellowGridEngine(BasePredictor):

    def _initialize_from_config(self):
        """从配置文件初始化目标检测模型和相关参数"""
        # 初始化模型参数
        self.fire_lane_model = EngineModelRuntime(
            self._get_file_by_key("model_filename"),
            predict_params=self.config.get("fire_lane_predict_params"),
            num_workers=1,
            task="segment",
        )
        self.vehicle_model = EngineModelRuntime(
            self._get_file_by_key("car_model"),
            predict_params=self.config.get("vehicle_predict_params"),
        )
        # 配置参数 - 调整字体和分辨率相关参数
        config = {
            "target_fps": 15,  # 目标推流帧率
            "max_history_alerts": 5,  # 显示的历史告警数量
            "font_scale": 1,  # 增大字体大小
            "font_thickness": 2,  # 增加字体粗细
        }
        self.monitor = FireLaneDetector(
            self.fire_lane_model, self.vehicle_model, config
        )
        self.frame_skip_counter = 0
        self.frame_count = 0
        self.skip_frames = 1  # 跳帧设置
        print(f"Initialized detector with: {self.config}")

    def preprocess(
        self, image_input: Union[str, Image.Image, np.ndarray]
    ) -> np.ndarray:
        """预处理图像数据以适应模型输入要求"""
        # 如果输入是文件路径，加载图像
        if isinstance(image_input, str):
            image = cv2.imread(image_input)
        elif isinstance(image_input, Path):
            image = cv2.imread(str(image_input))
        elif isinstance(image_input, Image.Image):
            image = np.array(image_input)
        else:
            image = image_input
        return image

    def predict(self, image_array: np.ndarray) -> np.ndarray:
        """执行目标检测预测"""
        # self.frame_skip_counter += 1
        # if self.frame_skip_counter <= self.skip_frames:
        # 跳过帧，不处理
        # return image_array
        self.frame_skip_counter = 0
        frame = self.monitor.process_video(image_array, self.frame_count)
        self.frame_count += 1
        return frame

    def visualize(self, frame: np.ndarray, out) -> np.ndarray:
        return out

    def postprocess(self, raw_prediction: np.ndarray) -> List[Dict[str, Any]]:
        """后处理预测结果，返回易读的检测结果"""
        return raw_prediction


class FireLaneDetector:
    def __init__(self, fire_lane_model, vehicle_model, config=None):
        # 使用YOLO类加载模型
        self.fire_lane_model = fire_lane_model
        self.vehicle_model = vehicle_model
        self.last_alert_frame = 0
        self.fps = 30
        self.alert_interval = max(1, int(5 * self.fps))  # 每5秒告警一次
        # 车辆类别ID列表
        self.vehicle_classes = [1]  # 汽车、摩托车、公交车、卡车
        # 创建违规车辆追踪器
        self.tracker = ViolationTracker(display_time=5)  # 显示5秒
        # 应用配置
        self.config = config or {}
        self._apply_config()

        # 性能监控
        self.processing_times = deque(maxlen=100)
        self.frames_processed = 0
        self.last_monitor_time = time.time()

    def _apply_config(self):
        """应用配置参数"""
        # 视频处理参数
        self.target_fps = self.config.get("target_fps", 15)
        self.skip_frames = self.config.get("skip_frames", 1)

        # 性能监控
        self.performance_monitor = self.config.get("performance_monitor", False)
        self.monitor_interval = self.config.get("monitor_interval", 5)  # 秒

        # 告警配置
        self.alert_interval = self.config.get("alert_interval", 5)  # 秒
        self.max_history_alerts = self.config.get("max_history_alerts", 3)

        # 多线程处理
        self.use_multithreading = self.config.get("use_multithreading", False)
        self.queue_size = self.config.get("queue_size", 20)

        # 文字显示配置
        self.font_scale = self.config.get("font_scale", 0.7)  # 字体大小
        self.font_thickness = self.config.get("font_thickness", 2)  # 字体粗细
        self.line_thickness = self.config.get("line_thickness", 2)  # 线条粗细

    def process_video(self, frame, frame_count):
        # 获取视频属性
        if frame is None:
            print(f"无法读取图像: {frame}")
            return None
        # 开始计时
        start_time = time.time()
        # 最终输出帧
        output_frame = frame.copy()
        # 单线程处理
        result = self._process_frame(frame, frame_count)
        # 使用消防车道处理后的帧作为基础
        output_frame = result["fire_lane_result"]["processed_frame"]
        # 绘制车辆和状态信息
        violation_count = self._draw_vehicles(
            output_frame,
            result["tracked_vehicles"],
            result["violating_vehicles"],
            frame_count,
        )

        output_frame = self._draw_status_info(
            output_frame, violation_count, time.time() - start_time, frame_count
        )
        # frame_count += 1
        # 性能监控
        self._monitor_performance(frame_count, start_time)
        print(frame_count)
        return output_frame

    def detect_vehicles(self, frame):
        """检测车辆并追踪"""
        if not self.vehicle_model:
            return {}

        # 使用ByteTrack追踪车辆
        results = self.vehicle_model.track(frame)

        tracked_vehicles = {}

        if results[0].boxes.id is not None:
            boxes = results[0].boxes.xyxy.cpu().numpy().astype(int)
            classes = results[0].boxes.cls.cpu().numpy().astype(int)
            track_ids = results[0].boxes.id.cpu().numpy().astype(int)

            for box, cls, track_id in zip(boxes, classes, track_ids):
                if cls in self.vehicle_classes:
                    x1, y1, x2, y2 = box
                    tracked_vehicles[track_id] = {
                        "box_data": (x1, y1, x2, y2, cls),
                        "centroid": ((x1 + x2) // 2, (y1 + y2) // 2),
                    }

        return tracked_vehicles

    def detect_fire_lane(self, frame):
        """检测消防车道区域"""
        fire_lane_mask = np.zeros((frame.shape[0], frame.shape[1]), dtype=np.uint8)
        fire_results = self.fire_lane_model(frame)

        # 创建用于彩色分割的叠加层
        overlay = frame.copy()

        contours = []
        if fire_results[0].masks is not None:
            # 处理每个检测到的消防车道区域
            for ci, c in enumerate(fire_results[0]):
                # 获取物体轮廓
                contour = c.masks.xy.pop().astype(np.int32).reshape(-1, 1, 2)
                contours.append(contour)
                # 在掩码上填充轮廓区域
                cv2.fillPoly(fire_lane_mask, [contour], 255)
                # 在叠加层上绘制消防车道区域
                cv2.fillPoly(overlay, [contour], (0, 255, 0))

        # 将原始图像与彩色分割叠加层融合
        alpha = 0.3  # 透明度因子
        processed_frame = cv2.addWeighted(overlay, alpha, frame, 1 - alpha, 0)

        return {
            "fire_lane_mask": fire_lane_mask,
            "processed_frame": processed_frame,
            "contours": contours,
        }

    def _is_in_fire_lane(self, vehicle_box, fire_lane_mask):
        """检查车辆是否在消防车道上（基于重叠率）"""
        x1, y1, x2, y2 = vehicle_box
        vehicle_area = (x2 - x1) * (y2 - y1)

        # 创建车辆掩码
        vehicle_mask = np.zeros(fire_lane_mask.shape, dtype=np.uint8)
        cv2.rectangle(vehicle_mask, (x1, y1), (x2, y2), 255, -1)  # -1表示填充矩形

        # 计算与消防车道的重叠部分
        overlapped = cv2.bitwise_and(vehicle_mask, fire_lane_mask)
        overlap_area = np.count_nonzero(overlapped)

        # 计算重叠率
        overlap_ratio = overlap_area / vehicle_area if vehicle_area > 0 else 0

        # 如果重叠率超过10%，则视为违规
        return overlap_ratio > 0.1

    def _process_frame(self, frame, frame_count):
        """处理单帧图像"""
        # 分离消防车道检测和车辆追踪
        fire_lane_result = self.detect_fire_lane(frame)

        # 使用原始帧进行车辆检测，避免受消防车道绘制的影响
        tracked_vehicles = self.detect_vehicles(frame)

        # 筛选出在消防车道上的违规车辆
        violating_vehicles = {}
        for track_id, vehicle_info in tracked_vehicles.items():
            box_data = vehicle_info["box_data"]
            x1, y1, x2, y2, cls = box_data
            if self._is_in_fire_lane(
                (x1, y1, x2, y2), fire_lane_result["fire_lane_mask"]
            ):
                violating_vehicles[track_id] = vehicle_info

        # 更新违规车辆追踪器
        self.tracker.update(violating_vehicles, frame_count)

        return {
            "tracked_vehicles": tracked_vehicles,
            "violating_vehicles": violating_vehicles,
            "fire_lane_result": fire_lane_result,
        }

    def _draw_vehicles(self, frame, tracked_vehicles, violating_vehicles, frame_count):
        """绘制车辆检测结果"""
        # 绘制所有检测到的车辆（绿色细框）
        for track_id, vehicle_info in tracked_vehicles.items():
            x1, y1, x2, y2, cls = vehicle_info["box_data"]
            cv2.rectangle(
                frame, (x1, y1), (x2, y2), (0, 255, 0), self.line_thickness
            )  # 增加线条粗细

        # 绘制活跃的违规车辆
        violation_count = 0
        for track_id, vehicle_info in self.tracker.violating_vehicles.items():
            violation_count += 1
            box_data = vehicle_info.get("box_data")
            if not box_data:
                continue

            try:
                x1, y1, x2, y2, cls = box_data
            except (ValueError, TypeError):
                continue

            # 计算停留时间（基于帧率）
            duration = self.tracker.get_duration(track_id, frame_count, self.target_fps)

            # 检查并触发告警
            if self.tracker.check_and_trigger_alert(track_id, frame_count, duration):
                timestamp = datetime.now().strftime("%H:%M:%S")
                logger.warning(
                    f"告警：违规车辆 ID:{track_id} 停留时长已超过5秒 ({duration:.1f}s)"
                )

            # 在车辆上方显示ID和停留时间 - 增加字体大小和粗细
            info_text = f"ID:{track_id} {duration:.1f}s"
            cv2.putText(
                frame,
                info_text,
                (x1, y1 - 10),
                cv2.FONT_HERSHEY_SIMPLEX,
                self.font_scale,
                (0, 0, 255),
                self.font_thickness,
            )

            # 红色粗边框（违规车辆）- 增加线条粗细
            cv2.rectangle(
                frame, (x1, y1), (x2, y2), (0, 0, 255), self.line_thickness + 1
            )

        return violation_count

    def _draw_status_info(self, frame, violation_count, process_time, frame_count):
        """绘制状态信息"""
        height, width = frame.shape[:2]

        # 在左上角显示所有违规车辆信息（包括活跃和非活跃的）
        all_violations = self.tracker.get_all_violations()
        y_offset = 30  # 从视频顶部开始
        line_height = int(25 * self.font_scale)  # 根据字体大小调整行高

        # 显示每辆违规车辆信息
        for violation_id, violation_info in all_violations:
            # 对于活跃车辆，获取当前时长
            duration = self.tracker.get_duration(
                violation_id, frame_count, self.target_fps
            )

            # 检查该车辆是否活跃（当前在画面中）
            is_active = violation_id in self.tracker.violating_vehicles

            # 设置文本颜色：活跃车辆为红色，非活跃车辆为灰色
            color = (0, 0, 255) if is_active else (255, 0, 0)

            # 状态指示符
            status = "" if is_active else " (已驶离)"

            info_text = f"车辆ID:{violation_id} 停留时长:{duration:.1f}s{status}"

            # 在左上角显示车辆信息 - 增加字体大小和粗细
            cv2AddChineseText(
                frame, info_text, (15, y_offset), color, self.font_thickness
            )

            y_offset += line_height

        # 更新告警状态
        self.tracker.update_alerts(frame_count, self.target_fps)

        # 获取当前活跃告警
        active_alerts = self.tracker.get_active_alerts()
        # 获取完成告警
        completed_alerts = self.tracker.get_completed_alerts()

        # 显示告警标题 - 增加字体大小和粗细
        alert_title = "预警车辆停留大于5s"
        cv2AddChineseText(
            frame, alert_title, (width - 220, 30), (0, 0, 255), self.font_thickness
        )

        # 显示告警计数器 - 增加字体大小和粗细
        alert_count_text = f"告警数量: {len(active_alerts)}"
        cv2AddChineseText(
            frame, alert_count_text, (width - 220, 60), (0, 0, 255), self.font_thickness
        )

        # 显示活跃告警 - 增加字体大小和粗细
        alert_y = 90
        for track_id, alert_info in active_alerts.items():
            duration = self.tracker.get_duration(track_id, frame_count, self.target_fps)
            alert_text = f"车辆ID:{track_id} 停留时长:{duration:.1f}s"
            cv2AddChineseText(
                frame,
                alert_text,
                (width - 220, alert_y),
                (0, 0, 255),
                self.font_thickness,
            )
            alert_y += line_height

        # 显示最近N条已完成告警 - 增加字体大小和粗细
        completed_y = alert_y + 15
        if completed_alerts:
            cv2AddChineseText(
                frame,
                "历史告警:",
                (width - 220, completed_y),
                (0, 165, 255),
                self.font_thickness,
            )
            completed_y += line_height

            # 只显示最近N条已完成告警
            recent_alerts = list(completed_alerts.items())[-self.max_history_alerts :]
            for track_id, alert_info in recent_alerts:
                alert_text = f"车辆ID:{track_id} 停留时长:{alert_info['start_time']}"
                cv2AddChineseText(
                    frame,
                    alert_text,
                    (width - 220, completed_y),
                    (0, 165, 255),
                    self.font_thickness,
                )
                completed_y += line_height

        # 计算处理时间和帧率
        current_fps = 1.0 / process_time if process_time > 0 else 0

        # 显示状态信息 - 增加字体大小和粗细
        status = f"FPS: {current_fps:.1f} | Violations: {violation_count}/{len(all_violations)}"
        cv2AddChineseText(
            frame, status, (15, height - 20), (0, 0, 255), self.font_thickness
        )

        return frame

    def _monitor_performance(self, frame_count, start_time):
        """监控性能"""
        if not self.performance_monitor:
            return

        process_time = time.time() - start_time
        self.processing_times.append(process_time)
        self.frames_processed += 1

        current_time = time.time()
        if current_time - self.last_monitor_time > self.monitor_interval:
            avg_fps = self.frames_processed / (current_time - self.last_monitor_time)
            avg_process_time = (
                sum(self.processing_times) / len(self.processing_times)
                if self.processing_times
                else 0
            )
            logger.info(
                f"性能监控: 平均FPS: {avg_fps:.1f}, 平均处理时间: {avg_process_time*1000:.1f}ms, 总帧: {frame_count}"
            )
            self.frames_processed = 0
            self.last_monitor_time = current_time


class ViolationTracker:
    """违规车辆追踪器，负责管理和记录违规车辆信息"""

    def __init__(self, display_time=5):
        # 存储违规车辆信息 {vehicle_id: {'box_data': (x1,y1,x2,y2,cls), 'start_frame': frame_count}}
        self.violating_vehicles = OrderedDict()
        # 存储已消失但还需要显示的违规车辆信息
        self.inactive_violations = OrderedDict()
        # 显示时间（秒）
        self.display_time = display_time
        # 存储活跃告警信息 {vehicle_id: {'frame': 触发告警的帧号, 'start_time': 触发告警的时间}}
        self.active_alerts = {}
        # 存储已完成的告警信息
        self.completed_alerts = OrderedDict()
        # 告警持续时间（秒）
        self.alert_duration = 5
        # 存储车辆开始违规的帧号
        self.start_frames = {}

    def register(self, track_id, box_data, current_frame):
        """注册新的违规车辆"""
        self.violating_vehicles[track_id] = {
            "box_data": box_data,
            "start_time": current_frame,
        }
        self.start_frames[track_id] = current_frame

    def deregister(self, track_id, current_frame):
        """注销违规车辆并保存信息"""
        if track_id in self.violating_vehicles:
            # 保存到非活跃违规车辆列表
            self.inactive_violations[track_id] = {
                "start_frame": self.start_frames[track_id],
                "end_frame": current_frame,
            }
            # 从当前追踪中移除
            del self.violating_vehicles[track_id]
            del self.start_frames[track_id]

        # 如果车辆有告警，标记为已完成
        if track_id in self.active_alerts:
            alert_info = self.active_alerts[track_id]
            alert_info["end_frame"] = current_frame
            self.completed_alerts[track_id] = alert_info
            del self.active_alerts[track_id]

    def update(self, tracked_vehicles, current_frame):
        """更新违规车辆状态"""
        # 获取当前活跃的追踪ID
        current_ids = set(tracked_vehicles.keys())

        # 检查消失的车辆
        for track_id in list(self.violating_vehicles.keys()):
            if track_id not in current_ids:
                # 车辆消失，注销
                self.deregister(track_id, current_frame)

        # 更新现有车辆信息
        for track_id, vehicle_info in tracked_vehicles.items():
            if track_id in self.violating_vehicles:
                # 更新已存在的违规车辆
                self.violating_vehicles[track_id]["box_data"] = vehicle_info["box_data"]
            else:
                # 注册新违规车辆
                self.register(track_id, vehicle_info["box_data"], current_frame)

        return self.violating_vehicles

    def get_all_violations(self):
        """获取所有违规车辆信息（包括活跃和非活跃的）"""
        return list(self.violating_vehicles.items()) + list(
            self.inactive_violations.items()
        )

    def get_duration(self, track_id, current_frame, frame_rate):
        """获取违规车辆停留时长（秒）"""
        if track_id in self.violating_vehicles:
            # 活跃车辆：计算从开始帧到当前帧的时长
            start_frame = self.start_frames[track_id]
            frames_elapsed = current_frame - start_frame
            return frames_elapsed / frame_rate
        elif track_id in self.inactive_violations:
            # 非活跃车辆：使用存储的开始帧和结束帧
            data = self.inactive_violations[track_id]
            frames_elapsed = data["end_frame"] - data["start_frame"]
            return frames_elapsed / frame_rate
        return 0.0

    def check_and_trigger_alert(self, track_id, current_frame, duration):
        """检查并触发告警"""
        if duration >= 5.0:  # 停留时长超过5秒
            if track_id not in self.active_alerts:
                # 首次触发告警
                self.active_alerts[track_id] = {
                    "trigger_frame": current_frame,
                    "start_time": datetime.now().strftime("%H:%M:%S"),
                }
                return True
        return False

    def update_alerts(self, current_frame, frame_rate):
        """更新告警状态"""
        completed = []
        for track_id, alert_info in list(self.active_alerts.items()):
            # 计算告警持续时间（帧）
            elapsed_frames = current_frame - alert_info["trigger_frame"]
            elapsed_time = elapsed_frames / frame_rate

            # 检查告警是否超过显示时间
            if elapsed_time > self.alert_duration:
                # 添加到已完成告警
                alert_info["end_frame"] = current_frame
                self.completed_alerts[track_id] = alert_info
                del self.active_alerts[track_id]
                completed.append(track_id)
        return completed

    def get_active_alerts(self):
        """获取当前活跃告警"""
        return self.active_alerts

    def get_completed_alerts(self):
        """获取已完成告警"""
        return self.completed_alerts
