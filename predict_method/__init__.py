from .base_predict import BasePredictor
from .object_detector import ObjectDetector
from config.model_config import ModelConfig
from .object_detector_yolo import ObjectDetectorYolo
from .object_detector_engine import ObjectDetectorEngine
from .object_detector_tensorrt import ObjectDetectorTensorrt
from .car_door_engine import CarDoorEngine
from .yellow_grid_engine import YellowGridEngine
from .car_direction_engine import CarDirectionEngine
from .person_crowd_engine import PersonCrowdEngine
from .motor_helmet_engine import MotorHelmetEngine
from .car_violation_engine import CarViolationEngine
from .car_velocity_engine import CarVelocityEngine
from .person_crowd_engine_v2_DBSCAN import PersonCrowdEngine_2
from .fish_man_engine_v1 import FishManCrowdEngine
from .roadblock_v1_engine import RoadBlockEngine
from .person_flow_count_engine import PersonFlowCountEngine
from .mult_car_flow_count_V1_engine import CarFlowCountEngine
from .banner_text_recognition import BannerTextRecognition
from .car_plate_recognition import CarPlateRecognition
from .car_class_engine import CarClassEngine

__all__ = [
    "ObjectDetector",
    "ObjectDetectorYolo",
    "BasePredictor",
    "ObjectDetectorEngine",
    "ObjectDetectorTensorrt",
    "CarDoorEngine",
    "YellowGridEngine",
    "CarDirectionEngine",
    "PersonCrowdEngine",
    "PersonCrowdEngine_2",
    "MotorHelmetEngine",
    "CarViolationEngine",
    "CarVelocityEngine",
    "FishManCrowdEngine",
    "RoadBlockEngine",
    "PersonFlowCountEngine",
    "CarFlowCountEngine",
    "BannerTextRecognition",
    "CarPlateRecognition",
    "CarClassEngine",
]

def get_process(model_config:ModelConfig,task={}): 
    return eval(model_config.config.get('predict_method'))(model_config,task)
