import cv2
import os
import torch
import numpy as np
from torchvision.ops import box_area
from collections import defaultdict
from shapely.geometry import Polygon
from typing import Union, Any, Dict, List, Tuple
from PIL import Image
import logging
import time
from pathlib import Path
from runtime import EngineModelRuntime
from predict_method.base_predict import BasePredictor
from utils.image_util import cv2AddChineseText

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class CarViolationEngine(BasePredictor):

    def _initialize_from_config(self):
        """从配置文件初始化目标检测模型和相关参数"""
        # 初始化模型参数
        self.yellow_grid_model = EngineModelRuntime(
            self._get_file_by_key("model_filename"),
            predict_params=self.config.get("yellow_grid_predict_params", {}),
            num_workers=1,
            input_size=[1280, 1280],
            task="segment",
        )
        self.vehicle_model = EngineModelRuntime(
            self._get_file_by_key("car_model"),
            predict_params=self.config.get("vehicle_predict_params", {}),
            num_workers=1,
        )
        # 配置参数 - 调整字体和分辨率相关参数
        config = {
            "target_fps": 15,  # 目标推流帧率
            "font_scale": 1,  # 增大字体大小
            "font_thickness": 2,  # 增加字体粗细
        }
        self.monitor = CarViolationPredict(
            self.yellow_grid_model, self.vehicle_model, config
        )
        self.frame_skip_counter = 0
        self.frame_count = 0
        self.skip_frames = 1  # 跳帧设置
        self.status = False
        print(f"Initialized detector with: {self.config}")

    def preprocess(
            self, image_input: Union[str, Image.Image, np.ndarray]
    ) -> np.ndarray:
        """预处理图像数据以适应模型输入要求"""
        # 如果输入是文件路径，加载图像
        if isinstance(image_input, str):
            image = cv2.imread(image_input)
        elif isinstance(image_input, Path):
            image = cv2.imread(str(image_input))
        elif isinstance(image_input, Image.Image):
            image = np.array(image_input)
        else:
            image = image_input
        return image

    def callback(self, task, processed_frame, predictions, timestamp, original_frame):
        if not self.status:
            return
        import time
        if not hasattr(self, '_last_call_time'):
            self._last_call_time = 0

        current_time = time.time()  # 获取当前系统时间（秒）
        if current_time - self._last_call_time >= 5:
            logger.info(self._last_call_time)
            print("================================")
            self.call_ai_hook_interface(task.callback_url, processed_frame, timestamp, task.latitude, task.longitude)
            self._last_call_time = current_time

    def predict(self, image_array: np.ndarray) -> np.ndarray:
        """执行目标检测预测"""
        # self.frame_skip_counter += 1
        # if self.frame_skip_counter <= self.skip_frames:
        # 跳过帧，不处理
        # return image_array
        self.frame_skip_counter = 0
        frame, self.status = self.monitor.yolo_inference(image_array)
        self.frame_count += 1
        return frame

    def visualize(self, frame: np.ndarray, out) -> np.ndarray:
        return out

    def postprocess(self, raw_prediction: np.ndarray) -> List[Dict[str, Any]]:
        """后处理预测结果，返回易读的检测结果"""
        return raw_prediction


class CarViolationPredict:
    """
    车辆违停识别算法：

    Attributes:
        model1：推理违停区域，为图像分割模型
        model2：推理车辆类型，为目标检测模型
        yolo_inference_for_examples：案例函数，yolo11官方案例
        compute_coverage_and_highlight：计算第二个模型检测框被第一个模型检测框覆盖的面积百分比，并标记高覆盖率的检测框
        merge_yolo_results：合并两个模型的推理结果
        yolo_inference：路面违停识别算法实现，标红重叠占比达到70%的检测框，并且统计违停时间。
                        若违停时间超过一定值则告警。一定时间内未出现违停情况则重置违停时间。
    """

    def __init__(self, yellow_grid_model, car_model, config=None):
        """
        初始化全局变量
        """
        self.yellow_grid_model = yellow_grid_model
        self.car_model = car_model
        # self.y_offset = 40  # 初始字段Y坐标偏移量
        # self.x_offset = 10  # 初始字段x坐标偏移量
        # self.field_intervals = 40 # 初始字段间隔
        self.current_time = 0.0  # 当前帧时间
        self.tracking_data = {}  # 跟踪数据
        self.violation_status = {}  # 警报状态
        # self.violation_text = ""  # 警报显示文本
        self.track_history = defaultdict(lambda: [])  # 跟踪警报历史
        self.violation = False
        self.color = (125, 125, 125)
        # 类别名称映射
        self.class_names = {
            0: '自行车', 1: '车', 2: '面包车', 3: '卡车',
            4: '三轮车', 5: '带蓬三轮车', 6: '公交车', 7: '摩托车'
        }
        # 应用配置
        self.config = config or {}
        self._apply_config()

    def _apply_config(self):
        """应用配置参数"""
        # 视频处理参数
        self.target_fps = self.config.get("target_fps", 25)
        self.skip_frames = self.config.get("skip_frames", 1)

        # 性能监控
        self.performance_monitor = self.config.get("performance_monitor", False)
        self.monitor_interval = self.config.get("monitor_interval", 5)  # 监控间隔、默认5s内未检测到违停不显示

        # 告警配置
        self.alert_thresholds = self.config.get("alert_thresholds", 10)  # 警报阈值，默认超过10s违停时间则发出警报
        self.min_alerts = self.config.get("max_history_alerts", 2)  # 最短违停时间显示间隔
        self.coverage_threshold = self.config.get("coverage_threshold", 0.2)  # 重叠度阈值

        # 多线程处理
        self.use_multithreading = self.config.get("use_multithreading", False)
        self.queue_size = self.config.get("queue_size", 20)

        # 文字显示配置
        self.font_scale = self.config.get("font_scale", 0.7)  # 字体大小
        self.font_thickness = self.config.get("font_thickness", 2)  # 字体粗细
        self.line_thickness = self.config.get("line_thickness", 2)  # 线条粗细

    # def reset(self):
    #     """
    #     重置所有变量
    #     """
    #     self.current_time = 0.0
    #     self.tracking_data = {}
    #     self.violation_status = {}
    #     self.violation_text = ""
    #     self.track_history = defaultdict(lambda: [])

    def yolo_inference(self, frame):
        """
        路面违停识别算法实现，标红重叠占比达到70%的检测框，并且统计违停时间。
        若违停时间超过一定值则告警。一定时间内未出现违停情况则重置违停时间。

        Returns:图像或视频检测结果

        """
        # 获取视频属性
        if frame is None:
            print(f"无法读取图像: {frame}")
            return None

        self.current_time = time.time()
        self.violation = False

        results1 = self.yellow_grid_model.predict(frame)
        trace = self.car_model.track(frame)[0]

        # 计算覆盖率并获取高亮索引
        coverage_ratios, highlight_indices = self.compute_coverage_and_highlight(results1[0], trace)
        plot = results1[0].plot(boxes=False)  # 第一个模型推理掩码结果

        # Get the boxes and track IDs
        if trace.boxes and trace.boxes.is_track:
            boxes = trace.boxes.xywh.cpu()
            track_ids = trace.boxes.id.int().cpu().tolist()
            classes = trace.boxes.cls.cpu().tolist()

            # Plot the tracks
            for box, track_id, cls_id in zip(boxes, track_ids, classes):
                # 初始化跟踪数据
                if track_id not in self.tracking_data:
                    self.tracking_data[track_id] = {
                        'start_time': 0.0,
                        'total_time': 0.0,
                        'last_active': 0.0,
                        'class_id': int(cls_id)
                    }

                # 更新轨迹历史
                if track_id not in self.track_history:
                    self.track_history[track_id] = []

        # 在第一个模型的掩码结果上标红高IOU框
        if hasattr(trace, 'boxes') and len(highlight_indices) > 0:
            boxes = trace.boxes.xyxy.cpu().numpy()

            for idx in highlight_indices:
                if idx < len(boxes):
                    box = boxes[idx].astype(int)
                    try:
                        track_id = trace.boxes.id[idx].item()
                    except (AttributeError, TypeError, IndexError):
                        track_id = -1

                    # 更新时间统计
                    if track_id != -1 and track_id in self.tracking_data:
                        data = self.tracking_data[track_id]
                        if data['start_time'] == 0.0:
                            data['start_time'] = self.current_time
                        data['last_active'] = self.current_time
                        data['total_time'] = self.current_time - data['start_time']

                        # 检查违规条件
                        if data['total_time'] > self.min_alerts:
                            self.violation_status[track_id] = True

                        # 绘制红色边框
                        class_id = self.tracking_data[track_id]['class_id']
                        class_name = self.class_names[class_id]
                        violation_text = f"{class_name}[{int(track_id)}]:{int(self.tracking_data[track_id]['total_time'])}s"
                        if self.tracking_data[track_id]['total_time'] > self.alert_thresholds:
                            self.color = (94, 53, 255)
                            self.violation = True
                            # violation_text += "|违停"

                        else:
                            self.color = (125, 125, 125)

                        text_width = len(violation_text) * 17
                        cv2.rectangle(plot, (box[0], box[1]), (box[2], box[3]), self.color, self.line_thickness)
                        cv2.rectangle(plot, (box[0], box[1] - 22),
                                      (box[0] + text_width, box[1]), self.color, -1)
                        plot = cv2AddChineseText(plot, violation_text,(box[0] + 2, box[1] - 20),(255, 255, 255), textSize=20)

        # 检查告警消失条件（5秒未激活）
        for track_id, data in list(self.tracking_data.items()):
            if self.current_time - data['last_active'] > self.monitor_interval:
                if track_id in self.violation_status:
                    del self.violation_status[track_id]
                data['start_time'] = 0.0  # 重置计时

        # # 添加文本描绘违停车辆、违停时间和告警
        # for i, track_id in enumerate(sorted(self.violation_status.keys())):
        #     if track_id in self.tracking_data:
        #         class_id = self.tracking_data[track_id]['class_id']
        #         class_name = trace.names.get(class_id, f"Class_{class_id}")
        #         self.violation_text += f"{class_name}[{int(track_id)}] : {self.tracking_data[track_id]['total_time']:.2f}s"
        #         if self.tracking_data[track_id]['total_time'] > self.alert_thresholds:
        #             self.violation_text += " | Violation"
        #
        #         self.y_offset += i * self.field_intervals
        #         cv2.putText(plot, self.violation_text.rstrip(', '), (self.x_offset, self.y_offset),
        #                     cv2.FONT_HERSHEY_SIMPLEX, self.font_scale, (0, 0, 255), self.font_thickness)
        #
        #     self.violation_text = ""
        #     self.y_offset = 30

        # self.reset()

        return plot, self.violation

    def compute_coverage_and_highlight(self, model1_results, model2_results):
        """
        计算第二个模型检测框被第一个模型检测框覆盖的面积百分比，并标记高覆盖率的检测框

        Args:
        model1_results(ultralytics.engine.results.Results)：第一个模型的推理结果
        model2_results(ultralytics.engine.results.Results)：第二个模型的推理结果
        coverage_threshold(float)：覆盖率阈值，超过此值则标记为高覆盖 (默认0.7)

        returns:
        (coverage_ratios, highlight_indices):
            coverage_ratios(list[float])：第二个模型每个检测框的最大覆盖率
            highlight_indices：第二个模型中需要高亮的框索引列表
        """
        # 提取两个模型的检测框 (xyxy格式)
        boxes1 = model1_results.boxes.xyxy.cpu()
        boxes2 = model2_results.boxes.xyxy.cpu()

        # 初始化结果
        coverage_ratios = []
        highlight_indices = []

        # 计算第二个模型每个检测框的面积
        areas2 = box_area(boxes2) if len(boxes2) > 0 else torch.tensor([])

        # 计算每个第二个模型框被第一个模型框覆盖的比例
        for i, box1 in enumerate(boxes1):
            for j, box2 in enumerate(boxes2):
                max_coverage = 0.0

                # 计算当前框的面积
                area2 = areas2[j].item()

                if len(boxes1) > 0:
                    x1, y1, x2, y2 = box2.tolist()  # 将张量转换为Python列表
                    corners = [
                        (x1, y1),  # 左上角
                        (x2, y1),  # 右上角
                        (x2, y2),  # 右下角
                        (x1, y2)  # 左下角
                    ]

                    mask1 = model1_results.masks.xy

                    # 创建矩形和多边形对象
                    corner = Polygon(corners)
                    mask = Polygon(mask1[i].tolist()).buffer(0)

                    # 计算交集面积
                    intersection = corner.intersection(mask)

                    # 计算覆盖率 = 交集面积 / 当前框面积
                    coverage_ratios_j = intersection.area / area2

                    # 获取最大覆盖率（考虑多个覆盖的情况）
                    max_coverage = coverage_ratios_j

                coverage_ratios.append(max_coverage)

                # 检查是否超过阈值
                if max_coverage >= self.coverage_threshold:
                    highlight_indices.append(j)

        return coverage_ratios, highlight_indices
