
from typing import Union, Any, Dict, List, Tuple
from PIL import Image
import numpy as np
from pathlib import Path

from predict_method.base_predict import BasePredictor

from runtime import YoloModelRuntime
import cv2 as cv
from utils.image_util import cv2AddChineseText


class ObjectDetectorYolo(BasePredictor):
    def _initialize_from_config(self):
        """从配置文件初始化目标检测模型和相关参数"""

        # 初始化模型参数
        self.confidence_threshold = self.config.get('confidence_threshold', 0.5)
        self.iou_threshold = self.config.get('iou_threshold', 0.4)
        self.input_size = tuple(self.config['input_size'])  # 期望的输入尺寸 (height, width)
        self.classes = self.config['classes']  # 类别名称列表
        self.session = YoloModelRuntime(self._get_model_file().read())
        print(f"Initialized detector with: {self.config}")

    def preprocess(self, image_input: Union[str, Image.Image, np.ndarray]) -> np.ndarray:
        """预处理图像数据以适应模型输入要求"""
        return image_input

    def predict(self, image_array: np.ndarray) -> np.ndarray:
        """执行目标检测预测"""
        return self.session.predict(image_array)
    def visualize(self, frame: np.ndarray,out) -> np.ndarray:
        """执行目标检测预测"""
        output_frame = frame.copy()
        # 自定义绘制每个检测框
        for i, box in enumerate(out):
            x1, y1, x2, y2 = map(int, box['bbox'])
            
            # 框颜色和样式
            color = (0, 0, 255)  # 绿色
            thickness = 2
            
            # 绘制边界框
            cv.rectangle(output_frame, (x1, y1), (x2, y2), color, thickness)
            
            # 置信度和类别标签
            #label = f"{results[0].names[int(cls[i])]} {conf[i]:.2f}"
            
            # 计算文本背景
            (text_width, text_height), _ = cv.getTextSize(str(box['class_name']), cv.FONT_HERSHEY_SIMPLEX, 0.5, 1)
            cv.rectangle(output_frame, (x1, y1 - text_height - 10), (x1 + text_width, y1), color, -1)
            output_frame=cv2AddChineseText(output_frame,str(box['class_name']),(x1+8, y1-text_height-8),(255, 255, 255),16)
        return output_frame
    def postprocess(self, raw_prediction: np.ndarray) -> List[Dict[str, Any]]:
        """后处理预测结果，返回易读的检测结果"""
        detections = []
        for result in raw_prediction:
            for box in result.boxes:
                # 获取坐标 (x1, y1, x2, y2)
                x1, y1, x2, y2 = box.xyxy[0].tolist()
                
                # 获取置信度和类别ID
                conf = box.conf.item()
                cls_id = box.cls.item()
                
                # 构建检测结果字典
                detection = {
                    'bbox': [float(x1), float(y1), float(x2), float(y2)],
                    'conf': conf,
                    'class_id': int(cls_id),
                    'class_name': self.classes[int(cls_id)]  # 使用 model.names 获取类别名称
                }
                detections.append(detection)
        return detections
   