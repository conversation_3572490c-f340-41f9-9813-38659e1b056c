from turtle import width
from runtime import EngineModelRuntime
import cv2
import numpy as np
from datetime import datetime
from collections import deque
from predict_method.base_predict import BasePredictor
from typing import Union, Any, Dict, List, Tuple
from PIL import Image
from pathlib import Path
import os
import time


class CarDirectionEngine(BasePredictor):

    def _initialize_from_config(self):
        """从配置文件初始化目标检测模型和相关参数"""
        # 初始化模型参数
        self.car_model = EngineModelRuntime(
            self._get_file_by_key("model_filename"),
            predict_params=self.config.get("predict_params"),
        )
        car_config = CarConfig()
        if self.config.get("density_box_min_count"):
            car_config.DENSITY_BOX_MIN_COUNT = self.config.get("density_box_min_count")
        self.monitor = TrafficAnalyzer(self.car_model, car_config, self)
        print(f"Initialized detector with: {self.config}")

    def preprocess(
        self, image_input: Union[str, Image.Image, np.ndarray]
    ) -> np.ndarray:
        """预处理图像数据以适应模型输入要求"""
        # 如果输入是文件路径，加载图像
        if isinstance(image_input, str):
            image = cv2.imread(image_input)
        elif isinstance(image_input, Path):
            image = cv2.imread(str(image_input))
        elif isinstance(image_input, Image.Image):
            image = np.array(image_input)
        else:
            image = image_input
        return image

    def predict(self, image_array: np.ndarray) -> np.ndarray:
        """执行目标检测预测"""
        boxes, centers = self.monitor.detect_vehicles(image_array)
        # 调用交通分析器的绘制方法，在帧上绘制检测结果
        result_frame = self.monitor.draw_results(image_array, boxes, centers)
        return result_frame

    def visualize(self, frame: np.ndarray, out) -> np.ndarray:
        return out

    def postprocess(self, raw_prediction: np.ndarray) -> List[Dict[str, Any]]:
        """后处理预测结果，返回易读的检测结果"""
        return raw_prediction


# 定义配置类，用于存储模型、聚类、区域和可视化等参数
class CarConfig:
    # 热力图配置
    HEATMAP_RADIUS = 30
    HEATMAP_THRESHOLD = 0.6
    DENSITY_BOX_COLOR = (0, 0, 255)
    # 模型配置
    # VEHICLE_CLASSES = [2, 5, 7]
    VEHICLE_CLASSES = [0, 7, 10, 12]
    # 目标检测的置信度阈值，只有置信度高于该值的检测结果才会被保留
    CONFIDENCE_THRESHOLD = 0.3
    # 模型推理时输入图像的大小
    INFERENCE_SIZE = 640
    DENSITY_BOX_MIN_COUNT = 7  # 区域框的最小车辆数量阈值

    DENSITY_LEVELS = [
        (5, "Lv1", (0, 255, 0)),  # 0-3辆: 绿色
        (10, "Lv2", (0, 255, 255)),  # 4-7辆: 黄色
        (99, "Lv3", (0, 0, 255)),  # 8+辆:  红色
    ]
    TEXT_FONT = cv2.FONT_HERSHEY_SIMPLEX
    TEXT_SCALE = 0.8
    TEXT_THICKNESS = 2


# 定义交通分析类，用于车辆检测、聚类分析和结果可视化
class TrafficAnalyzer:
    def __init__(self, car_model, car_config, predictor):
        self.model = car_model
        self.predictor = predictor
        self.car_config = car_config

    def detect_vehicles(self, frame):
        # 检查输入帧是否为空或无效
        if frame is None or frame.size == 0:
            return [], np.empty((0, 2))

        try:
            # 计算缩放比例，保持宽高比
            orig_h, orig_w = frame.shape[:2]
            scale = min(
                self.car_config.INFERENCE_SIZE / orig_w,
                self.car_config.INFERENCE_SIZE / orig_h,
            )
            new_w, new_h = int(orig_w * scale), int(orig_h * scale)
            # 调整图像大小
            resized_frame = cv2.resize(frame, (new_w, new_h))
            # 创建带黑边的画布以保持原始比例
            canvas = np.zeros(
                (self.car_config.INFERENCE_SIZE, self.car_config.INFERENCE_SIZE, 3),
                dtype=np.uint8,
            )
            x_offset = (self.car_config.INFERENCE_SIZE - new_w) // 2
            y_offset = (self.car_config.INFERENCE_SIZE - new_h) // 2
            canvas[y_offset : y_offset + new_h, x_offset : x_offset + new_w] = (
                resized_frame
            )
            # 将BGR颜色空间转换为RGB颜色空间
            rgb_frame = cv2.cvtColor(canvas, cv2.COLOR_BGR2RGB)
        except Exception as e:
            # 若调整大小或颜色转换失败，打印错误信息并返回空结果
            print(f"Resize error: {str(e)}")
            return [], np.empty((0, 2))

        # 使用自动混合精度进行推理，提高计算效率
        try:
            # 使用YOLO模型对调整后的帧进行推理，获取检测结果
            results = self.model.predict(rgb_frame)[0]
        except Exception as e:
            # 若推理失败，打印错误信息并返回空结果
            print(f"Inference error: {str(e)}")
            return [], np.empty((0, 2))

        # 初始化检测框列表和中心点列表
        boxes, centers = [], []
        # 遍历检测结果中的每个边界框
        for box in results.boxes:
            # 检查边界框的类别是否在配置的车辆类别中，并且置信度是否高于阈值
            # if int(box.cls) in self.car_config.VEHICLE_CLASSES and box.conf > self.car_config.CONFIDENCE_THRESHOLD:
            if box.conf > self.car_config.CONFIDENCE_THRESHOLD:
                # 获取画布上的坐标
                x1_canvas, y1_canvas, x2_canvas, y2_canvas = map(float, box.xyxy[0])
                # 减去偏移量，转换到调整后图像的坐标
                x1_resized = x1_canvas - x_offset
                y1_resized = y1_canvas - y_offset
                x2_resized = x2_canvas - x_offset
                y2_resized = y2_canvas - y_offset
                # print(x1_canvas,y1_canvas,x2_canvas,y2_canvas)
                # 缩放到原始图像坐标
                x1 = int(x1_resized / scale)
                y1 = int(y1_resized / scale)
                x2 = int(x2_resized / scale)
                y2 = int(y2_resized / scale)
                # 将缩放后的检测框添加到列表中
                boxes.append((x1, y1, x2, y2))
                # 计算检测框的中心点并添加到列表中
                centers.append([(x1 + x2) // 2, (y1 + y2) // 2])

        # 返回检测框列表和中心点数组
        return boxes, np.array(centers) if centers else np.empty((0, 2))

    def generate_heatmap(self, centers, frame_shape):
        """生成车辆密度热力图"""
        heatmap = np.zeros(frame_shape[:2], dtype=np.float32)
        for x, y in centers:
            cv2.circle(heatmap, (x, y), self.car_config.HEATMAP_RADIUS, 1, -1)
        heatmap = cv2.GaussianBlur(heatmap, (99, 99), 0)
        return (heatmap - heatmap.min()) / (heatmap.max() - heatmap.min() + 1e-8)

    def draw_results(self, frame, boxes, centers):
        # 生成热力图
        heatmap = self.generate_heatmap(centers, frame.shape)
        # colored_heatmap = cv2.applyColorMap(
        #     (heatmap * 255).astype(np.uint8), cv2.COLORMAP_JET
        # )

        # 叠加热力图到原始帧
        # frame = cv2.addWeighted(frame, 0.7, colored_heatmap, 0.3, 0)

        # 绘制密度框
        thresh_map = (heatmap > self.car_config.HEATMAP_THRESHOLD).astype(np.uint8)
        contours, _ = cv2.findContours(
            thresh_map, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
        )

        for cnt in contours:
            # 创建掩膜统计区域内的车辆数量
            mask = np.zeros(heatmap.shape[:2], dtype=np.uint8)
            cv2.drawContours(mask, [cnt], -1, 1, thickness=cv2.FILLED)

            # 转换中心点为整数坐标并过滤无效点
            if centers.size > 0:
                points = centers.astype(int)
                valid = (
                    (points[:, 0] >= 0)
                    & (points[:, 0] < mask.shape[1])
                    & (points[:, 1] >= 0)
                    & (points[:, 1] < mask.shape[0])
                )
                valid_points = points[valid]

                # 统计在区域内的车辆数量
                if valid_points.size > 0:
                    in_contour = mask[valid_points[:, 1], valid_points[:, 0]]
                    count = np.sum(in_contour)
                else:
                    count = 0
            else:
                count = 0

            # 仅当数量达到阈值时绘制框
            if count >= self.car_config.DENSITY_BOX_MIN_COUNT:
                x, y, w, h = cv2.boundingRect(cnt)
                cv2.rectangle(
                    frame, (x, y), (x + w, y + h), self.car_config.DENSITY_BOX_COLOR, 2
                )
                text_color = None
                # 计算密集程度
                density_text = f"{count}"
                for max_count, label, color in self.car_config.DENSITY_LEVELS:
                    if count <= max_count:
                        density_text = f"{label}({count}car)"
                        text_color = color
                        break

                # 计算文字位置（框顶部居中）
                text_size = cv2.getTextSize(
                    density_text,
                    self.car_config.TEXT_FONT,
                    self.car_config.TEXT_SCALE,
                    self.car_config.TEXT_THICKNESS,
                )[0]
                text_x = x + (w - text_size[0]) // 2
                text_y = y - 10 if y > 30 else y + 20

                # 添加文字背景
                cv2.rectangle(
                    frame,
                    (text_x - 5, text_y - text_size[1] - 5),
                    (text_x + text_size[0] + 5, text_y + 5),
                    (30, 30, 30),
                    -1,
                )

                # 绘制密度文字
                cv2.putText(
                    frame,
                    density_text,
                    (text_x, text_y),
                    self.car_config.TEXT_FONT,
                    self.car_config.TEXT_SCALE,
                    text_color,
                    self.car_config.TEXT_THICKNESS,
                )
                # timestamp = time.time()
                # self.predictor.call_ai_hook_interface(
                #     self.predictor.task.callback_url, frame, timestamp
                # )

        # 在帧上绘制检测框
        # self._draw_detections(frame, boxes)
        # 在帧上绘制中心点
        # for (x, y) in centers:
        #     cv2.circle(frame, (x, y), 3, (0, 0, 255), -1)
        return self._draw_detections(frame, boxes)

    def _draw_detections(self, frame, boxes):
        # 遍历所有检测框
        for x1, y1, x2, y2 in boxes:
            # 在帧上绘制检测框
            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 1)
            # cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 0, 255), 1)
        return frame
           
