from turtle import width
from runtime import EngineModelRuntime
import cv2
import numpy as np
from datetime import datetime
from collections import deque
from predict_method.base_predict import BasePredictor
from typing import Union, Any, Dict, List, Tuple
from PIL import Image
from pathlib import Path
import os
import time


class PersonCrowdEngine(BasePredictor):

    def _initialize_from_config(self):
        """从配置文件初始化目标检测模型和相关参数"""
        # 初始化模型参数
        self.person_model = EngineModelRuntime(
            self._get_file_by_key("model_filename"),
            input_size=[1280,1280],
            predict_params=self.config.get("person_predict_params"),
        )
        self.monitor = CrowdMonitor(self.person_model, PersonConfig(), self)
        print(f"Initialized detector with: {self.config}")

    def predict(self, image_array: np.ndarray) -> np.ndarray:
        """执行目标检测预测"""
        return self.monitor.process_frame(image_array)

    def visualize(self, frame: np.ndarray, out) -> np.ndarray:
        return out

    def preprocess(
        self, image_input: Union[str, Image.Image, np.ndarray]
    ) -> np.ndarray:
        """预处理图像数据以适应模型输入要求"""
        # 如果输入是文件路径，加载图像
        if isinstance(image_input, str):
            image = cv2.imread(image_input)
        elif isinstance(image_input, Path):
            image = cv2.imread(str(image_input))
        elif isinstance(image_input, Image.Image):
            image = np.array(image_input)
        else:
            image = image_input
        return image

    def postprocess(self, raw_prediction: np.ndarray) -> List[Dict[str, Any]]:
        """后处理预测结果，返回易读的检测结果"""
        return raw_prediction


class PersonConfig:
    # 模型参数
    CONFIDENCE = 0.3
    IOU = 0.4

    # 显示参数
    BOX_COLOR = (0, 255, 0)  # 边界框颜色 (BGR)
    CENTER_COLOR = (0, 0, 255)  # 中心点颜色
    TEXT_COLOR = (255, 255, 255)  # 文字颜色
    # DENSITY_COLOR = (0, 0, 255)  # 密度框颜色

    # 密度参数
    DENSITY_THRESHOLD = 8  # 建议调整范围：5-15（值越小越敏感）
    HEATMAP_ALPHA = 0.8  # 框透明度
    MIN_AREA_RATIO = 0.02  # 最小区域面积比例（相对于画面） 建议调整范围：0.01-0.1（值越小检测区域越小）

    DENSITY_LEVEL_COLORS = [  # 密度等级颜色配置（BGR格式）
        (0, 255, 0),  # 等级1 - 绿色
        (0, 165, 255),  # 等级2 - 橙色
        (0, 0, 255),  # 等级3 - 红色
    ]

    # 处理参数
    FRAME_SKIP = 1  # 跳帧处理
    SMOOTHING_WINDOW = 5  # 密度平滑窗口

    # 视频输出参数
    OUTPUT_PATH = "crowd_output/"  # 输出目录
    VIDEO_FORMAT = "mp4v"  # 视频编码格式
    SEGMENT_DURATION = 300  # 分段时长（秒）
    AUTO_SAVE = False  # 是否自动保存


class CrowdMonitor:
    def __init__(self, person_model, personConfig, predictor):
        self.predictor = predictor
        self.model = person_model
        self.personConfig = personConfig
        # 视频输出相关属性
        self.writer = None
        # 统一初始化计数器
        self.segment_counter = 1 if self.personConfig.AUTO_SAVE else 0
        self.start_time = cv2.getTickCount() if self.personConfig.AUTO_SAVE else None
        self.total_frames = 0
        self.frame_counter = 0
        # 状态变量
        self.frame_queue = deque(maxlen=self.personConfig.SMOOTHING_WINDOW)
        self.prev_count = 0

    def initialize_writer(self, width, height, fps):
        """初始化视频写入器"""
        try:
            self.frame_width = width
            self.frame_height = height
            self.fps = fps
            output_file = self._get_output_filename()
            self.writer = cv2.VideoWriter(
                output_file,
                cv2.VideoWriter_fourcc(*self.personConfig.VIDEO_FORMAT),
                self.fps,
                (self.frame_width, self.frame_height),
            )
            if not self.writer.isOpened():
                raise RuntimeError("视频写入器初始化失败")
            print(f"初始化视频写入器: {output_file}")
        except Exception as e:
            self._release_writer()
            raise RuntimeError(f"视频写入器初始化失败: {e}")

    def _get_output_filename(self):
        """生成带时间戳的分段文件名"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return os.path.join(
            self.personConfig.OUTPUT_PATH,
            f"crowd_{timestamp}_part{self.segment_counter:03d}.mp4",
        )

    def _check_segment(self):
        """检查是否需要创建新分段"""
        elapsed_time = (cv2.getTickCount() - self.start_time) / cv2.getTickFrequency()
        if elapsed_time > self.personConfig.SEGMENT_DURATION:
            self._release_writer()
            self.segment_counter += 1
            self.initialize_writer(self.frame_width, self.frame_height, self.fps)
            self.start_time = cv2.getTickCount()

    def _release_writer(self):
        if self.writer and self.writer.isOpened():
            self.writer.release()
            print(f"已释放第{self.segment_counter}段视频写入器")
        # 状态变量
        self.frame_queue = deque(maxlen=self.personConfig.SMOOTHING_WINDOW)
        self.prev_count = 0
        self.frame_queue = deque(maxlen=self.personConfig.SMOOTHING_WINDOW)
        self.prev_count = 0
        self.frame_counter = 0

    def _analyze_density(self, persons, img_shape):
        """生成密度热力图并检测高密度区域"""
        heatmap = np.zeros(img_shape[:2], dtype=np.float32)
        density_img = np.zeros((img_shape[0], img_shape[1], 3), dtype=np.uint8)

        # 生成核密度估计
        if persons:
            points = [((x1 + x2) // 2, (y1 + y2) // 2) for x1, y1, x2, y2 in persons]
            for x, y in points:
                cv2.circle(heatmap, (x, y), 30, 1, -1)

            # 高斯模糊处理
            heatmap = cv2.GaussianBlur(heatmap, (51, 51), 0)

            # 检测高密度区域
            _, thresh = cv2.threshold(
                heatmap,
                self.personConfig.DENSITY_THRESHOLD / 10,
                255,
                cv2.THRESH_BINARY,
            )
            contours, _ = cv2.findContours(
                thresh.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
            )

            min_area = img_shape[0] * img_shape[1] * self.personConfig.MIN_AREA_RATIO
            for cnt in contours:
                if cv2.contourArea(cnt) > min_area:
                    x, y, w, h = cv2.boundingRect(cnt)
                    # 根据密度值确定颜色等级
                    density_value = heatmap[y : y + h, x : x + w].mean()
                    print(f"当前区域密度值: {density_value:.4f}")
                    if 0.5 >= density_value > 0.3:
                        color_index = 0
                    elif 0.7 >= density_value > 0.5:
                        color_index = 1
                    else:
                        color_index = 2
                    # cv2.rectangle(density_img, (x,y), (x+w,y+h), personConfig.DENSITY_COLOR, 2)
                    # color_index = min(int(density_value * 200), len(personConfig.DENSITY_LEVEL_COLORS)-1)
                    cv2.rectangle(
                        density_img,
                        (x, y),
                        (x + w, y + h),
                        self.personConfig.DENSITY_LEVEL_COLORS[color_index],
                        2,
                    )
                    cv2.putText(
                        density_img,
                        f"Lv{color_index+1}-{density_value:.4f}",
                        (x + 5, y + 20),
                        cv2.FONT_HERSHEY_SIMPLEX,
                        0.6,
                        self.personConfig.DENSITY_LEVEL_COLORS[color_index],
                        2,
                    )
                    print(f"警示区域: x={x}, y={y}, w={w}, h={h}, 等级={color_index+1}")
                    # 调用AI钩子接口
                    timestamp = time.time() 
                    # self.predictor.call_ai_hook_interface(
                    #     self.predictor.task.callback_url, density_img, timestamp
                    # )
        return density_img

    def _dynamic_params(self, img, persons):
        """动态调整参数"""
        # 根据亮度调整显示参数
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        avg_intensity = np.mean(gray)
        text_size = 0.5 + avg_intensity / 255  # 动态文字大小
        line_width = max(1, int(avg_intensity / 100))  # 动态线宽

        return text_size, line_width

    def process_frame(self, img):
        """处理单帧图像"""
        # 跳帧处理
        # self.frame_counter += 1
        # if self.frame_counter % self.personConfig.FRAME_SKIP != 0:
        #     return img, self.prev_count

        # 目标检测
        result = self.model.predict(img)[0]

        # 解析检测结果
        persons = []
        for box in result.boxes:
            x1, y1, x2, y2 = map(int, box.xyxy[0].cpu().numpy())
            persons.append((x1, y1, x2, y2))

        # 时间域平滑
        current_count = len(persons)
        self.frame_queue.append(current_count)
        smoothed_count = int(np.mean(self.frame_queue))
        self.prev_count = smoothed_count

        # 可视化标注
        text_size, line_width = self._dynamic_params(img, persons)
        display_img = img.copy()

        # 绘制检测框和中心点
        for x1, y1, x2, y2 in persons:
            # 绘制边界框
            # cv2.rectangle(
            #     display_img,
            #     (x1, y1), (x2, y2),
            #     personConfig.BOX_COLOR,
            #     line_width
            # )

            # 绘制中心点
            center_x = (x1 + x2) // 2
            center_y = (y1 + y2) // 2
            cv2.circle(
                display_img,
                (center_x, center_y),
                4,  # 点半径
                self.personConfig.CENTER_COLOR,
                -1,  # 实心圆
            )

        # 显示统计信息
        cv2.putText(
            display_img,
            f"Persons: {smoothed_count}",
            (20, 50),
            cv2.FONT_HERSHEY_SIMPLEX,
            text_size,
            self.personConfig.TEXT_COLOR,
            2,
        )

        # 密度区域分析
        density_img = self._analyze_density(persons, img.shape)
        display_img = cv2.addWeighted(
            display_img, 1, density_img, self.personConfig.HEATMAP_ALPHA, 0
        )

        # 视频写入逻辑
        if self.personConfig.AUTO_SAVE:
            if not self.writer:
                self.initialize_writer(img.shape[1], img.shape[0], 25)  # 默认25fps
            try:
                # self._check_segment()
                self.writer.write(display_img)
                self.total_frames += 1
            except Exception as e:
                print(f"视频写入失败: {e}")
                self._release_writer()
        elif self.writer:
            self._release_writer()

        return display_img
