
from typing import Union, Any, Dict, List, Tuple
from PIL import Image
import cv2
import numpy as np
from hashlib import md5
from pathlib import Path
from PIL import Image, ImageFont, ImageDraw
from predict_method.base_predict import BasePredictor
from utils.image_util import cv2AddChineseText

from runtime import EngineModelRuntime
import cv2 as cv



class MotorHelmetEngine(BasePredictor):
    def _initialize_from_config(self):
        """从配置文件初始化目标检测模型和相关参数"""
        # 初始化模型参数
        self.confidence_threshold = self.config.get('confidence_threshold', 0.5)
        self.predict_params = self.config.get('predict_params', {})
        self.input_size = tuple(self.config['input_size'])  # 期望的输入尺寸 (height, width)
        self.classes = self.config['classes']  # 类别名称列表
        self.session = EngineModelRuntime(self._get_model_file().read(),input_size=self.input_size,predict_params=self.predict_params)
        print(f"Initialized detector with: {self.config}")

    def preprocess(self, image_input: Union[str, Image.Image, np.ndarray]) -> np.ndarray:
        """预处理图像数据以适应模型输入要求"""
        # 如果输入是文件路径，加载图像
        if isinstance(image_input, str):
            image = cv.imread(image_input)
        elif isinstance(image_input, Path):
            image = cv.imread(str(image_input))
        elif isinstance(image_input, Image.Image):
            image =  np.array(image_input)  
           
        else:
            image = image_input
        # self.img_height, self.img_width = image.shape[:2]
        #         # 将图像颜色空间从 BGR 转换为 RGB
        # img_rgb = cv.cvtColor(image, cv.COLOR_BGR2RGB)
 
        # # 保持宽高比，进行 letterbox 填充, 使用模型要求的输入尺寸
        # img, self.ratio, (self.dw, self.dh) = self.letterbox(img_rgb, new_shape=(self.session.input_width, self.session.input_height))
        return image
    
    def callback(self, task, processed_frame, predictions, timestamp,original_frame):
        if len(predictions) ==0:
            return
        import time
        if not hasattr(self, '_last_call_time'):
            self._last_call_time = 0
            
        current_time = time.time()  # 获取当前系统时间（秒）
        if current_time - self._last_call_time >= 3:
            self.call_ai_hook_interface(task.callback_url,processed_frame, timestamp,task.latitude,task.longitude)
            self._last_call_time = current_time
            print(task.callback_url)

    def predict(self, image_array: np.ndarray) -> np.ndarray:
        """执行目标检测预测"""
        #return [{}]
        return self.session.predict(image_array)

    # def visualize(self, frame: np.ndarray,out) -> np.ndarray:
    #     """执行目标检测预测"""
    #     output_frame = frame.copy()
    #     # 自定义绘制每个检测框
    #     for i, box in enumerate(out):
    #         x1, y1, x2, y2 = map(int, box['bbox'])
            
    #         # 框颜色和样式
    #         color = (0, 0, 255)  # 绿色
    #         thickness = 2
            
    #         # 绘制边界框
    #         cv.rectangle(output_frame, (x1, y1), (x2, y2), color, thickness)
            
    #         # 置信度和类别标签
    #         #label = f"{results[0].names[int(cls[i])]} {conf[i]:.2f}"
            
    #         # 计算文本背景
    #         (text_width, text_height), _ = cv.getTextSize(str(box['class_name']), cv.FONT_HERSHEY_SIMPLEX, 0.5, 1)
    #         cv.rectangle(output_frame, (x1, y1 - text_height - 10), (x1 + text_width, y1), color, -1)
    #         output_frame=cv2AddChineseText(output_frame,str(box['class_name']),(x1+8, y1-text_height-8),(255, 255, 255),16)
    #     return output_frame


    def visualize(self, frame: np.ndarray, out) -> np.ndarray:
        """目标检测结果可视化后处理函数（支持中文显示）
        Args:
            frame: 原始图像 (H, W, C)
            out: 检测结果列表，每个元素为包含以下键的字典:
                'bbox': [x1, y1, x2, y2] 边界框坐标
                'class_name': 类别名称字符串
                'confidence': 置信度浮点数
        Returns:
            绘制了检测结果的图像
        """
        # 可调整参数区域
        font_path = 'simsun.ttc'  # 中文字体路径
        font_size = 20            # 字体大小
        text_offset = 5           # 文本与框的垂直偏移量
        bg_padding = 5            # 文本背景的水平内边距
        bg_alpha = 0.6            # 文本背景透明度
        bbox_thickness = 2        # 边界框线宽
        
        # 创建图像副本并转换为PIL格式
        h, w = frame.shape[:2]
        pil_img = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(pil_img, 'RGBA')  # 使用RGBA模式支持透明度
        
        # 尝试加载字体
        try:
            font = ImageFont.truetype(font_path, font_size)
        except IOError:
            font = ImageFont.load_default()  # 字体加载失败时使用默认字体
        
        for i, box in enumerate(out):
            # 解析检测结果
            x1, y1, x2, y2 = map(int, box['bbox'])
            class_name = str(box['class_name'])
            confidence = float(box.get('confidence', 0.0))
            
            # 确保坐标在图像范围内
            x1, y1 = max(0, x1), max(0, y1)
            x2, y2 = min(w-1, x2), min(h-1, y2)
            
            # 通过哈希生成类别固定颜色
            # hash_val = int(md5(class_name.encode()).hexdigest()[:6], 16)
            color = (255,53,94)
            
            # 使用PIL绘制边界框[7](@ref)[8](@ref)
            draw.rectangle(
                [(x1, y1), (x2, y2)],
                outline=color,
                width=bbox_thickness
            )
            
            # 准备文本标签
            text = f"{class_name} {confidence:.2f}" if confidence > 0 else class_name
            
            # 获取文本尺寸[10](@ref)[12](@ref)
            bbox = font.getbbox(text)
            text_w = bbox[2] - bbox[0]  # 宽度 = right - left
            text_h = bbox[3] - bbox[1]  # 高度 = bottom - top
            
            # 计算文本背景尺寸
            bg_width = text_w + 2 * bg_padding
            bg_height = text_h + 2 * bg_padding
            
            # 确定文本垂直位置（优先在框上方）[9](@ref)
            if y1 - text_offset - bg_height >= 0:  # 上方有足够空间
                bg_y1 = y1 - text_offset - bg_height
            else:  # 上方空间不足，放在框下方
                bg_y1 = y2 + text_offset
            
            # 确定文本水平位置（防止超出图像边界）
            if x1 + bg_width > w:  # 右侧会超出
                bg_x1 = max(0, w - bg_width)  # 向左移动
            else:
                bg_x1 = x1  # 保持与框左对齐
            
            # 确保文本背景在图像范围内
            bg_x1 = max(0, bg_x1)
            bg_y1 = max(0, bg_y1)
            bg_x2 = min(w, bg_x1 + bg_width)
            bg_y2 = min(h, bg_y1 + bg_height)
            
            # 文本位置（在背景中央）
            text_x = bg_x1 + bg_padding
            text_y = bg_y1 + bg_padding
            
            # 绘制半透明文本背景[10](@ref)
            draw.rectangle(
                [bg_x1, bg_y1, bg_x2, bg_y2],
                fill=(255,53,94 , int(255 * bg_alpha)))  # 白色半透明背景
            
            # 绘制文本（确保在背景内）[10](@ref)[12](@ref)
            draw.text(
                (text_x, text_y), 
                text, 
                font=font, 
                fill=(0, 0, 0)  # 黑色文本
            )
        
        # 将PIL图像转换回OpenCV格式
        output_frame = cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)
        return output_frame





    def postprocess(self, raw_prediction: np.ndarray) -> List[Dict[str, Any]]:
        """后处理预测结果，返回易读的检测结果"""
        detections = []
        for result in raw_prediction:
            for box in result.boxes:
                # 获取坐标 (x1, y1, x2, y2)
                x1, y1, x2, y2 = box.xyxy[0].tolist()
                
                # 获取置信度和类别ID
                conf = box.conf.item()
                cls_id = box.cls.item()
                # 构建检测结果字典
                detection = {
                    'bbox': [float(x1), float(y1), float(x2), float(y2)],
                    'conf': conf,
                    'class_id': int(cls_id),
                    'class_name': self.classes[int(cls_id)]  # 使用 model.names 获取类别名称
                }
                detections.append(detection)
        return detections
