from collections import defaultdict
from typing import Union, Any, Dict, List

import cv2
import torch
from PIL import Image
import numpy as np
from pathlib import Path

from predict_method.base_predict import BasePredictor
from utils.image_util import cv2AddChineseText
from runtime import EngineModelRuntime
from lib.deep_sort.deep_sort import DeepSort

class CarFlowCountEngine(BasePredictor):
    def _initialize_from_config(self):
        """从配置文件初始化目标检测模型和相关参数"""
        # 初始化模型参数
        self.confidence_threshold = self.config.get('confidence_threshold', 0.5)
        self.predict_params = self.config.get('predict_params', {})
        self.input_size = tuple(self.config['input_size'])  # 期望的输入尺寸 (height, width)
        # self.input_size_river = tuple(self.config['input_size_river'])  # 期望的输入尺寸 (height, width)
        self.classes_people = self.config['classes']  # 类别名称列表
        # self.classes_river = self.config['classes_river']  # 类别名称列表
        self.car_model = EngineModelRuntime(self._get_file_by_key('model_filename'), input_size=self.input_size,
                                          predict_params=self.predict_params,num_workers=1)


        # self.river_model=EngineModelRuntime(self._get_file_by_key('river_model_filename'),input_size=self.input_size_river,
        #                                     predict_params=self.predict_params,task='segment')

        self.car_flow_count = CarFlowCountEngine_main(car_model=self.car_model)

        # print(f"Initialized detector with: {self.config}")


    def preprocess(self, image_input: Union[str, Image.Image, np.ndarray]) -> np.ndarray:
        """预处理图像数据以适应模型输入要求"""
        # 如果输入是文件路径，加载图像
        if isinstance(image_input, str):
            image = cv2.imread(image_input)
        elif isinstance(image_input, Path):
            image = cv2.imread(str(image_input))
        elif isinstance(image_input, Image.Image):
            image = np.array(image_input)

        else:
            image = image_input
        # self.img_height, self.img_width = image.shape[:2]
        #         # 将图像颜色空间从 BGR 转换为 RGB
        # img_rgb = cv.cvtColor(image, cv.COLOR_BGR2RGB)

        # # 保持宽高比，进行 letterbox 填充, 使用模型要求的输入尺寸
        # img, self.ratio, (self.dw, self.dh) = self.letterbox(img_rgb, new_shape=(self.session.input_width, self.session.input_height))
        return image

    def callback(self, task, processed_frame, predictions, timestamp,original_frame):
        detections=[]
        # class_ids=[]
        for result in predictions:
            for box in result.boxes:
                # 获取坐标 (x1, y1, x2, y2)
                x1, y1, x2, y2 = box.xyxy[0].tolist()

                # 获取置信度和类别ID
                conf = box.conf.item()
                cls_id = box.cls.item()
                if cls_id in [1,2,3,6]:
                    # class_ids.append(int(cls_id))
                # 构建检测结果字典
                    detection = {
                        'bbox': [float(x1), float(y1), float(x2), float(y2)],
                        'conf': conf,
                        'class_id': int(cls_id),
                        # 'class_name': self.classes[int(cls_id)]  # 使用 model.names 获取类别名称
                    }
                    detections.append(detection)
        # print(f'clsss_ids:{class_ids}')
        # print(f'len:{len(class_ids)}')
        #
        # print(f'detections:{detections}')
        # print(f'len:{len(detections)}')

        if len(detections) == 0:
            return
        import time
        if not hasattr(self, '_last_call_time'):
            self._last_call_time = 0

        current_time = time.time()  # 获取当前系统时间（秒）
        if current_time - self._last_call_time >= 3:
            s=self.call_ai_hook_interface(task.callback_url, processed_frame, timestamp, task.latitude, task.longitude,params={'car_num':len(detections)})
            self._last_call_time = current_time
            # print(f'回调成功:{s}')

    def predict(self, image_array: np.ndarray) -> np.ndarray:
        """执行目标检测预测"""
        # return [{}]
        results = self.car_model.track(
            image_array
        )

        return results

    def visualize(self, frame: np.ndarray, out) -> np.ndarray:
        results=out
        out_img = self.car_flow_count.predict_main(frame,results)
        # cv2.imshow('nn', frame)
        # cv2.waitKey(1)
        # cv2.destroyAllWindows()
        return out_img

    def postprocess(self, raw_prediction: np.ndarray) -> List[Dict[str, Any]]:
        results=raw_prediction
        """后处理预测结果，返回易读的检测结果"""
        # detections = []
        # for result in raw_prediction:
        #     for box in result.boxes:
        #         # 获取坐标 (x1, y1, x2, y2)
        #         x1, y1, x2, y2 = box.xyxy[0].tolist()
        #
        #         # 获取置信度和类别ID
        #         conf = box.conf.item()
        #         cls_id = box.cls.item()
        #         # 构建检测结果字典
        #         detection = {
        #             'bbox': [float(x1), float(y1), float(x2), float(y2)],
        #             'conf': conf,
        #             'class_id': int(cls_id),
        #             'class_name': self.classes[int(cls_id)]  # 使用 model.names 获取类别名称
        #         }
        #         detections.append(detection)
        return results


class CarFlowCountEngine_main:
    def __init__(self,car_model):

        self.car_model = car_model
        self.CLASS_IDS = [0, 1, 2, 3, 4, 5, 6, 7]  # 8个检测类别
        # 类别名称映射
        self.CLASS_NAMES = {
            0: '自行车', 1: '车', 2: '面包车', 3: '卡车',
            4: '三轮车', 5: '带蓬三轮车', 6: '公交车', 7: '摩托车'
        }
        # ==== 多类别计数系统 ====
        self.GLOBAL_ID_COUNTER = 0  # 全局ID计数器
        self.correction_count = 0  # 类别修正计数器

        # 初始化各类别的总计数和当前帧计数
        self.total_counts = [0] * 8  # 每个类别的总计数
        self.current_counts = [0] * 8  # 当前帧各类别计数

        # 跟踪ID状态管理（新增class_change_count字段）
        self.track_history = defaultdict(lambda: {
            'first_detected': None,  # 首次检测到的帧号
            'last_seen': None,  # 最后出现的帧号
            'consecutive_count': 0,  # 连续出现的帧数
            'confirmed': False,  # 是否已确认为有效目标
            'global_id': None,  # 分配的全局ID
            'confirmed_class': None,  # 确认后的最终类别
            'class_votes': defaultdict(int),  # 类别投票计数器
            'last_class': None,  # 当前帧检测类别
            'class_change_count': 0  # 类别变化计数器
        })
        # 帧计数器
        self.current_frame_count = 0

        self.class_color_dict={
            0:(4, 42, 255),
            1:(11, 219, 235),
            2:(0, 243, 68),
            3:(0, 223, 183),
            4:(17, 31, 104),
            5:(255, 111, 221),
            6:(255, 68, 79),
            7:(204, 237, 0)
        }
        # self.fourcc = cv2.VideoWriter_fourcc(*'XVID')
        # self.out = cv2.VideoWriter(r'E:\A_worker\W_XM\ZLT_api_test\modelManger_deply\test_video\out.avi', self.fourcc, 30, (3840, 2160))#3840x2160
    def predict_main(self, frame,results):
        self.current_frame_count += 1
        self.current_counts = [0] * 8  # 重置当前帧各类别计数

        # ==== 使用YOLO内置的BoT-SORT进行跟踪 ====


        # 获取跟踪结果
        if results[0].boxes.id is not None:
            track_ids = results[0].boxes.id.cpu().numpy().astype(int)
            boxes = results[0].boxes.xyxy.cpu().numpy()
            class_ids = results[0].boxes.cls.cpu().numpy().astype(int)
        else:
            track_ids = []
            boxes = []
            class_ids = []

        # ==== 更新跟踪状态和多类别计数 ====
        active_ids = set()

        # 遍历所有检测到的目标
        for i, box in enumerate(boxes):
            track_id = track_ids[i]
            active_ids.add(track_id)
            x1, y1, x2, y2 = map(int, box[:4])
            class_id = class_ids[i]

            # 初始化新跟踪ID的状态
            if track_id not in self.track_history:
                self.track_history[track_id] = {
                    'first_detected': self.current_frame_count,
                    'last_seen': self.current_frame_count,
                    'consecutive_count': 1,
                    'confirmed': False,
                    'global_id': None,
                    'confirmed_class': None,
                    'class_votes': defaultdict(int),
                    'last_class': class_id,
                    'class_change_count': 0
                }
                self.track_history[track_id]['class_votes'][class_id] = 1
            else:
                # 更新现有跟踪ID的状态
                data = self.track_history[track_id]
                last_seen = data['last_seen']
                frame_diff = self.current_frame_count - last_seen

                # 检查是否连续出现（允许最多丢失1帧）
                if frame_diff <= 2:
                    data['consecutive_count'] += 1
                else:
                    data['consecutive_count'] = 1

                data['last_seen'] = self.current_frame_count
                data['last_class'] = class_id

                # 更新类别投票（仅未确认目标）
                if not data['confirmed']:
                    data['class_votes'][class_id] += 1

                # 检查是否满足连续5帧的条件
                if (not data['confirmed'] and data['consecutive_count'] >= 5):
                    # ==== 类别投票决策逻辑 ====
                    max_vote = max(data['class_votes'].values())
                    candidate_classes = [
                        cid for cid, count in data['class_votes'].items()
                        if count == max_vote
                    ]

                    if len(candidate_classes) == 1:
                        final_class = candidate_classes[0]
                    else:
                        final_class = data['last_class']

                    # 更新总计数
                    self.total_counts[final_class] += 1

                    # 更新目标状态
                    data['confirmed'] = True
                    data['confirmed_class'] = final_class
                    data['global_id'] = self.GLOBAL_ID_COUNTER
                    self.GLOBAL_ID_COUNTER += 1
                    # print(f"New target confirmed! Class: {self.CLASS_NAMES[final_class]}, "
                    #       f"Global ID: {self.GLOBAL_ID_COUNTER}, "
                    #       f"Total count: {self.total_counts[final_class]}")

            # 更新当前帧计数
            if self.track_history[track_id]['confirmed']:
                target_class = self.track_history[track_id]['confirmed_class']
                self.current_counts[target_class] += 1
            else:
                self.current_counts[class_id] += 1

            # 获取显示ID和类别
            display_id = self.track_history[track_id]['global_id'] or f"T-{track_id}"
            display_class = (self.track_history[track_id]['confirmed_class']
                             if self.track_history[track_id]['confirmed']
                             else class_id)
            class_name = self.CLASS_NAMES.get(display_class, f'Class {display_class}')

            # 绘制边界框和ID
            # color = (0, 255, 0)  # 绿色边框
            text=f'{class_name}：{display_id}'
            # text_2=f'{display_id}'
            # # 计算文本背景
            (text_width, text_height), _ = cv2.getTextSize(str(text), cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)
            text_width=len(text)*17
            # print(f'{text}---{text_width}, {text_height}{(x1+10 + text_width)-x1}')

            frame=cv2.rectangle(frame, (x1, y1 - text_height - 13), (x1 + text_width, y1-3), self.class_color_dict[class_id], -1)
            frame = cv2AddChineseText(frame, str(text), (x1 + 8, y1 - text_height - 8),
                                             (255, 255, 255), 16,4)
            cv2.rectangle(frame, (x1, y1), (x2, y2), self.class_color_dict[class_id], 2)
            # frame=cv2AddChineseText(frame, f'{class_name}: {display_id}', (x1, y1 - 10),
            #             color,0.75,  2)

        # ==== 类别修正逻辑 ====
        for track_id in active_ids:
            data = self.track_history[track_id]
            if not data['confirmed']:
                continue

            current_class = data['last_class']
            confirmed_class = data['confirmed_class']

            # 检测到类别变化
            if current_class != confirmed_class:
                data['class_change_count'] += 1

                # 连续5帧类别不一致触发修正
                if data['class_change_count'] >= 5:
                    self.total_counts[confirmed_class] -= 1
                    self.total_counts[current_class] += 1
                    data['confirmed_class'] = current_class
                    self.correction_count += 1
                    # print(f"类别修正: ID{track_id} {self.CLASS_NAMES[confirmed_class]}→{self.CLASS_NAMES[current_class]}")

                    # 重置变化计数器
                    data['class_change_count'] = 0
            else:
                # 类别一致时重置计数器
                data['class_change_count'] = 0

        # ==== 清理长时间未出现的跟踪ID ====
        MAX_DISAPPEAR_FRAMES = 50  # 约6-8秒超时
        to_remove = []
        for track_id, data in self.track_history.items():
            if (self.current_frame_count - data['last_seen'] > MAX_DISAPPEAR_FRAMES and
                    track_id not in active_ids):
                to_remove.append(track_id)

        for track_id in to_remove:
            # print(f"BoT-SORT ID {track_id} removed due to timeout")
            del self.track_history[track_id]

        height,width = frame.shape[0],frame.shape[1]
        # ==== 在画面上绘制统计信息 ====
        # 左边统计区域
        stats_left = {
            'start_x': width - 300,
            'start_y': 10,
            'width': 140,
            'line_height': 20
        }

        # 右边统计区域
        stats_right = {
            'start_x': width - 150,
            'start_y': 10,
            'width': 150,
            'line_height': 20
        }

        # 绘制半透明背景（左边）
        overlay = frame.copy()
        cv2.rectangle(overlay,
                      (stats_left['start_x'] - 10, stats_left['start_y'] - 40),
                      (stats_left['start_x'] + stats_left['width'],
                       stats_left['start_y'] + stats_left['line_height'] * (len(self.CLASS_IDS) + 1)),
                      (0, 0, 0), -1)
        alpha = 0.4
        frame = cv2.addWeighted(overlay, alpha, frame, 1 - alpha, 0)

        # 绘制半透明背景（右边）
        overlay = frame.copy()
        cv2.rectangle(overlay,
                      (stats_right['start_x'] - 10, stats_right['start_y'] - 40),
                      (stats_right['start_x'] + stats_right['width'],
                       stats_right['start_y'] + stats_right['line_height'] * (len(self.CLASS_IDS) + 1)),
                      (0, 0, 0), -1)
        frame = cv2.addWeighted(overlay, alpha, frame, 1 - alpha, 0)

        # ==== 显示多类别统计结果 ====
        # 左上角：当前帧各类别数量
        frame=cv2AddChineseText(frame, '实时计数:',
                    (stats_left['start_x'], stats_left['start_y']),
                     (255, 255, 255),16,4)

        # 右上角：各类别总数量
        frame=cv2AddChineseText(frame, '累计计数:',
                    (stats_right['start_x'], stats_right['start_y']),
                      (255, 255, 255), 16,4)

        # 分两列显示统计数据
        for i in range(8):
            class_name = self.CLASS_NAMES[i]

            # 左边：实时计数
            if i < 4:
                y_pos = stats_left['start_y'] + (i + 1) * stats_left['line_height']
                # text = f"{class_name}: {self.current_counts[i]}"
                text=f'{class_name:<5}{str(self.current_counts[i]):>3}'
                frame=cv2AddChineseText(frame, str(text),
                            (stats_left['start_x'], y_pos),
                             (255, 255, 255),16,4)
            else:
                y_pos = stats_left['start_y'] + (i - 3) * stats_left['line_height'] + 80
                # text = f"{class_name}: {self.current_counts[i]}"
                text = f'{class_name:<5}{str(self.current_counts[i]):>3}'
                frame=cv2AddChineseText(frame, str(text),
                            (stats_left['start_x'], y_pos),
                            (255, 255, 255), 16,4)

            # 右边：累计计数
            if i < 4:
                y_pos = stats_right['start_y'] + (i + 1) * stats_right['line_height']
                # text = f"{class_name}: {self.total_counts[i]}"
                text = f'{class_name:<5}{str(self.total_counts[i]):>3}'
                frame=cv2AddChineseText(frame, str(text),
                            (stats_right['start_x'], y_pos),
                             (255, 255, 255), 16,4)
            else:
                y_pos = stats_right['start_y'] + (i - 3) * stats_right['line_height'] + 80
                # text = f"{class_name}: {self.total_counts[i]}"
                text = f'{class_name:<5}{str(self.total_counts[i]):>3}'
                frame=cv2AddChineseText(frame, str(text),
                            (stats_right['start_x'], y_pos),
                            (255, 255, 255), 16,4)

        # ==== 显示帧和写入视频 ====
        # print(f'车流量计数已识别{self.current_frame_count}帧')
        # cv2.imwrite(f'E:/A_worker/W_XM/ZLT_api_test/modelManger_deply/test_video/test{self.current_frame_count}.png',frame)
        # self.out.write(frame)
        return frame






if __name__ == "__main__":
    model_path = "/home/<USER>/project/carTrack/people_dr/train7/weights/best.onnx"
    obj = ObjectDetector('/home/<USER>/project/modelManger/models/poeple/config.yaml')
    print(obj.predict('/home/<USER>/project/carTrack/people_v4/images/Train/frame_000030.png'))