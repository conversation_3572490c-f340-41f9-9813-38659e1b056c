
from typing import Union, Any, Dict, List, Tuple
from PIL import Image
import numpy as np
from pathlib import Path

from collections import defaultdict, deque
from predict_method.base_predict import BasePredictor
from utils.image_util import cv2AddChineseText

from runtime import EngineModelRuntime
import cv2 
import time

class CarDoorEngine(BasePredictor):
    car = None
    def _initialize_from_config(self):
        """从配置文件初始化目标检测模型和相关参数"""
        # 初始化模型参数
        self.input_size = tuple(self.config['input_size'])  # 期望的输入尺寸 (height, width)
        self.car = EngineModelRuntime(self._get_model_file().read(),num_workers=1,task='pose')
        self.person = EngineModelRuntime(self._get_file_by_key('person_model').read(),num_workers=1)
        self.monitor = DoorContactMonitor(
        person_model=self.person,  # 人体检测模型
        #person_model_path="yolo11x.pt",
        vehicle_model= self.car,  # 车辆姿态模型
        base_contact_thresh=150,
        alert_thresh=2,
        contact_cooldown=2,#2秒内不同车辆拉的车门只计算为1次
    )
        self.f = 0
        print(f"Initialized detector with: {self.config}")

    def preprocess(self, image_input: Union[str, Image.Image, np.ndarray]) -> np.ndarray:
        """预处理图像数据以适应模型输入要求"""
        # 如果输入是文件路径，加载图像
        if isinstance(image_input, str):
            image = cv2.imread(image_input)
        elif isinstance(image_input, Path):
            image = cv2.imread(str(image_input))
        elif isinstance(image_input, Image.Image):
            image =  np.array(image_input)  
           
        else:
            image = image_input
        return image
    


    def predict(self, image_array: np.ndarray) -> np.ndarray:
        """执行目标检测预测"""
        #return [{}]
        self.f += 1
        frame = image_array
        if(self.f%5==0):
            frame= self.monitor.process_frame(frame)
        frame=self.monitor._visualize_alerts(frame)
        return  frame

    def visualize(self, frame: np.ndarray,out) -> np.ndarray:
        return out



    def postprocess(self, raw_prediction: np.ndarray) -> List[Dict[str, Any]]:
        """后处理预测结果，返回易读的检测结果"""
        return raw_prediction



































   
class VehiclePoseTracker:
    def __init__(self, model):
        """初始化车辆姿态跟踪模型"""
        self.model = model
        self.track_history = defaultdict(lambda: [])
        # 关键点索引映射
        self.keypoint_ids = {
            0: 'front',
            1: 'front_left_handle',
            2: 'front_right_handle', 
            3: 'rear_left_handle',
            4: 'rear_right_handle',
            5: 'rear'
        }
    
    def __call__(self, image: np.ndarray) -> List[Dict]:
        """执行车辆姿态跟踪"""
        results = self.model.track(image)
        
        detections = []
        if results[0].boxes.id is not None:
            for box, kpts, track_id in zip(results[0].boxes, 
                                         results[0].keypoints, 
                                         results[0].boxes.id):
                detections.append({
                    'id': int(track_id.item()),
                    'bbox': box.xyxy[0].cpu().numpy().tolist(),
                    'keypoints': kpts.xy[0].cpu().numpy().tolist(),
                    'conf': box.conf.item()
                })
        return detections

class DoorContactMonitor:
    def __init__(self,
                 person_model: str = "yolov8n.pt",
                 vehicle_model: str = "yolov8s-vehicle-pose.pt",
                 base_contact_thresh: int = 100,  # 基础接触阈值(像素)
                 alert_thresh: int = 2,
                 time_window: int = 60,
                contact_cooldown: float = 1.0):
        """初始化监控系统"""
        # 加载模型
        self.person_tracker = person_model
        self.vehicle_tracker = VehiclePoseTracker(vehicle_model)
        
        # 配置参数
        self.base_contact_thresh = base_contact_thresh
        self.alert_threshold = alert_thresh
        self.time_window = time_window
        self.contact_cooldown = contact_cooldown
        
        # 数据记录
        self.contact_records = defaultdict(lambda: defaultdict(lambda: defaultdict(deque)))
        self.alerts = []
        self.frame_count = 0
        self.last_alert_time = defaultdict(float)
    
    def process_frame(self, frame: np.ndarray) -> np.ndarray:
        """处理单帧图像"""
        self.frame_count += 1
        current_time = time.time()
        self.last_frame = frame.copy()
        
        # 1. 人体检测
        person_results = self.person_tracker.track(frame)
        persons = self._parse_person_results(person_results)
        
        # 2. 车辆姿态检测
        vehicles = self.vehicle_tracker(frame)
        
        # 3. 更新接触记录
        self._update_contacts(persons, vehicles, current_time)
        
        # 4. 可视化结果
        return self._visualize(frame, persons, vehicles)
    
    def _parse_person_results(self, results):
        """解析人体检测结果"""
        persons = []
        if results[0].boxes.id is not None:
            for box, track_id in zip(results[0].boxes, results[0].boxes.id):
                bbox = box.xyxy[0].cpu().numpy()
                persons.append({
                    'id': int(track_id.item()),
                    'bbox': bbox.tolist(),
                    'pixel_height': bbox[3] - bbox[1],
                    'conf': box.conf.item(),
                    'center': [(bbox[0]+bbox[2])/2, (bbox[1]+bbox[3])/2]  # 改为边界框中心点
                })
        return persons
    
    def _update_contacts(self, persons, vehicles, timestamp):
        """更新接触状态"""
        self._cleanup_old_records(timestamp)
        
        for person in persons:
            # 动态阈值基于人体高度
            dynamic_thresh = self._get_dynamic_threshold(person['pixel_height'])
            
            for vehicle in vehicles:
                # 检查人员与车门的接触
                contact_info = self._check_person_contact(
                    person, vehicle, dynamic_thresh)
                
                if contact_info['door_id'] > 0:
                    self._record_contact(
                        person['id'], 
                        vehicle['id'], 
                        contact_info['door_id'], 
                        timestamp)
                    
                    # 检查是否需要触发警报
                    self._check_for_alerts(person['id'], timestamp)
    
    def _get_dynamic_threshold(self, pixel_height: float) -> float:
        """基于人体高度的动态阈值"""
        base_height = 180
        scale_factor = pixel_height / base_height
        return self.base_contact_thresh * scale_factor
    
    def _check_person_contact(self, person, vehicle, threshold) -> Dict:
        """
        检查人员与车门的接触
        返回: {
            'door_id': 接触的车门ID(0表示无接触), 
            'distance': 实际距离
        }
        """
        min_distance = float('inf')
        contact_info = {'door_id': 0, 'distance': float('inf')}
        
        # 使用人员底部中心点作为接触点
        person_point = person['center']
        
        # 检查所有车门把手关键点
        for door_idx in range(1, 5):  # 车门把手关键点索引为1-4
            door_pos = vehicle['keypoints'][door_idx]
            
            # 计算人员底部中心点到车门把手的距离
            dist = np.sqrt((person_point[0]-door_pos[0])**2 + (person_point[1]-door_pos[1])**2)
            if dist < threshold and dist < min_distance:
                min_distance = dist
                contact_info = {
                    'door_id': door_idx,
                    'distance': dist
                }
        
        return contact_info
    
    def _cleanup_old_records(self, current_time):
        """清理旧记录"""
        for pid in list(self.contact_records.keys()):
            for vid in list(self.contact_records[pid].keys()):
                for did in list(self.contact_records[pid][vid].keys()):
                    while (len(self.contact_records[pid][vid][did]) > 0 and 
                           current_time - self.contact_records[pid][vid][did][0]['timestamp'] > self.time_window):
                        self.contact_records[pid][vid][did].popleft()
                    
                    if len(self.contact_records[pid][vid][did]) == 0:
                        del self.contact_records[pid][vid][did]
                
                if len(self.contact_records[pid][vid]) == 0:
                    del self.contact_records[pid][vid]
            
            if len(self.contact_records[pid]) == 0:
                del self.contact_records[pid]
    
    def _record_contact(self, person_id, vehicle_id, door_id, timestamp):
        """记录接触事件"""
        records = self.contact_records[person_id][vehicle_id][door_id]
        if(records):
            print(timestamp - records[-1]['timestamp'])
        if records and (timestamp - records[-1]['timestamp']) < self.contact_cooldown:
            
            return
        
        records.append({
            'timestamp': timestamp,
            'frame': self.frame_count
        })
    
    def _check_for_alerts(self, person_id, timestamp):
        """检查是否需要触发警报"""
        if timestamp - self.last_alert_time.get(person_id, 0) < 5:
            return
        
        unique_vehicles = set()  # 记录接触的不同车辆
        contact_count = 0
        last_contact_time = defaultdict(float)
        
        for vid in self.contact_records[person_id]:
            vehicle_contact = False  # 标记这辆车是否有过接触
            
            for did in self.contact_records[person_id][vid]:
                valid_contacts = []
                for r in self.contact_records[person_id][vid][did]:
                    if (timestamp - r['timestamp'] <= self.time_window and 
                        (r['timestamp'] - last_contact_time[vid] >= self.contact_cooldown)):
                        valid_contacts.append(r)
                        last_contact_time[vid] = r['timestamp']
                
                if valid_contacts:
                    vehicle_contact = True
                    contact_count += len(valid_contacts)
            
            # 只要这辆车有任何车门被接触过，就计为一次车辆接触
            if vehicle_contact:
                unique_vehicles.add(vid)
        
        # 只有当接触了多辆不同车时才触发警报
        if len(unique_vehicles) >= self.alert_threshold:
            last_contact = None
            for vid in self.contact_records[person_id]:
                for did in self.contact_records[person_id][vid]:
                    for record in self.contact_records[person_id][vid][did]:
                        if last_contact is None or record['timestamp'] > last_contact['timestamp']:
                            last_contact = record
                            last_contact.update({
                                'vehicle_id': vid,
                                'door_id': did
                            })
            
            if last_contact:
                self._trigger_alert(
                    person_id,
                    last_contact['vehicle_id'],
                    last_contact['door_id'],
                    timestamp,
                    len(unique_vehicles),  # 现在显示的是不同车辆数
                    contact_count
                )
                self.last_alert_time[person_id] = timestamp
        
    def _trigger_alert(self, person_id, vehicle_id, door_id, timestamp, unique_vehicles, total_contacts):
        """触发警报并记录触碰边界框"""
        # 获取最后接触的边界框坐标
        last_contact_bbox = None
        for person in self._parse_person_results(self.person_tracker.track(self.last_frame)):
            if person['id'] == person_id:
                last_contact_bbox = person['bbox']  # [lx, ly, rx, ry]
                break
        
        alert_info = {
            'person_id': person_id,
            'vehicle_id': vehicle_id,
            'door_id': door_id,
            'timestamp': time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(timestamp)),
            'unique_vehicles': unique_vehicles,
            'total_contacts': total_contacts,
            'frame_count': self.frame_count,
            'contact_bbox': last_contact_bbox  # 新增触碰边界框坐标
        }
        self.alerts.append(alert_info)
        
        # 打印警报信息，包含边界框坐标
        bbox_text = f"({last_contact_bbox[0]:.0f},{last_contact_bbox[1]:.0f})-({last_contact_bbox[2]:.0f},{last_contact_bbox[3]:.0f})" if last_contact_bbox else "N/A"
        print(f"\n🚨 警报 [人员{person_id}] 帧 {self.frame_count} | 检测到多车车门接触!")
        print(f" - 最后接触: 车辆{vehicle_id}-车门{door_id}")
        print(f" - 接触的不同车辆数: {unique_vehicles}")
        print(f" - 触碰位置: {bbox_text}")
        print(f" - 时间: {alert_info['timestamp']}\n")
    
    def _visualize(self, frame, persons, vehicles) -> np.ndarray:
        
        #frame=cv2AddChineseText(frame,'测试',(10, 10))
        """可视化结果"""
        # 绘制车辆关键点
        # for vehicle in vehicles:
        #     x1, y1, x2, y2 = map(int, vehicle['bbox'])
        #     color = (255, 165, 0)
        #     #cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
        #     cv2.putText(frame, f"V{vehicle['id']}", (x1, y1-10), 
        #                cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
        #     for i, (x, y) in enumerate(vehicle['keypoints']):
        #         color = (255, 0, 0) if i == 0 else (0, 0, 255)
        #         cv2.circle(frame, (int(x), int(y)), 6, color, -1)
        #         cv2.putText(frame, f"D{i}", (int(x)+8, int(y)), 
        #                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255,255,255), 1)
        
        # 绘制人员
        alert_persons = {a['person_id'] for a in self.alerts[-3:]}
        for person in persons:
            # x1, y1, x2, y2 = map(int, person['bbox'])
            # color = (0, 0, 255) if person['id'] in alert_persons else (0, 255, 0)
            # #cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
            # cv2.putText(frame, f"P{person['id']}", (x1, y1-10), 
            #            cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
            
            # # 绘制中心点(接触点)
            cx, cy = int(person['center'][0]), int(person['center'][1])
            # cv2.circle(frame, (cx, cy), 5, (0, 255, 255), -1)
            
            # # 绘制接触判定范围(动态阈值圆)
            dynamic_thresh = self._get_dynamic_threshold(person['pixel_height'])
            # cv2.circle(frame, (cx, cy), int(dynamic_thresh), (0, 0, 255), 1)
            
            # 标记当前检测到的接触
            for vehicle in vehicles:
                contact_info = self._check_person_contact(person, vehicle, dynamic_thresh)
                if contact_info['door_id'] > 0:
                    door_pos = vehicle['keypoints'][contact_info['door_id']]
                    cv2.line(frame, (cx, cy), 
                            (int(door_pos[0]), int(door_pos[1])), 
                            (0, 0, 255), 2)
                    # 在车门把手处添加接触标记
                    cv2.rectangle(frame, 
              (int(door_pos[0])-70, int(door_pos[1])-70),
              (int(door_pos[0])+70, int(door_pos[1])+70),
               (0, 0, 255), 2)
                    cv2.putText(frame, "X", (int(door_pos[0])-5, int(door_pos[1])+5), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                #cv2.putText(frame, alert_text, (10, y_offset), 
                           #cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                #cv2.putText(frame, details_text, (10, y_offset+30), 
                           #cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 1)
        
        # # 在画面右下角显示当前接触阈值
        # cv2.putText(frame, f"{self.base_contact_thresh}px", 
        #            (frame.shape[1]-250, frame.shape[0]-20),
        #            cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        return frame
    def _visualize_alerts(self,frame):
        height, width = frame.shape[:2]
              # 显示警报信息
        if self.alerts:
            active_alerts = {}
            for alert in reversed(self.alerts):
                pid = alert['person_id']
                if pid not in active_alerts:
                    active_alerts[pid] = alert
            
            for i, (pid, alert) in enumerate(sorted(active_alerts.items())[-3:]):
                alert_text = (f"人员 {pid} 预警")
                details_text = (f"最后触碰: vehicle{alert['vehicle_id']}")
                
                y_offset = 30 + i * (60+height/20)
                frame=cv2AddChineseText(frame,alert_text,(10, y_offset),(255, 0, 0),height/20)
                frame=cv2AddChineseText(frame,details_text,(10, y_offset+height/20),(255, 0, 0),height/20)
        return frame