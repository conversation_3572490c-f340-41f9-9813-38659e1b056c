from typing import Union, Any, Dict, List, Tuple
from PIL import Image
import numpy as np
from pathlib import Path

from predict_method.base_predict import BasePredictor
from utils.image_util import cv2AddChineseText

from runtime import EngineModelRuntime
import cv2 as cv


class CarClassEngine(BasePredictor):
    def _initialize_from_config(self):
        """从配置文件初始化目标检测模型和相关参数"""
        # 初始化模型参数
        self.confidence_threshold = self.config.get('confidence_threshold', 0.5)
        self.predict_params = self.config.get('predict_params', {})
        self.input_size = tuple(self.config['input_size'])  # 期望的输入尺寸 (height, width)
        self.classes = self.config['classes']  # 类别名称列表
        self.session = EngineModelRuntime(self._get_model_file().read(), input_size=self.input_size,
                                          predict_params=self.predict_params)
        # print(f"Initialized detector with: {self.config}")

        self.class_color_dict = {
            0: (4, 42, 255),
            1: (11, 219, 235),
            2: (0, 243, 68),
            3: (0, 223, 183),
            4: (17, 31, 104),
            5: (255, 111, 221),
            6: (255, 68, 79),
            7: (204, 237, 0)
        }
        self.CLASS_NAMES = {
            0: '自行车', 1: '车', 2: '面包车', 3: '卡车',
            4: '三轮车', 5: '带蓬三轮车', 6: '公交车', 7: '摩托车'
        }
        self.frame_count=0
    def preprocess(self, image_input: Union[str, Image.Image, np.ndarray]) -> np.ndarray:
        """预处理图像数据以适应模型输入要求"""
        # 如果输入是文件路径，加载图像
        if isinstance(image_input, str):
            image = cv.imread(image_input)
        elif isinstance(image_input, Path):
            image = cv.imread(str(image_input))
        elif isinstance(image_input, Image.Image):
            image = np.array(image_input)

        else:
            image = image_input
        # self.img_height, self.img_width = image.shape[:2]
        #         # 将图像颜色空间从 BGR 转换为 RGB
        # img_rgb = cv.cvtColor(image, cv.COLOR_BGR2RGB)

        # # 保持宽高比，进行 letterbox 填充, 使用模型要求的输入尺寸
        # img, self.ratio, (self.dw, self.dh) = self.letterbox(img_rgb, new_shape=(self.session.input_width, self.session.input_height))
        return image

    def callback(self, task, processed_frame, predictions, timestamp,original_frame):
        if len(predictions) == 0:
            return
        import time
        if not hasattr(self, '_last_call_time'):
            self._last_call_time = 0

        current_time = time.time()  # 获取当前系统时间（秒）
        if current_time - self._last_call_time >= 3:
            self.call_ai_hook_interface(task.callback_url, processed_frame, timestamp, task.latitude, task.longitude)
            self._last_call_time = current_time

    def predict(self, image_array: np.ndarray) -> np.ndarray:
        """执行目标检测预测"""
        # return [{}]
        return self.session.predict(image_array)

    def visualize(self, frame: np.ndarray, out) -> np.ndarray:
        """执行目标检测预测"""
        output_frame = frame.copy()
        # 自定义绘制每个检测框
        for i, box in enumerate(out):
            x1, y1, x2, y2 = map(int, box['bbox'])
            class_id = box['class_id']
            class_name = self.CLASS_NAMES.get(class_id, class_id)
            class_color = self.class_color_dict[class_id]
            # text_1=f"{class_name} 1"
            text = f'{class_name}'
            # text_2=f'{display_id}'
            # # 计算文本背景
            (text_width, text_height), _ = cv.getTextSize(str(text), cv.FONT_HERSHEY_SIMPLEX, 0.5, 1)
            text_width = len(text) * 20 if len(text) !=1 else 30
            # print(f'{text}---{text_width}, {text_height}{(x1+10 + text_width)-x1}')

            output_frame = cv.rectangle(output_frame, (x1, y1 - text_height - 10), (x1 + text_width, y1),
                                  self.class_color_dict[class_id], -1)
            output_frame = cv2AddChineseText(output_frame, str(text), (x1 + 8, y1 - text_height - 8),
                                      (255, 255, 255), 16, 4)
            cv.rectangle(output_frame, (x1, y1), (x2, y2), self.class_color_dict[class_id], 2)
        self.frame_count+=1
        # if self.frame_count % 30 == 0:
            # print('已处理{}帧'.format(self.frame_count))
            # cv.imwrite(f'E:/A_worker/W_XM/ZLT_api_test/modelManger_deply/test_video/test{int((self.frame_count)/30)}.png',
            #             output_frame)
        return output_frame

    def postprocess(self, raw_prediction: np.ndarray) -> List[Dict[str, Any]]:
        """后处理预测结果，返回易读的检测结果"""
        detections = []
        for result in raw_prediction:
            for box in result.boxes:
                # 获取坐标 (x1, y1, x2, y2)
                x1, y1, x2, y2 = box.xyxy[0].tolist()

                # 获取置信度和类别ID
                conf = box.conf.item()
                cls_id = box.cls.item()
                # 构建检测结果字典
                detection = {
                    'bbox': [float(x1), float(y1), float(x2), float(y2)],
                    'conf': conf,
                    'class_id': int(cls_id),
                    'class_name': self.classes[int(cls_id)]  # 使用 model.names 获取类别名称
                }
                detections.append(detection)
        return detections


if __name__ == "__main__":
    model_path = "/home/<USER>/project/carTrack/people_dr/train7/weights/best.onnx"
    obj = ObjectDetector('/home/<USER>/project/modelManger/models/poeple/config.yaml')
    print(obj.predict('/home/<USER>/project/carTrack/people_v4/images/Train/frame_000030.png'))