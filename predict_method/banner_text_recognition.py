import base64
import json
import cv2
import numpy as np
import threading
import time
from typing import Union, Any, Dict, List
from PIL import Image
from predict_method.base_predict import BasePredictor
from config.model_config import ModelConfig
from openai import OpenAI
from utils.image_util import cv2AddChineseText
from runtime import EngineModelRuntime

class BannerTextRecognition(BasePredictor):
    """横幅文字识别类，使用OpenAI API进行文字识别"""
    
    def __init__(self, model_config: ModelConfig, task):
        """初始化横幅文字识别器
        
        Args:
            model_config: 模型配置
            task: 任务对象
        """
        super().__init__(model_config, task)
        
    def _initialize_from_config(self):
        """从配置初始化模型"""
        # 目标检测相关参数
        self.detection_confidence = self.config.get('detection_confidence', 0.5)
        self.input_size = tuple(self.config.get('input_size', [640, 640]))
        self.classes = self.config.get('classes', ['banner'])
        self.predict_params = self.config.get('predict_params', {})

        # OpenAI API参数
        self.api_base = self.config.get('api_base', 'http://192.168.171.53:30001/v1')
        self.model_name = self.config.get('ai_model_name', 'qwen2.5-vl-7b-instruct')
        self.api_key = self.config.get('api_key', 'gpustack_5e7a262b5803b6bc_16b314c2d6113ca26b285e5190180f24')
        self.max_tokens = self.config.get('max_tokens', 1000)
        self.temperature = self.config.get('temperature', 0.1)

        # 保存最后识别到的横幅文本
        self.last_recognized_text = ""
        self.last_recognition_time = 0

        # 初始化OpenAI客户端
        self.client = OpenAI(api_key=self.api_key, base_url=self.api_base)

        # 加载检测模型
        self._load_detection_model()

        print(f"✅ 横幅识别器初始化完成")

    def _load_detection_model(self):
        """加载横幅检测模型"""
        try:
            self.detection_session = EngineModelRuntime(
                self._get_model_file().read(),
                input_size=self.input_size,
                predict_params=self.predict_params
            )
            print(f"✅ 横幅检测模型加载成功")

        except Exception as e:
            print(f"❌ 横幅检测模型加载失败: {e}")
            self.detection_session = None
    
    def preprocess(self, image_input: Union[str, Image.Image, np.ndarray]) -> np.ndarray:
        """预处理输入图像"""
        if isinstance(image_input, str):
            image = cv2.imread(image_input)
        elif isinstance(image_input, Image.Image):
            image = cv2.cvtColor(np.array(image_input), cv2.COLOR_RGB2BGR)
        elif isinstance(image_input, np.ndarray):
            image = image_input.copy()
        else:
            raise ValueError(f"不支持的图像输入类型: {type(image_input)}")

        if image is None:
            raise ValueError("无法加载图像")

        return image
    
    def _detect_banners(self, image: np.ndarray) -> List[Dict]:
        """检测横幅位置"""
        if self.detection_session is None:
            return []

        try:
            raw_predictions = self.detection_session.predict(image)
            detections = self._postprocess_detections(raw_predictions)

            # 过滤低置信度检测
            filtered = [d for d in detections if d.get('conf', 0) >= self.detection_confidence]
            # print(f"检测到 {len(filtered)} 个横幅")
            return filtered

        except Exception as e:
            print(f"横幅检测失败: {e}")
            return []

    def _postprocess_detections(self, raw_predictions) -> List[Dict]:
        """后处理检测结果"""
        detections = []
        try:
            if isinstance(raw_predictions, list):
                for result in raw_predictions:
                    if hasattr(result, 'boxes'):
                        for box in result.boxes:
                            x1, y1, x2, y2 = box.xyxy[0].tolist()
                            conf = box.conf.item()
                            cls_id = box.cls.item()

                            detection = {
                                'bbox': [float(x1), float(y1), float(x2), float(y2)],
                                'conf': conf,
                                'class_id': int(cls_id),
                                'class_name': self.classes[int(cls_id)] if int(cls_id) < len(self.classes) else 'banner'
                            }
                            detections.append(detection)
        except Exception as e:
            print(f"后处理检测结果失败: {e}")
        return detections
    
    def _async_text_recognition(self, image: np.ndarray, detections: List[Dict], task, timestamp,original_frame):
        """异步进行文字识别并推送回调"""
        def recognize_and_callback():
            try:
                # 将图像等比例缩放到宽960
                h, w = original_frame.shape[:2]
                if w > 960:
                    scale = 960 / w
                    new_w, new_h = int(w * scale), int(h * scale)
                    resized_image = cv2.resize(original_frame, (new_w, new_h), interpolation=cv2.INTER_AREA)
                else:
                    resized_image = original_frame

                # 编码图像
                _, buffer = cv2.imencode('.jpg', resized_image, [cv2.IMWRITE_JPEG_QUALITY, 95])
                image_base64 = base64.b64encode(buffer).decode('utf-8')

                # 调用OpenAI API识别文字
                response = self.client.chat.completions.create(
                    model='qwen2.5-vl-7b-instruct',
                    messages=[{
                        "role": "user",
                        "content": [{
                            "type": "text",
                            "text": '''
"严格根据图像内容进行判断：若存在横幅，则直接提取并返回横幅上的文字内容（仅返回原始文字，无需任何格式或说明）；若无横幅、文字不可识别或图像不清晰，则返回空值。注意：必须确保结果100%基于图像实际内容，严禁任何形式的推测或虚构。
'''
                        }, {
                            "type": "image_url",
                            "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"}
                        }]
                    }],
                    max_tokens=self.max_tokens,
                    temperature=self.temperature
                )

                text_content = response.choices[0].message.content.strip()

                if text_content:
                    print(f"识别到横幅文字: {text_content}")

                    # 保存最后识别到的文本
                    self.last_recognized_text = text_content
                    self.last_recognition_time = timestamp

                    # 推送回调
                    callback_params = {
                        "content": text_content,
                    }

                    self.call_ai_hook_interface(
                        task.callback_url,
                        image,
                        timestamp,
                        task.latitude,
                        task.longitude,
                        callback_params
                    )
                else:
                    print("未识别到文字内容")

            except Exception as e:
                print(f"异步文字识别失败: {e}")

        # 启动异步线程
        thread = threading.Thread(target=recognize_and_callback, daemon=True)
        thread.start()

    def predict(self, image_array: np.ndarray) -> Any:
        """主预测方法：检测横幅并返回检测结果"""
        try:
            # 检测横幅
            detections = self._detect_banners(image_array)

            if not detections:
                return {
                    "has_banner": False,
                    "detections": [],
                    "success": True
                }

            # 转换为相对坐标
            h, w = image_array.shape[:2]
            for detection in detections:
                bbox = detection.get('bbox', [])
                if len(bbox) == 4:
                    detection['rel_bbox'] = [bbox[0]/w, bbox[1]/h, bbox[2]/w, bbox[3]/h]

            return {
                "has_banner": True,
                "detections": detections,
                "success": True
            }

        except Exception as e:
            print(f"横幅检测失败: {e}")
            return {
                "has_banner": False,
                "detections": [],
                "success": False,
                "error": str(e)
            }
    
    def postprocess(self, raw_prediction: Any) -> Dict[str, Any]:
        """后处理预测结果"""
        return raw_prediction
    
    def visualize(self, frame: np.ndarray, predictions: Dict[str, Any]) -> np.ndarray:
        """可视化检测结果"""
        result_frame = frame.copy()

        if not predictions.get("success", False):
            result_frame = cv2AddChineseText(result_frame, "检测失败", (10, 30), (0, 0, 255))
            return result_frame

        detections = predictions.get("detections", [])

        if not detections:
            result_frame = cv2AddChineseText(result_frame, "未检测到横幅", (10, 30), (0, 255, 255))
            return result_frame

        # 绘制检测框
        for i, detection in enumerate(detections):
            bbox = detection.get('bbox', [])
            conf = detection.get('conf', 0)

            if len(bbox) == 4:
                x1, y1, x2, y2 = map(int, bbox)

                # 绘制边界框
                cv2.rectangle(result_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)

                # 显示标签
                label = f"横幅 {conf:.2f}"
                label_y = max(y1 - 10, 20)
                result_frame = cv2AddChineseText(result_frame, label, (x1, label_y), (0, 255, 0))

        # # 显示检测统计
        # result_frame = cv2AddChineseText(result_frame, f"检测到 {len(detections)} 个横幅",
        #                                (10, 30), (0, 255, 0))

        # 显示最后识别到的横幅文本
        if hasattr(self, 'last_recognized_text') and self.last_recognized_text:
            y_offset = 10
            result_frame = cv2AddChineseText(result_frame, "横幅文字:", (10, y_offset), (255, 255, 255))

            # 分行显示长文本
            text = self.last_recognized_text
            max_length = 30  # 每行最大字符数

            if len(text) > max_length:
                # 分割长文本
                lines = []
                for i in range(0, len(text), max_length):
                    lines.append(text[i:i+max_length])

                for line in lines:
                    y_offset += 30
                    result_frame = cv2AddChineseText(result_frame, line, (10, y_offset), (255, 255, 255))
            else:
                y_offset += 30
                result_frame = cv2AddChineseText(result_frame, text, (10, y_offset), (255, 255, 255))

        return result_frame
    
    def callback(self, task, processed_frame, predictions, timestamp,original_frame):
        """回调函数：检测到横幅时启动异步文字识别"""
        
            
        current_time = time.time()  # 获取当前系统时间（秒）
        if not hasattr(self, '_last_call_time'):
            self._last_call_time = 0
        if predictions.get("success", False) and predictions.get("has_banner", False) and current_time - self._last_call_time >= 1:
            self._last_call_time = current_time
            detections = predictions.get("detections", [])
            if detections:
                # print(f"检测到 {len(detections)} 个横幅，启动异步文字识别")
                # 启动异步文字识别
                self._async_text_recognition(processed_frame, detections, task, timestamp,original_frame)


if __name__ == "__main__":
    import os

    print("=" * 50)
    print("横幅检测测试")
    print("=" * 50)

    try:
        # 初始化模型
        config_path = '/home/<USER>/modelManger_deply/models/banner/config.yaml'
        model = BannerTextRecognition(ModelConfig(config_path), {})
        print("✅ 模型初始化成功")

        # 加载测试图像
        image_path = '/home/<USER>/modelManger_deply/accc.png'
        if not os.path.exists(image_path):
            print(f"❌ 图像文件不存在: {image_path}")
            exit(1)

        original_image = cv2.imread(image_path)
        if original_image is None:
            print(f"❌ 无法读取图像文件: {image_path}")
            exit(1)

        print(f"✅ 图像加载成功: {original_image.shape}")

        # 执行检测
        start_time = time.time()
        predictions = model.predict_pipeline(image_path)
        processing_time = time.time() - start_time

        print(f"✅ 检测完成，耗时: {processing_time:.2f}秒")

        # 显示结果
        if predictions.get("success", False):
            detections = predictions.get("detections", [])
            # print(f"检测到 {len(detections)} 个横幅")

            for i, detection in enumerate(detections):
                bbox = detection.get('bbox', [])
                conf = detection.get('conf', 0)
                print(f"横幅{i+1}: 置信度={conf:.3f}, 位置={bbox}")
        else:
            print("❌ 检测失败")

        # 保存可视化结果
        result_image = model.visualize(original_image, predictions)
        model.callback({}, result_image, predictions, 1)
        result_path = f"banner_detection_result_{int(time.time())}.jpg"
        cv2.imwrite(result_path, result_image)
        print(f"✅ 结果已保存: {result_path}")

    except Exception as e:
        print(f"❌ 发生错误: {e}")
        import traceback
        traceback.print_exc()

    print("=" * 50)