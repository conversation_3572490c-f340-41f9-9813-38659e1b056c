from turtle import width
from runtime import EngineModelRuntime
import cv2
import numpy as np
from datetime import datetime
from collections import deque
from predict_method.base_predict import BasePredictor
from typing import Union, Any, Dict, List, Tuple
from PIL import Image
from pathlib import Path
import os
import time
from utils.image_util import cv2AddChineseText

class WaterSwimEngine(BasePredictor):

    def _initialize_from_config(self):
        """从配置文件初始化目标检测模型和相关参数"""
        # 初始化模型参数
        self.waterswim_model = EngineModelRuntime(
            self._get_file_by_key("model_filename"),
            predict_params=self.config.get("predict_params"),
        )
        waterswim_config = WaterSwimConfig()
        # if self.config.get("density_box_min_count"):
        #     car_config.DENSITY_BOX_MIN_COUNT = self.config.get("density_box_min_count")
        self.monitor = WaterSwim(self.waterswim_model_model, waterswim_config)
        print(f"Initialized detector with: {self.config}")

    def preprocess(
        self, image_input: Union[str, Image.Image, np.ndarray]
    ) -> np.ndarray:
        """预处理图像数据以适应模型输入要求"""
        # 如果输入是文件路径，加载图像
        if isinstance(image_input, str):
            image = cv2.imread(image_input)
        elif isinstance(image_input, Path):
            image = cv2.imread(str(image_input))
        elif isinstance(image_input, Image.Image):
            image = np.array(image_input)
        else:
            image = image_input
        return image

    def predict(self, image_array: np.ndarray) -> np.ndarray:
        """执行目标检测预测"""
        boxes, confidences, class_ids = self.monitor.detect_vehicles(image_array)
        # 调用交通分析器的绘制方法，在帧上绘制检测结果
        result_frame = self.monitor.draw_results(image_array, boxes, centers)
        return result_frame

    def visualize(self, frame: np.ndarray, out) -> np.ndarray:
        return out

    def postprocess(self, raw_prediction: np.ndarray) -> List[Dict[str, Any]]:
        """后处理预测结果，返回易读的检测结果"""
        return raw_prediction

class WaterSwimConfig:
    """水域游泳检测配置类"""
    # 热力图相关配置
    HEATMAP_RADIUS = 30
    HEATMAP_THRESHOLD = 0.6
    DENSITY_BOX_COLOR = (0, 0, 255)
    
    # 目标检测相关配置
    CONFIDENCE_THRESHOLD = 0.3
    IOU_THRESHOLD = 0.45
    INFERENCE_SIZE = 640



class WaterSwim:
    """水域游泳检测器"""
    def __init__(self, swaterswim, config):
        """
        初始化水域游泳检测器
        
        Args:
            model_path: 模型路径（.pt或.engine格式）
            config: 配置对象，如果为None则使用默认配置
        """
        self.config = config if config else WaterSwimConfig()
        self.model = swaterswim
        self.class_names = self.model.names  # 获取类别名称映射

    def predict(self, image: Union[str, np.ndarray]) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        执行目标检测
        
        Args:
            image: 图像路径或NumPy数组
            
        Returns:
            boxes: 边界框数组 (N, 4)
            confidences: 置信度数组 (N,)
            class_ids: 类别ID数组 (N,)
        """
        # 使用模型进行推理，Ultralytics YOLO模型内部已实现NMS
        results = self.model.predict(
            source=image,
            conf=self.config.CONFIDENCE_THRESHOLD,
            iou=self.config.IOU_THRESHOLD,
            imgsz=self.config.INFERENCE_SIZE,
        )
        
        # 提取结果
        result = results[0]  # 单图推理只需取第一个结果
        boxes = result.boxes.xyxy.cpu().numpy()
        confidences = result.boxes.conf.cpu().numpy()
        class_ids = result.boxes.cls.cpu().numpy().astype(int)
        
        return boxes, confidences, class_ids

    def _visualize(self, image: Union[str, np.ndarray], 
                 boxes: np.ndarray, confidences: np.ndarray, class_ids: np.ndarray) -> np.ndarray:
        """
        可视化检测结果
        
        Args:
            image: 原始图像路径或NumPy数组
            boxes: 边界框数组
            confidences: 置信度数组
            class_ids: 类别ID数组
        """
        # 读取图像（如果是路径）
        if isinstance(image, str):
            img = cv2.imread(image)
        else:
            img = image.copy()
        
        # 绘制检测结果
        for box, conf, cls_id in zip(boxes, confidences, class_ids):
            x1, y1, x2, y2 = map(int, box)
            
            # 绘制边界框
            cv2.rectangle(img, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # 添加类别和置信度标签
            label = f"{self.class_names[cls_id]} {conf:.2f}"
            img = cv2.putText(img, label, (x1, y1 - 10), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
        return img
        

