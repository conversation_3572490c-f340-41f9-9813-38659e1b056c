import base64
import cv2
import numpy as np
import threading
import time
from typing import Union, Any, Dict, List
from PIL import Image
from predict_method.base_predict import BasePredictor
from config.model_config import ModelConfig
from openai import OpenAI
from utils.image_util import cv2AddChineseText
from runtime import EngineModelRuntime


class CarPlateRecognition(BasePredictor):
    """车牌识别类，使用目标检测模型检测车牌位置，OpenAI API识别车牌号码"""
    
    def __init__(self, model_config: ModelConfig, task):
        """初始化车牌识别器"""
        super().__init__(model_config, task)
        
    def _initialize_from_config(self):
        """从配置初始化模型"""
        # 目标检测相关参数
        self.detection_confidence = self.config.get('detection_confidence', 0.5)
        self.input_size = tuple(self.config.get('input_size', [640, 640]))
        self.classes = self.config.get('classes', ['car_plate'])
        self.predict_params = self.config.get('predict_params', {})

        # OpenAI API参数
        self.api_base = self.config.get('api_base', 'http://192.168.171.53:30001/v1')
        self.model_name = self.config.get('ai_model_name', 'qwen2.5-vl-7b-instruct')
        self.api_key = self.config.get('api_key', 'gpustack_5e7a262b5803b6bc_16b314c2d6113ca26b285e5190180f24')
        self.max_tokens = self.config.get('max_tokens', 1000)
        self.temperature = self.config.get('temperature', 0.1)

        # 保存最后识别到的车牌号码
        self.last_recognized_plates = []
        self.last_recognition_time = 0

        # 初始化OpenAI客户端
        self.client = OpenAI(api_key=self.api_key, base_url=self.api_base)

        # 加载检测模型
        self._load_detection_model()

        # 加载车牌白名单
        self._load_plate_whitelist()

        print(f"✅ 车牌识别器初始化完成")
    
    def _load_detection_model(self):
        """加载车牌检测模型"""
        try:
            self.detection_session = EngineModelRuntime(
                 self._get_model_file().read(),
                input_size=self.input_size,
                predict_params=self.predict_params
            )
            print(f"✅ 车牌检测模型加载成功")

        except Exception as e:
            print(f"❌ 车牌检测模型加载失败: {e}")
            self.detection_session = None

    def _load_plate_whitelist(self):
        """加载车牌白名单"""
        try:
            whitelist_path = '/home/<USER>/modelManger_deply/car_plate'
            with open(whitelist_path, 'r', encoding='utf-8') as f:
                self.plate_whitelist = [line.strip() for line in f.readlines() if line.strip()]
            print(f"✅ 车牌白名单加载成功，共 {len(self.plate_whitelist)} 个车牌")

        except Exception as e:
            print(f"❌ 车牌白名单加载失败: {e}")
            self.plate_whitelist = []

    def preprocess(self, image_input: Union[str, Image.Image, np.ndarray]) -> np.ndarray:
        """预处理输入图像"""
        if isinstance(image_input, str):
            image = cv2.imread(image_input)
        elif isinstance(image_input, Image.Image):
            image = cv2.cvtColor(np.array(image_input), cv2.COLOR_RGB2BGR)
        elif isinstance(image_input, np.ndarray):
            image = image_input.copy()
        else:
            raise ValueError(f"不支持的图像输入类型: {type(image_input)}")
        
        if image is None:
            raise ValueError("无法加载图像")
        
        return image

    def _detect_car_plates(self, image: np.ndarray) -> List[Dict]:
        """检测车牌位置"""
        if self.detection_session is None:
            return []

        try:
            # 使用session.predict获取原始结果
            raw_predictions = self.detection_session.predict(image)

            # 后处理检测结果
            detections = self._postprocess_detections(raw_predictions)

            # 过滤低置信度检测
            filtered = [d for d in detections if d.get('conf', 0) >= self.detection_confidence]
            return filtered

        except Exception as e:
            print(f"车牌检测失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def _postprocess_detections(self, raw_predictions) -> List[Dict]:
        """后处理检测结果"""
        detections = []
        try:
            if isinstance(raw_predictions, list):
                for result in raw_predictions:
                    if hasattr(result, 'boxes') and result.boxes is not None:
                        for box in result.boxes:
                            x1, y1, x2, y2 = box.xyxy[0].tolist()
                            conf = box.conf.item()
                            cls_id = box.cls.item()

                            detection = {
                                'bbox': [float(x1), float(y1), float(x2), float(y2)],
                                'conf': conf,
                                'class_id': int(cls_id),
                                'class_name': self.classes[int(cls_id)] if int(cls_id) < len(self.classes) else 'car_plate'
                            }
                            detections.append(detection)
            else:
                # 如果是单个结果对象
                if hasattr(raw_predictions, 'boxes') and raw_predictions.boxes is not None:
                    for box in raw_predictions.boxes:
                        x1, y1, x2, y2 = box.xyxy[0].tolist()
                        conf = box.conf.item()
                        cls_id = box.cls.item()

                        detection = {
                            'bbox': [float(x1), float(y1), float(x2), float(y2)],
                            'conf': conf,
                            'class_id': int(cls_id),
                            'class_name': self.classes[int(cls_id)] if int(cls_id) < len(self.classes) else 'car_plate'
                        }
                        detections.append(detection)
        except Exception as e:
            print(f"后处理检测结果失败: {e}")
            import traceback
            traceback.print_exc()
        return detections

    def _async_plate_recognition(self, image: np.ndarray, detections: List[Dict], task, timestamp, original_frame):
        """异步进行车牌号码识别并推送回调"""
        def recognize_and_callback():
            try:
                # 将图像等比例缩放到宽960
                h, w = image.shape[:2]
                if w > 960:
                    scale = 960 / w
                    new_w, new_h = int(w * scale), int(h * scale)
                    resized_image = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_AREA)
                else:
                    resized_image = image

                # 编码图像
                _, buffer = cv2.imencode('.jpg', resized_image, [cv2.IMWRITE_JPEG_QUALITY, 95])
                image_base64 = base64.b64encode(buffer).decode('utf-8')

                # 构建包含白名单的提示词
                whitelist_text = "、".join(self.plate_whitelist) if self.plate_whitelist else "无"
                prompt_text = f"""请识别图像中的车牌号码，并判断是否在以下白名单中：
白名单车牌：{whitelist_text}

要求：
1. 只识别图像中清晰可见的车牌号码
2. 只有当识别到的车牌号码在上述白名单中时，才返回该车牌号码
3. 如果识别到的车牌不在白名单中，或者没有识别到车牌，请返回'无'
4. 如果有多个符合条件的车牌，用逗号分隔
5. 直接返回车牌号码，不需要其他说明文字"""

                # 调用OpenAI API识别车牌号码
                response = self.client.chat.completions.create(
                    model=self.model_name,
                    messages=[{
                        "role": "user",
                        "content": [{
                            "type": "text",
                            "text": prompt_text
                        }, {
                            "type": "image_url",
                            "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"}
                        }]
                    }],
                    max_tokens=self.max_tokens,
                    temperature=self.temperature
                )

                plate_text = response.choices[0].message.content.strip()

                # 检查是否识别到有效车牌
                if plate_text and plate_text.lower() not in ['无', 'none', 'null', '没有', '未识别', '不清晰']:
                    print(f"识别到车牌号码: {plate_text}")

                    # 解析车牌号码
                    plate_numbers = [p.strip() for p in plate_text.split(',') if p.strip()]

                    # 验证车牌格式（简单验证）
                    valid_plates = []
                    for plate in plate_numbers:
                        # 基本的车牌格式验证：包含中文字符或字母数字组合
                        if len(plate) >= 6 and (any('\u4e00' <= c <= '\u9fff' for c in plate) or
                                               any(c.isalnum() for c in plate)):
                            valid_plates.append(plate)

                    if valid_plates:
                        valid_plate_text = ','.join(valid_plates)

                        # 保存最后识别到的车牌
                        self.last_recognized_plates = valid_plates
                        self.last_recognition_time = timestamp

                        # 推送回调
                        callback_params = {
                            "car_licence": valid_plate_text,
                        }

                        self.call_ai_hook_interface(
                            task.callback_url,
                            image,
                            timestamp,
                            task.latitude,
                            task.longitude,
                            callback_params
                        )
                    else:
                        print("识别结果不符合车牌格式，跳过推送")
                else:
                    print("未识别到有效车牌号码")

            except Exception as e:
                print(f"异步车牌识别失败: {e}")
                import traceback
                traceback.print_exc()

        # 启动异步线程
        thread = threading.Thread(target=recognize_and_callback, daemon=True)
        thread.start()

    def predict(self, image_array: np.ndarray) -> Any:
        """主预测方法：检测车牌并返回检测结果"""
        try:
            # 检测车牌
            detections = self._detect_car_plates(image_array)
            
            if not detections:
                return {
                    "has_car_plate": False,
                    "detections": [],
                    "success": True
                }
            
            # 转换为相对坐标
            h, w = image_array.shape[:2]
            for detection in detections:
                bbox = detection.get('bbox', [])
                if len(bbox) == 4:
                    detection['rel_bbox'] = [bbox[0]/w, bbox[1]/h, bbox[2]/w, bbox[3]/h]
            
            return {
                "has_car_plate": True,
                "detections": detections,
                "success": True
            }
                
        except Exception as e:
            print(f"车牌检测失败: {e}")
            return {
                "has_car_plate": False,
                "detections": [],
                "success": False,
                "error": str(e)
            }

    def postprocess(self, raw_prediction: Any) -> Dict[str, Any]:
        """后处理预测结果"""
        return raw_prediction

    def visualize(self, frame: np.ndarray, predictions: Dict[str, Any]) -> np.ndarray:
        """可视化检测结果"""
        result_frame = frame.copy()

        if not predictions.get("success", False):
            result_frame = cv2AddChineseText(result_frame, "检测失败", (10, 30), (0, 0, 255))
            return result_frame

        detections = predictions.get("detections", [])

        # 绘制检测框
        if detections:
            for detection in detections:
                bbox = detection.get('bbox', [])
                conf = detection.get('conf', 0)

                if len(bbox) == 4:
                    x1, y1, x2, y2 = map(int, bbox)

                    # 绘制边界框
                    cv2.rectangle(result_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)

                    # 显示标签
                    label = f"车牌 {conf:.2f}"
                    label_y = max(y1 - 10, 20)
                    result_frame = cv2AddChineseText(result_frame, label, (x1, label_y), (0, 255, 0))

            # 显示检测统计
            result_frame = cv2AddChineseText(result_frame, f"检测到 {len(detections)} 个车牌",
                                           (10, 30), (0, 255, 0))
        else:
            result_frame = cv2AddChineseText(result_frame, "未检测到车牌", (10, 30), (0, 255, 255))

        # 显示最后识别到的车牌号码
        if hasattr(self, 'last_recognized_plates') and self.last_recognized_plates:
            y_offset = 60
            result_frame = cv2AddChineseText(result_frame, "车牌:", (10, y_offset), (255, 255, 255))

            for i, plate in enumerate(self.last_recognized_plates):
                y_offset += 30
                result_frame = cv2AddChineseText(result_frame, f"{i+1}. {plate}", (10, y_offset), (255, 255, 255))


        return result_frame

    def callback(self, task, processed_frame, predictions, timestamp, original_frame):
        """回调函数：检测到车牌时启动异步车牌号码识别"""
        current_time = time.time()  # 获取当前系统时间（秒）
        if not hasattr(self, '_last_call_time'):
            self._last_call_time = 0
        if predictions.get("success", False) and predictions.get("has_car_plate", False) and current_time - self._last_call_time >= 1:
            self._last_call_time = current_time
            detections = predictions.get("detections", [])
            if detections:
                # 启动异步车牌识别
                self._async_plate_recognition(processed_frame, detections, task, timestamp, original_frame)


if __name__ == "__main__":
    import os
    
    print("=" * 50)
    print("车牌检测测试")
    print("=" * 50)
    
    try:
        # 初始化模型
        config_path = '/home/<USER>/modelManger_deply/models/car_plate_txt/config.yaml'
        model = CarPlateRecognition(ModelConfig(config_path), {})
        print("✅ 模型初始化成功")
        
        # 加载测试图像
        image_path = '/home/<USER>/modelManger_deply/accc.png'
        if not os.path.exists(image_path):
            print(f"❌ 图像文件不存在: {image_path}")
            exit(1)
        
        original_image = cv2.imread(image_path)
        if original_image is None:
            print(f"❌ 无法读取图像文件: {image_path}")
            exit(1)
        
        print(f"✅ 图像加载成功: {original_image.shape}")
        
        # 执行检测
        start_time = time.time()
        predictions = model.predict_pipeline(image_path)
        processing_time = time.time() - start_time
        
        print(f"✅ 检测完成，耗时: {processing_time:.2f}秒")
        
        # 显示结果
        if predictions.get("success", False):
            detections = predictions.get("detections", [])
            
            for i, detection in enumerate(detections):
                bbox = detection.get('bbox', [])
                conf = detection.get('conf', 0)
                print(f"车牌{i+1}: 置信度={conf:.3f}, 位置={bbox}")
        else:
            print("❌ 检测失败")
        
        # 保存可视化结果
        result_image = model.visualize(original_image, predictions)
        result_path = f"car_plate_detection_result_{int(time.time())}.jpg"
        cv2.imwrite(result_path, result_image)
        print(f"✅ 结果已保存: {result_path}")
        
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("=" * 50)
