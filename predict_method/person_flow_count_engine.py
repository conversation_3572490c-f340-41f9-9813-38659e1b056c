from typing import Union, Any, Dict, List

import cv2
import torch
from PIL import Image
import numpy as np
from pathlib import Path

from predict_method.base_predict import BasePredictor

from runtime import EngineModelRuntime
from lib.deep_sort.deep_sort import DeepSort
from utils import cv2AddChineseText


class PersonFlowCountEngine(BasePredictor):
    def _initialize_from_config(self):
        """从配置文件初始化目标检测模型和相关参数"""
        # 初始化模型参数
        self.confidence_threshold = self.config.get('confidence_threshold', 0.5)
        self.predict_params = self.config.get('predict_params', {})
        self.input_size = tuple(self.config['input_size'])  # 期望的输入尺寸 (height, width)
        # self.input_size_river = tuple(self.config['input_size_river'])  # 期望的输入尺寸 (height, width)
        self.classes_people = self.config['classes']  # 类别名称列表
        # self.classes_river = self.config['classes_river']  # 类别名称列表
        self.people_model = EngineModelRuntime(self._get_file_by_key('model_filename'), input_size=self.input_size,
                                          predict_params=self.predict_params,num_workers=1)


        # self.river_model=EngineModelRuntime(self._get_file_by_key('river_model_filename'),input_size=self.input_size_river,
        #                                     predict_params=self.predict_params,task='segment')

        self.person_flow_count = PersonFlowCountEngine_main(people_model=self.people_model)

        # print(f"Initialized detector with: {self.config}")
        self.count_all=0


    def preprocess(self, image_input: Union[str, Image.Image, np.ndarray]) -> np.ndarray:
        """预处理图像数据以适应模型输入要求"""
        # 如果输入是文件路径，加载图像
        if isinstance(image_input, str):
            image = cv2.imread(image_input)
        elif isinstance(image_input, Path):
            image = cv2.imread(str(image_input))
        elif isinstance(image_input, Image.Image):
            image = np.array(image_input)

        else:
            image = image_input
        # self.img_height, self.img_width = image.shape[:2]
        #         # 将图像颜色空间从 BGR 转换为 RGB
        # img_rgb = cv.cvtColor(image, cv.COLOR_BGR2RGB)

        # # 保持宽高比，进行 letterbox 填充, 使用模型要求的输入尺寸
        # img, self.ratio, (self.dw, self.dh) = self.letterbox(img_rgb, new_shape=(self.session.input_width, self.session.input_height))
        return image

    def callback(self, task, processed_frame, predictions, timestamp,original_frame):
        # detections = []
        # for result in predictions:
        #     for box in result.boxes:
        #         # 获取坐标 (x1, y1, x2, y2)
        #         x1, y1, x2, y2 = box.xyxy[0].tolist()
        #
        #         # 获取置信度和类别ID
        #         conf = box.conf.item()
        #         cls_id = box.cls.item()
        #         # 构建检测结果字典
        #         detection = {
        #             'bbox': [float(x1), float(y1), float(x2), float(y2)],
        #             'conf': conf,
        #             'class_id': int(cls_id),
        #             # 'class_name': self.classes[int(cls_id)]  # 使用 model.names 获取类别名称
        #         }
        #         detections.append(detection)
        # self.count_all=
        # print(f'call:{self.count_all}')
        if self.count_all == 0:
            return
        # print(self.count_all)
        import time
        if not hasattr(self, '_last_call_time'):
            self._last_call_time = 0

        current_time = time.time()  # 获取当前系统时间（秒）
        if current_time - self._last_call_time >= 3 and self.count_all != 0:
            s=self.call_ai_hook_interface(task.callback_url, processed_frame, timestamp, task.latitude, task.longitude,params={'person_num':self.count_all})
            self._last_call_time = current_time
            # print(f'回调成功:{s}')

    def predict(self, image_array: np.ndarray) -> np.ndarray:
        """执行目标检测预测"""
        # return [{}]
        results = self.people_model.predict(image_array)
        return results

    def visualize(self, frame: np.ndarray, out) -> np.ndarray:
        out_img,self.count_all = self.person_flow_count.predict_main(frame,out)
        return out_img

    def postprocess(self, raw_prediction: np.ndarray) -> List[Dict[str, Any]]:
        """后处理预测结果，返回易读的检测结果"""
        detections = raw_prediction
        # for result in raw_prediction:
        #     for box in result.boxes:
        #         # 获取坐标 (x1, y1, x2, y2)
        #         x1, y1, x2, y2 = box.xyxy[0].tolist()
        #
        #         # 获取置信度和类别ID
        #         conf = box.conf.item()
        #         cls_id = box.cls.item()
        #         # 构建检测结果字典
        #         detection = {
        #             'bbox': [float(x1), float(y1), float(x2), float(y2)],
        #             'conf': conf,
        #             'class_id': int(cls_id),
        #             'class_name': self.classes[int(cls_id)]  # 使用 model.names 获取类别名称
        #         }
        #         detections.append(detection)
        return detections

class PersonFlowCountEngine_main:
    def __init__(self,people_model):
        self.DEEPSORT_MODEL = r"lib/deep_sort/deep_sort/deep/checkpoint/ckpt.t7"
        #初始化deepsort
        self.deepsort = DeepSort(
            model_path=self.DEEPSORT_MODEL,
            max_age=50,
            n_init=5,
            use_cuda=torch.cuda.is_available()
        )
        self.people_model = people_model

        # ==== 人流量统计参数 ====
        self.GLOBAL_ID_COUNTER = 0  # 全局ID计数器（确保每个ID唯一）
        self.COUNTED_IDS = {}  # 已统计的ID字典 {track_id: (global_id, last_seen_frame)}
        self.MAX_DISAPPEAR_FRAMES = 50  # 约6-8秒超时
        self.PEOPLE_COUNT = 0  # 总人流量统计
        self.current_frame_count = 0  # 视频帧计数
        self.count_all=0
    def predict_main(self, frame,results):
        self.current_frame_count += 1
        self.count_all=0
        # ==== YOLO检测 ====
        detections = results[0].boxes.data.cpu().numpy()

        # 过滤检测结果
        valid_detections = []
        for *xyxy, conf, cls in detections:
            x_center = (xyxy[0] + xyxy[2]) / 2
            y_center = (xyxy[1] + xyxy[3]) / 2
            valid_detections.append([x_center, y_center, xyxy[2] - xyxy[0],
                                     xyxy[3] - xyxy[1], conf, int(cls)])

        # ==== DeepSORT跟踪与流量统计 ====
        active_ids = set()
        outputs=[]
        if valid_detections:
            bbox_xywh = np.array([d[:4] for d in valid_detections])
            confidences = np.array([d[4] for d in valid_detections])
            outputs = self.deepsort.update(bbox_xywh, confidences, frame)

            # 遍历当前帧所有跟踪目标
            for track in outputs:
                x1, y1, x2, y2, track_id = map(int, track[:5])

                # ==== 全局ID管理 ====
                if track_id not in self.COUNTED_IDS:
                    # 新目标：分配全局ID并计数
                    self.GLOBAL_ID_COUNTER += 1
                    self.PEOPLE_COUNT += 1
                    self.COUNTED_IDS[track_id] = (self.GLOBAL_ID_COUNTER, self.current_frame_count)
                    # print(
                    #     f"New person detected! Global ID: {self.GLOBAL_ID_COUNTER}, DeepSORT ID: {track_id}, Total count: {self.PEOPLE_COUNT}")
                else:
                    # 更新现有目标最后出现时间
                    global_id, _ = self.COUNTED_IDS[track_id]
                    self.COUNTED_IDS[track_id] = (global_id, self.current_frame_count)

                # 获取全局ID
                global_id = self.COUNTED_IDS[track_id][0]

                # ==== 显示目标框和全局ID ====
                # color = (0, 255, 0)  # 绿色边框
                # cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)  # 绘制边界框
                # cv2.putText(frame, f'ID: {global_id}', (x1, y1 - 10),  # 显示全局ID
                #             cv2.FONT_HERSHEY_SIMPLEX, 0.75, color, 2)
                text = '人'
                (text_width, text_height), _ = cv2.getTextSize(str(text), cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)
                text_width = len(text) * 17
                frame = cv2.rectangle(frame, (int(x1), int(y1 - text_height - 10)),
                                         (int(x1 + text_width), int(y1)),
                                         (94, 53, 255), -1)

                frame = cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)),
                                         (94, 53, 255), 1)  # 绘制边界框
                frame = cv2AddChineseText(frame, text, (int(x1), int(y1) - 20),  # 显示结果
                                             (255, 255, 255), 16)

                active_ids.add(track_id)

        # ==== 清理消失超时的ID ====
        disappeared_ids = [tid for tid, (_, last_seen) in self.COUNTED_IDS.items()
                           if self.current_frame_count - last_seen > self.MAX_DISAPPEAR_FRAMES]
        for tid in disappeared_ids:
            del self.COUNTED_IDS[tid]
            # print(f"DeepSORT ID {tid} removed due to timeout")

        h,w=frame.shape[0],frame.shape[1]
        overlay = frame.copy()
        cv2.rectangle(overlay,
                      (int(w-len('实时垂钓人数:111')*17),15),(int(w),90),
                      (0, 0, 0), -1)
        alpha = 0.4
        frame = cv2.addWeighted(overlay, alpha, frame, 1 - alpha, 0)

        # img_mask = cv2.addWeighted(overlay, alpha, img_mask, 0, 0)
        # ==== 显示统计结果 ====

        frame = cv2AddChineseText(frame, f"累计人数: {self.PEOPLE_COUNT}",
                                     (int(frame.shape[1] - 150), 30),
                                     (255, 255, 255), 16, )

        frame = cv2AddChineseText(frame, f"实时人数: {len(outputs)}",
                                  (int(frame.shape[1] - 150), 60),
                                  (255, 255, 255), 16, )

        self.count_all=len(outputs)
        # print(f'2:{self.count_all}')

        # cv2.putText(frame, f"Total People: {self.PEOPLE_COUNT}", (20, 100),
        #             cv2.FONT_HERSHEY_SIMPLEX, 3, (0, 255, 0), 5)
        # cv2.putText(frame, f"Current People: {len(valid_detections)}", (20, 200),
        #             cv2.FONT_HERSHEY_SIMPLEX, 3, (0, 255, 0), 5)

        # ==== 显示帧 ====
        # print(f'人流量已识别{self.current_frame_count}帧')
        # cv2.imwrite(f'E:/A_worker/W_XM/ZLT_api_test/modelManger_deply/test_video/test{self.current_frame_count}.png',frame)
        return frame,self.count_all





if __name__ == "__main__":
    model_path = "/home/<USER>/project/carTrack/people_dr/train7/weights/best.onnx"
    obj = ObjectDetector('/home/<USER>/project/modelManger/models/poeple/config.yaml')
    print(obj.predict('/home/<USER>/project/carTrack/people_v4/images/Train/frame_000030.png'))