
from typing import Union, Any, Dict, List, Tuple
from PIL import Image
from pathlib import Path
import hashlib
import numpy as np

from predict_method.base_predict import BasePredictor
from utils.image_util import cv2AddChineseText
from runtime import OnnxModelRuntime
import cv2 as cv

class ObjectDetector(BasePredictor):
    # Class-level cache for loaded models
    _model_cache = {}
    
    def _initialize_from_config(self):
        """从配置文件初始化目标检测模型和相关参数"""
        # 初始化模型参数
        self.confidence_threshold = self.config.get('confidence_threshold', 0.5)
        self.iou_threshold = self.config.get('iou_threshold', 0.4)
        self.input_size = tuple(self.config['input_size'])  # 期望的输入尺寸 (height, width)
        self.classes = self.config['classes']  # 类别名称列表
        
        # Get model file content and calculate its MD5 hash
        
        # Check if model is already loaded
        if self.model_config.get_model_id() not in ObjectDetector._model_cache:
            # Load new model and store in cache
            model_content = self._get_model_file().read()
            ObjectDetector._model_cache[self.model_config.get_model_id()] = OnnxModelRuntime(model_content)
        else:
            print(f"Reusing already loaded model with MD5: {self.model_config.get_model_id()}")
        
        # Get the session from cache
        self.session = ObjectDetector._model_cache[self.model_config.get_model_id()]
        print(f"Initialized detector with: {self.model_config.get_model_id()}")
    def preprocess(self, image_input: Union[str, Image.Image, np.ndarray]) -> np.ndarray:
        """预处理图像数据以适应模型输入要求"""
        # 如果输入是文件路径，加载图像
        if isinstance(image_input, str):
            image = cv.imread(image_input)
        elif isinstance(image_input, Path):
            image = cv.imread(str(image_input))
        elif isinstance(image_input, Image.Image):
            image =  np.array(image_input)  
           
        else:
            image = image_input
        # self.img_height, self.img_width = image.shape[:2]
        #         # 将图像颜色空间从 BGR 转换为 RGB
        # img_rgb = cv.cvtColor(image, cv.COLOR_BGR2RGB)
 
        # # 保持宽高比，进行 letterbox 填充, 使用模型要求的输入尺寸
        # img, self.ratio, (self.dw, self.dh) = self.letterbox(img_rgb, new_shape=(self.session.input_width, self.session.input_height))
        return image
    


    def predict(self, image_array: np.ndarray) -> np.ndarray:
        """执行目标检测预测"""
        #return [{}]
        return self.session.predict(image_array)

    def visualize(self, frame: np.ndarray, out) -> np.ndarray:
        """执行目标检测预测，自动生成类别颜色，文本框半透明"""
        output_frame = frame.copy()

        for box in out:
            x1, y1, x2, y2 = map(int, box['bbox'])
            class_name = str(box['class_name'])
            
            # 1. 自动生成颜色（基于类别名称的哈希值）
            hash_val = hash(class_name)  # 使用哈希值确保同一类别颜色一致
            color = (
                int((hash_val * 10) % 255),  # B
                int((hash_val * 20) % 255),  # G
                int((hash_val * 30) % 255)   # R
            )
            
            # 2. 绘制边界框（不透明）
            cv.rectangle(output_frame, (x1, y1), (x2, y2), color, 1)
            
            # 3. 计算文本大小
            (text_width, text_height), _ = cv.getTextSize(
                class_name, cv.FONT_HERSHEY_SIMPLEX, 0.5, 1
            )
            
            # 4. 创建半透明背景（使用 overlay 混合）
            overlay = output_frame.copy()
            cv.rectangle(
                overlay, 
                (x1, y1 - text_height - 10), 
                (x1 + text_width, y1), 
                color, -1
            )
            alpha = 0.5  # 透明度（0=全透明，1=不透明）
            cv.addWeighted(overlay, alpha, output_frame, 1 - alpha, 0, output_frame)
            
            # 5. 绘制中文文本（使用你的 cv2AddChineseText 方法）
            output_frame = cv2AddChineseText(
                output_frame, 
                class_name, 
                (x1 + 5, y1 - 5),  # 调整文本位置
                (255, 255, 255),    # 白色文本
                16                  # 字体大小
            )
        
        return output_frame


    def callback(self, task, processed_frame, predictions, timestamp,original_frame):
        import time
        if not hasattr(self, '_last_call_time'):
            self._last_call_time = 0
        current_time = time.time()  # 获取当前系统时间（秒）
        if current_time - self._last_call_time >= 3:
            self.call_ai_hook_interface(processed_frame, timestamp,task.latitude,task.longitude)
            self._last_call_time = current_time
       
    def postprocess(self, raw_prediction: np.ndarray) -> List[Dict[str, Any]]:
        """后处理预测结果，返回易读的检测结果"""
        detections = []
        for result in raw_prediction:
            for box in result.boxes:
                # 获取坐标 (x1, y1, x2, y2)
                x1, y1, x2, y2 = box.xyxy[0].tolist()
                
                # 获取置信度和类别ID
                conf = box.conf.item()
                cls_id = box.cls.item()
                
                # 构建检测结果字典
                detection = {
                    'bbox': [float(x1), float(y1), float(x2), float(y2)],
                    'conf': conf,
                    'class_id': int(cls_id),
                    'class_name': self.classes[int(cls_id)]  # 使用 model.names 获取类别名称
                }
                detections.append(detection)
        return detections


if __name__ == "__main__":
    model_path = "/home/<USER>/project/carTrack/people_dr/train7/weights/best.onnx"
    obj=ObjectDetector('/home/<USER>/project/modelManger/models/poeple/config.yaml')
    print(obj.predict('/home/<USER>/project/carTrack/people_v4/images/Train/frame_000030.png'))