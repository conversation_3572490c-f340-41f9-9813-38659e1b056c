from abc import ABC, abstractmethod
import base64
from typing import Union, Any, Dict, Optional, BinaryIO
from PIL import Image
import cv2
import numpy as np
import yaml
from pathlib import Path
import requests
import io
import json
from config.model_config import ModelConfig
from concurrent.futures import ThreadPoolExecutor

class BasePredictor(ABC):
    def __init__(self, model_config: ModelConfig,task):
        """Initialize predictor from YAML or PKI (ZIP) file without extraction.
        
        Args:
            config_path: Path to YAML config or PKI (ZIP) file.
        """
        self.pki_zip = model_config.pki_zip
        self.config = model_config.config
        self.model_config=model_config
        self.task = task
        self._initialize_from_config()
        self.executor = ThreadPoolExecutor(max_workers=2) 
    def get_model_name(self): 
        return self.config.get('model_name')
    def get_predict_params(self): 
        return self.config.get('predict_params')
    def _get_model_file(self) -> BinaryIO:
       return self.model_config._get_model_file()
    def _get_file_by_key(self,key) -> BinaryIO:
       return self.model_config._get_file_by_key(key)
    def _load_config_from_file(self, config_path: Path) -> Dict[str, Any]:
        """Load YAML from a standalone file."""
        with open(config_path, 'r') as f:
            return yaml.safe_load(f)
    def _load_config_from_zip(self) -> Dict[str, Any]:
        """Load YAML from PKI (ZIP) without extraction."""
        if self.pki_zip is None:
            raise ValueError("PKI ZIP file not opened.")

        # Search for YAML in the ZIP
        yaml_files = [
            name for name in self.pki_zip.namelist()
            if name.endswith(('.yaml', '.yml'))
        ]

        if not yaml_files:
            raise FileNotFoundError("No YAML config found in PKI package.")
        
        # Use the first YAML found
        with self.pki_zip.open(yaml_files[0], 'r') as yaml_file:
            return yaml.safe_load(yaml_file)

    def _read_file_from_zip(self, file_path: str) -> BinaryIO:
        return self.model_config._read_file_from_zip(file_path)

    def __del__(self):
        """Ensure ZIP file is closed on object destruction."""
        self.model_config.__del__()

    def call_ai_hook_interface(self, callback_url, frame, timestamp,latitude,longitude,params={}):
        """提交任务到线程池，立即返回 Future 对象"""
        future = self.executor.submit(
            self._call_api_sync,  # 同步执行函数
            callback_url,
            frame,
            timestamp,latitude,longitude,params
        )
        return future  # 可 future.add_done_callback() 设置回调
    def _call_api_sync(self,callback_url,frame,timestamp,latitude,longitude,params):
        if callback_url is None:
            return
        """
        调用AI Hook接口
        
        :param device_sn: 设备序列号
        :param business_id: 业务ID
        :param rules_data: 规则数据列表，每个元素应包含ruleId和image，可选frame信息
        :return: 接口响应
        """
        # 接口地址
        url = callback_url
        
        # 构造请求头
        headers = {
            "Content-Type": "application/json"
        }
        _, buffer = cv2.imencode('.jpg', frame)  # 也可以选择 '.png'
    # 转换为 Base64
        base64_str = base64.b64encode(buffer).decode('utf-8')
       
        # 构造请求体
        payload = {
            "app_id": "test_app",  # 根据实际情况修改或设为None
            "device": self.task.device_sn,
            "event": "message",    # 固定值
            "timestamp": int(timestamp * 1000),  # 当前时间戳(毫秒)
            "business_id":  self.task.business_id,
            "data": {
                 "frame": {
                  "flight": {
                     "latitude": latitude,
                     "longitude": longitude
                   }

            },
                "rules": [
        {
            "rule_id": self.model_config.get_model_id(),
            "image": base64_str,  # 替换为实际的base64编码图像
            **params,
           
        }
    ]
            }
        }
        
        try:
            # print('请求:'+json.dumps(payload,ensure_ascii=False))
            # 发送POST请求
            response = requests.post(
                url,
                headers=headers,
                data=json.dumps(payload),
                timeout=10  # 设置超时时间
            )
            
            # 检查响应状态
            response.raise_for_status()
            # 返回JSON响应
            return response.json()
            
        except requests.exceptions.RequestException as e:
            print(f"请求接口失败: {e}")
            return None



    @abstractmethod
    def _initialize_from_config(self):
        """Initialize model from config (load weights from ZIP if needed)."""
        pass


    def callback(self,task,processed_frame, predictions,timestamp,original_frame):
        """Initialize model from config (load weights from ZIP if needed)."""
        pass


    @abstractmethod
    def preprocess(self, image_input: Union[str, Image.Image, np.ndarray]) -> np.ndarray:
        """Preprocess input image."""
        pass

    @abstractmethod
    def predict(self, image_array: np.ndarray) -> Any:
        """Run prediction on preprocessed input."""
        pass

    @abstractmethod
    def visualize(self, frame: np.ndarray,out) -> Any:
        """将结果展示在图片上."""
        pass
    # def callback(self,frame,out):


    @abstractmethod
    def postprocess(self, raw_prediction: Any) -> Any:
        """Post-process prediction results."""
        pass

    def predict_pipeline(self, image_input: Union[str, Image.Image, np.ndarray]) -> Any:
        """Run full prediction pipeline."""
        processed_array = self.preprocess(image_input)
        raw_result = self.predict(processed_array)
        outs =self.postprocess(raw_result)
        return outs
