from typing import Union, Any, Dict, List, Tuple

import cv2
from PIL import Image
import numpy as np
from pathlib import Path

from sklearn.cluster import DBSCAN
from ultralytics.utils.ops import scale_image

from predict_method.base_predict import BasePredictor
from utils.image_util import cv2AddChineseText

from runtime import EngineModelRuntime
from utils.image_util import cv2AddChineseText


class FishManCrowdEngine(BasePredictor):
    def _initialize_from_config(self):
        """从配置文件初始化目标检测模型和相关参数"""
        # 初始化模型参数
        self.confidence_threshold = self.config.get('confidence_threshold', 0.5)
        self.predict_params = self.config.get('predict_params', {})
        self.input_size_people = tuple(self.config['input_size_people'])  # 期望的输入尺寸 (height, width)
        self.input_size_river = tuple(self.config['input_size_river'])  # 期望的输入尺寸 (height, width)
        self.classes_people = self.config['classes_people']  # 类别名称列表
        self.classes_river = self.config['classes_river']  # 类别名称列表
        self.people_model = EngineModelRuntime(self._get_file_by_key('people_model_filename'), input_size=self.input_size_people,
                                          predict_params=self.predict_params)


        self.river_model=EngineModelRuntime(self._get_file_by_key('river_model_filename'),input_size=self.input_size_river,
                                            predict_params=self.predict_params,task='segment')

        self.fish_man = Fish_Man_Main(people_model=self.people_model, river_model=self.river_model)
        self.is_fish_man=False

        # print(f"Initialized detector with: {self.config}")


    def preprocess(self, image_input: Union[str, Image.Image, np.ndarray]) -> np.ndarray:
        """预处理图像数据以适应模型输入要求"""
        # 如果输入是文件路径，加载图像
        if isinstance(image_input, str):
            image = cv2.imread(image_input)
        elif isinstance(image_input, Path):
            image = cv2.imread(str(image_input))
        elif isinstance(image_input, Image.Image):
            image = np.array(image_input)

        else:
            image = image_input
        # self.img_height, self.img_width = image.shape[:2]
        #         # 将图像颜色空间从 BGR 转换为 RGB
        # img_rgb = cv.cvtColor(image, cv.COLOR_BGR2RGB)

        # # 保持宽高比，进行 letterbox 填充, 使用模型要求的输入尺寸
        # img, self.ratio, (self.dw, self.dh) = self.letterbox(img_rgb, new_shape=(self.session.input_width, self.session.input_height))
        return image

    def callback(self, task, processed_frame, predictions, timestamp,original_frame):
        # if len(predictions) == 0:
        #     return
        import time
        if not hasattr(self, '_last_call_time'):
            self._last_call_time = 0

        current_time = time.time()  # 获取当前系统时间（秒）
        # print(self.is_fish_man)
        if current_time - self._last_call_time >= 3 and self.is_fish_man:
            s=self.call_ai_hook_interface(task.callback_url, processed_frame, timestamp, task.latitude, task.longitude)
            self._last_call_time = current_time
            self.is_fish_man=False
            # print(f'回调成功:{s}')

    def predict(self, image_array: np.ndarray) -> np.ndarray:
        """执行目标检测预测"""
        # return [{}]

        return image_array

    def visualize(self, frame: np.ndarray, out) -> np.ndarray:
        out_img,flag = self.fish_man.predict_main(frame)
        self.is_fish_man=flag
        return out_img

    def postprocess(self, raw_prediction: np.ndarray) -> List[Dict[str, Any]]:
        """后处理预测结果，返回易读的检测结果"""
        detections = []
        # for result in raw_prediction:
        #     for box in result.boxes:
        #         # 获取坐标 (x1, y1, x2, y2)
        #         x1, y1, x2, y2 = box.xyxy[0].tolist()
        #
        #         # 获取置信度和类别ID
        #         conf = box.conf.item()
        #         cls_id = box.cls.item()
        #         # 构建检测结果字典
        #         detection = {
        #             'bbox': [float(x1), float(y1), float(x2), float(y2)],
        #             'conf': conf,
        #             'class_id': int(cls_id),
        #             'class_name': self.classes[int(cls_id)]  # 使用 model.names 获取类别名称
        #         }
        #         detections.append(detection)
        return detections


class Fish_Man_Main:
    def __init__(self,people_model,river_model):
        self.people_model = people_model
        self.river_model = river_model
        self.width_thershold=3.5
        self.count=0
        self.is_fish_man=False
        self.is_fish_man_count=0

    def get_mask_contours(self,masks, orig_shape):
        """
        提取所有掩码的边界点
        :param masks: YOLO输出的掩码数据 (n, H, W)
        :param orig_shape: 原始图像尺寸 (h, w)
        :return: 轮廓列表 [contour1, contour2, ...]
        """
        contours_all = []
        for mask in masks:
            # 缩放掩码到原始尺寸
            scaled_mask = scale_image(mask, orig_shape)  # [5](@ref)
            # 二值化并提取轮廓
            _, bin_mask = cv2.threshold(scaled_mask, 0.5, 255, cv2.THRESH_BINARY)
            contour, _ = cv2.findContours(
                bin_mask.astype(np.uint8),
                cv2.RETR_EXTERNAL,
                cv2.CHAIN_APPROX_SIMPLE
            )
            if contour:
                contours_all.append(contour[0])  # 取主要轮廓
                # flat_contour = np.vstack(contour).squeeze()
                # contours_all.append(flat_contour)  # 直接存储展平数组
        return contours_all

    def min_distance_to_contour(self,point, contours):
        """
        兼容列表和NumPy数组的轮廓处理
        :param point: 目标点 (x, y)
        :param contour: 轮廓数据（列表或数组）
        :return: 最短距离
        """
        min_dist = float('inf')
        for contour in contours:
            # 统一转换为NumPy数组处理
            contour_array = np.array(contour).reshape(-1, 2)
            for pt in contour_array:
                dist = np.linalg.norm(pt - point)
                min_dist = min(min_dist, dist)

        return min_dist

    def predict_main(self, frame):
        self.is_fish_man_count=0
        results_person = self.people_model.predict(frame)
        results_river = self.river_model.predict(frame)
        img_mask=results_river[0].plot(boxes=False)
        # cv2.imwrite(r'E:\A_worker\W_XM\ZLT_api_test\modelManger_deply\predict_method\img_mask.jpg', img_mask)

        # h, w = image_array.shape[:2]
        #
        # # 生成合并河流掩码
        # combined_mask = np.zeros((h, w), dtype=np.uint8)
        # if results_river[0].masks is not None:
        #     masks = results_river[0].masks.data.cpu().numpy()
        #     for mask in masks:
        #         resized_mask = cv2.resize(mask, (w, h))
        #         combined_mask = np.where(resized_mask > 0.5, 255, combined_mask)
        #
        # # 创建彩色掩码
        # color_mask = np.zeros_like(image_array, dtype=np.uint8)
        # color_mask[combined_mask == 255] = (0, 255, 0)  # BGR绿色
        #
        # # 创建绘图画布 (原图副本)
        # img_mask = image_array.copy()
        # img_mask[combined_mask == 255] = cv2.addWeighted(
        #     image_array[combined_mask == 255], 0.7,
        #     color_mask[combined_mask == 255], 0.3, 0
        # )

        # 获取河流模型结果
        # boxes_river = results_river[0].boxes.xywh.cpu().numpy()  # 获取预测框，格式为 [x_center, y_center, width, height]
        # masks_river = results_river[0].masks.data.cpu().numpy()
        if results_river[0].masks is not None:
            masks_river = results_river[0].masks.data.cpu().numpy()
        else:
            masks_river = None  # 或跳过后续处理
            return frame

        river_masks_contours_list = self.get_mask_contours(masks_river, frame.shape[:2])

        # 获取人模型结果
        boxes_person = results_person[0].boxes.xywh.cpu().numpy()  # 获取预测框，格式为 [x_center, y_center, width, height]

        # 输出框的坐标、类别和置信度
        all_fish_man_count = 0
        color = (0, 255, 0)  # 绿色边框
        for i in range(len(boxes_person)):
            x_center, y_center, width, height = boxes_person[i]
            x_bottom_center = int(x_center + (width / 2))
            y_bottom_center = int(y_center)
            width = int(width)
            height = int(height)
            min_distance = self.min_distance_to_contour((x_bottom_center, y_bottom_center), river_masks_contours_list)
            if min_distance < width * self.width_thershold:
                self.is_fish_man_count+=1
                # self.is_fish_man=True
                all_fish_man_count += 1

                x_left = x_center - width / 2
                y_left = y_center - height / 2
                x_right = x_center + width / 2
                y_right = y_center + height / 2

                text='垂钓者'
                (text_width, text_height), _ = cv2.getTextSize(str(text), cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)
                text_width = len(text) * 17
                img_mask = cv2.rectangle(img_mask, (int(x_left), int(y_left - text_height - 10)), (int(x_left + text_width), int(y_left)),
                                      (94,53,255), -1)

                img_mask=cv2.rectangle(img_mask, (int(x_left), int(y_left)), (int(x_right), int(y_right)), (94,53,255), 2)  # 绘制边界框
                img_mask=cv2AddChineseText(img_mask, text, (int(x_left), int(y_left) - 20),  # 显示结果
                            (255,255,255), 16)
        # 绘制半透明背景（右边）
        # alpha = 0.6
        # overlay = img_mask.copy()
        # img_mask = cv2.cvtColor(img_mask, cv2.COLOR_RGB2RGBA)
        if self.is_fish_man_count!=0:
            self.is_fish_man=True
            # print('不为0')
        elif self.is_fish_man_count==0:
            self.is_fish_man=False

        # cv2.rectangle(img_mask,
        #               (int(img_mask.shape[1]-150),20),(int(img_mask.shape[1]),60),(0, 0, 0,128), -1)
        # h,w=img_mask.shape[0],img_mask.shape[1]
        # overlay = img_mask.copy()
        # cv2.rectangle(overlay,
        #               (int(w-len('实时垂钓人数:111')*17),15),(int(w),60),
        #               (0, 0, 0), -1)
        # alpha = 0.4
        # img_mask = cv2.addWeighted(overlay, alpha, img_mask, 1 - alpha, 0)

        # img_mask = cv2.addWeighted(overlay, alpha, img_mask, 0, 0)
        # ==== 显示统计结果 ====
        img_mask=cv2AddChineseText(img_mask, f"实时垂钓人数: {all_fish_man_count}", (int(img_mask.shape[1]-150), 30),
                                   (255,255,255), 16, )
        self.count+=1
        # print(f'垂钓算法已处理{self.count}帧')
        # cv2.imwrite(f'E:/A_worker/W_XM/ZLT_api_test/modelManger_deply/test_video/test{self.count}.png',img_mask)
        return img_mask,self.is_fish_man




if __name__ == "__main__":
    model_path = "/home/<USER>/project/carTrack/people_dr/train7/weights/best.onnx"
    obj = ObjectDetector('/home/<USER>/project/modelManger/models/poeple/config.yaml')
    print(obj.predict('/home/<USER>/project/carTrack/people_v4/images/Train/frame_000030.png'))