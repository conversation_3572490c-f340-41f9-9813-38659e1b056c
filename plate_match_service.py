#!/usr/bin/env python3
"""
车牌匹配服务
提供HTTP接口，使用大模型匹配车牌列表与白名单
"""

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, Field
from typing import List, Dict, Any
import json
from openai import OpenAI
import uvicorn
try:
    from json_repair import repair_json  # type: ignore
    JSON_REPAIR_AVAILABLE = True
except ImportError:
    JSON_REPAIR_AVAILABLE = False
    print("警告: json-repair 库未安装，将使用标准JSON解析")

app = FastAPI(title="车牌匹配服务", description="使用大模型匹配车牌列表与白名单")

class PlateInfo(BaseModel):
    """车牌信息"""
    id: str = Field(..., description="车牌唯一标识")
    plate_number: str = Field(..., description="车牌号码")

# 配置OpenAI客户端
client = OpenAI(
    api_key="gpustack_5e7a262b5803b6bc_16b314c2d6113ca26b285e5190180f24",
    base_url="http://**************:30001/v1"
)

class PlateMatchRequest(BaseModel):
    """车牌匹配请求"""
    detected_plates: List[PlateInfo] = Field(
        ...,
        description="检测到的车牌列表",
        example=[
            {"id": "det_001", "plate_number": "赣AZ36E1"},
            {"id": "det_002", "plate_number": "川ADM8396"},
            {"id": "det_003", "plate_number": "京A12345"}
        ]
    )
    whitelist_plates: List[str] = Field(
        ...,
        description="白名单车牌列表",
        example=[
            "粤BD2589", "沪AF3695", "津EK7852", "渝GZ1234", "冀JX4567",
            "晋LM7890", "辽NB8520", "吉PC9631", "黑RD1478", "苏SE2580",
            "浙TF3691", "川ADM8336", "闽VH5823", "赣WI6934", "鲁XJ7145",
            "豫YK8256", "鄂ZL9367", "湘AM1478", "粤BN2589", "赣AZ36E2"
        ]
    )

class PlateMatchResult(BaseModel):
    """单个车牌匹配结果"""
    detected_id: str = Field(..., description="检测车牌ID")
    detected_plate: str = Field(..., description="检测到的车牌号")
    whitelist_plate: str = Field(..., description="匹配的白名单车牌号")
    similarity: float = Field(..., description="相似度评分(0-1)")

class PlateMatchResponse(BaseModel):
    """车牌匹配响应"""
    success: bool = Field(..., description="匹配是否成功", example=True)
    matched_plates: List[PlateMatchResult] = Field(
        ...,
        description="最相似的2个匹配结果",
        example=[
            {
                "detected_id": "det_001",
                "detected_plate": "赣AZ36E1",
                "whitelist_plate": "赣AZ36E2",
                "similarity": 0.95
            },
            {
                "detected_id": "det_002",
                "detected_plate": "川ADM8396",
                "whitelist_plate": "川ADM8336",
                "similarity": 0.85
            }
        ]
    )
    message: str = Field(..., description="匹配结果消息", example="找到最相似的 2 个车牌匹配")

def load_default_whitelist() -> List[str]:
    """加载默认白名单"""
    try:
        with open('/home/<USER>/modelManger_deply/car_plate', 'r', encoding='utf-8') as f:
            return [line.strip() for line in f.readlines() if line.strip()]
    except Exception as e:
        print(f"加载默认白名单失败: {e}")
        return []

def match_plates_with_ai(detected_plates: List[PlateInfo], whitelist_plates: List[str]) -> Dict[str, Any]:
    """使用大模型匹配车牌"""
    try:
        # 构建提示词
        detected_text = ";".join([f"{p.plate_number}" for p in detected_plates])
        whitelist_text = ";".join(whitelist_plates)

        # 创建检测车牌号到ID的映射
        detected_map = {p.plate_number: p.id for p in detected_plates}

        prompt = f"""请分析以下检测到的车牌列表和白名单车牌列表，从所有可能的匹配中找出最相似的2个车牌。

检测到的车牌：{detected_text}
白名单车牌：{whitelist_text}

要求：
1. 比较所有检测车牌与所有白名单车牌的相似性
2. 从所有匹配组合中选出相似度最高的2个匹配
3. 考虑字符相似性、位置相似性等因素
4. 给出相似度评分（0-1之间）
5. 按照以下JSON格式返回结果：
6. 不需要完全匹配，按照匹配度即可
7. 不需要多余的解释

输出以下格式：
{{
  "matches": [
    {{
      "detected_plate": "检测到的车牌1",
      "whitelist_plate": "匹配的白名单车牌1",
      "similarity": 0.95
    }},
    {{
      "detected_plate": "检测到的车牌2",
      "whitelist_plate": "匹配的白名单车牌2",
      "similarity": 0.85
    }}
  ]
}}

只返回相似度最高的最高2个匹配结果，如果所有匹配的相似度都很低（<0.3），则返回空数组。"""
        print(prompt)
        # 调用大模型
        response = client.chat.completions.create(
            model='deepseek-r1-0528-qwen3-8b',
            messages=[{
                "role": "user",
                "content": prompt
            }],
            max_tokens=2000,
            temperature=0.1
        )
        
        result_text = response.choices[0].message.content.strip()
        print(result_text)
        
        # 尝试解析JSON结果
        try:
            # 首先尝试使用 json-repair 修复JSON
            if JSON_REPAIR_AVAILABLE:
                try:
                    # 查找JSON部分
                    json_start = result_text.find('{')
                    json_end = result_text.rfind('}') + 1

                    if json_start != -1 and json_end > json_start:
                        json_content = result_text[json_start:json_end]
                        # 使用 json-repair 修复JSON
                        repaired_json = repair_json(json_content)
                        parsed_result = json.loads(repaired_json)
                        # 处理匹配结果，添加ID信息
                        processed_matches = []
                        if "matches" in parsed_result:
                            for match in parsed_result["matches"]:
                                detected_plate = match.get("detected_plate", "")
                                whitelist_plate = match.get("whitelist_plate", "")
                                similarity = match.get("similarity", 0.0)

                                processed_matches.append({
                                    "detected_id": detected_map.get(detected_plate, ""),
                                    "detected_plate": detected_plate,
                                    "whitelist_plate": whitelist_plate,
                                    "similarity": similarity
                                })

                        return {
                            "success": True,
                            "data": {"matches": processed_matches},
                            "raw_response": result_text,
                            "repaired_json": repaired_json
                        }
                except Exception as repair_error:
                    print(f"json-repair 修复失败: {repair_error}")
                    # 继续使用标准JSON解析

            # 标准JSON解析
            json_start = result_text.find('{')
            json_end = result_text.rfind('}') + 1

            if json_start != -1 and json_end > json_start:
                json_content = result_text[json_start:json_end]
                parsed_result = json.loads(json_content)
                # 处理匹配结果，添加ID信息
                processed_matches = []
                if "matches" in parsed_result:
                    for match in parsed_result["matches"]:
                        detected_plate = match.get("detected_plate", "")
                        whitelist_plate = match.get("whitelist_plate", "")
                        similarity = match.get("similarity", 0.0)

                        processed_matches.append({
                            "detected_id": detected_map.get(detected_plate, ""),
                            "detected_plate": detected_plate,
                            "whitelist_plate": whitelist_plate,
                            "similarity": similarity
                        })

                return {
                    "success": True,
                    "data": {"matches": processed_matches},
                    "raw_response": result_text
                }
            else:
                return {
                    "success": False,
                    "error": "无法找到有效的JSON格式",
                    "raw_response": result_text
                }

        except json.JSONDecodeError as e:
            return {
                "success": False,
                "error": f"JSON解析失败: {str(e)}",
                "raw_response": result_text
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": f"大模型调用失败: {str(e)}"
        }

@app.post("/match_plates", response_model=PlateMatchResponse)
async def match_plates(request: PlateMatchRequest):
    """
    车牌匹配接口
    
    Args:
        request: 包含检测车牌列表和白名单车牌列表的请求
        
    Returns:
        匹配结果
    """
    try:
        # 验证输入
        if not request.detected_plates:
            raise HTTPException(status_code=400, detail="检测车牌列表不能为空")
        
        # 如果没有提供白名单，使用默认白名单
        whitelist = request.whitelist_plates if request.whitelist_plates else load_default_whitelist()
        
        if not whitelist:
            raise HTTPException(status_code=400, detail="白名单车牌列表不能为空")
        
        print(f"开始匹配车牌:")
        print(f"  检测车牌: {request.detected_plates}")
        print(f"  白名单车牌: {whitelist}")
        
        # 调用大模型进行匹配
        match_result = match_plates_with_ai(request.detected_plates, whitelist)
        
        if match_result["success"]:
            matches = match_result["data"].get("matches", [])
            # 转换为PlateMatchResult对象
            match_results = [
                PlateMatchResult(
                    detected_id=match["detected_id"],
                    detected_plate=match["detected_plate"],
                    whitelist_plate=match["whitelist_plate"],
                    similarity=match["similarity"]
                ) for match in matches
            ]

            return PlateMatchResponse(
                success=True,
                matched_plates=match_results,
                message=f"找到最相似的 {len(match_results)} 个车牌匹配"
            )
        else:
            return PlateMatchResponse(
                success=False,
                matched_plates=[],
                message=f"匹配失败: {match_result.get('error', '未知错误')}"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        print(f"车牌匹配服务异常: {e}")
        raise HTTPException(status_code=500, detail=f"服务内部错误: {str(e)}")

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "service": "plate_match_service"}

@app.get("/whitelist")
async def get_whitelist():
    """获取默认白名单接口"""
    try:
        whitelist = load_default_whitelist()
        return {
            "success": True,
            "whitelist": whitelist,
            "count": len(whitelist)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取白名单失败: {str(e)}")

@app.post("/test_match")
async def test_match():
    """测试匹配接口"""
    test_request = PlateMatchRequest(
        detected_plates=[
            PlateInfo(id="det_001", plate_number="赣AZ36E1"),
            PlateInfo(id="det_002", plate_number="川ADM8396"),
            PlateInfo(id="det_003", plate_number="京A12345")
        ],
        whitelist_plates=[
            "粤BD2589", "沪AF3695", "津EK7852", "渝GZ1234", "冀JX4567",
            "晋LM7890", "辽NB8520", "吉PC9631", "黑RD1478", "苏SE2580",
            "浙TF3691", "川ADM8336", "闽VH5823", "赣WI6934", "鲁XJ7145",
            "豫YK8256", "鄂ZL9367", "湘AM1478", "粤BN2589", "赣AZ36E2"
        ]
    )

    return await match_plates(test_request)

if __name__ == "__main__":
    print("启动车牌匹配服务...")
    print("API文档地址: http://localhost:8001/docs")
    print("健康检查: http://localhost:8001/health")
    print("测试接口: http://localhost:8001/test_match")
    
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=8001,
        log_level="info"
    )
