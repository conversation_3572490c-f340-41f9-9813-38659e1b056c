import paho.mqtt.client as mqtt
import uuid
import time
import json



class FlightMqtt:
    def __init__(self,broker,port,username,password,topic):
        self.client = mqtt.Client(callback_api_version=mqtt.CallbackAPIVersion.VERSION1)
        self.client.on_connect =self.on_connect
        self.client.on_message = self.on_message
        self.client.username_pw_set(username, password)
        self.client.connect(broker, port)
        self.client.loop_start()
        self.callback = None
        self.topic = f"thing/product/{topic}/osd"
    def set_callbak(self, callback):
        self.callback = callback
    def on_connect(self,client, userdata, flags, rc):
        if rc == 0:
            print("Connected to MQTT Broker!")
            self.client.subscribe(self.topic)
        else:
            print(f"Failed to connect, return code {rc}")

    def on_message(self,client, userdata, msg):
        if self.callback:
            self.callback(msg.topic,msg.payload.decode())
        #print(f"Received message on topic {msg.topic}: {msg.payload.decode()}")
    def __del__(self):
        self.client.loop_stop()
        self.client.disconnect()