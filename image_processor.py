import io
import base64
import numpy as np
from PIL import Image
from typing import Tuple, Union

class ImageProcessor:
    @staticmethod
    def decode_image(image_data: str) -> Image.Image:
        """解码图片数据（支持base64或URL）"""
        if image_data.startswith(('http://', 'https://')):
            import requests
            response = requests.get(image_data)
            image = Image.open(io.BytesIO(response.content))
        elif image_data.startswith('data:image'):
            # 处理base64编码图片
            header, encoded = image_data.split(",", 1)
            image = Image.open(io.BytesIO(base64.b64decode(encoded)))
        else:
            # 直接解码base64字符串
            image = Image.open(io.BytesIO(base64.b64decode(image_data)))
        
        return image.convert('RGB')

    @staticmethod
    def resize_image(image: Image.Image, target_size: Tuple[int, int]) -> Image.Image:
        """调整图片大小并保持宽高比，使用letterbox方法"""
        orig_width, orig_height = image.size
        target_width, target_height = target_size
        
        # 计算缩放比例
        width_ratio = target_width / orig_width
        height_ratio = target_height / orig_height
        ratio = min(width_ratio, height_ratio)
        
        # 计算新尺寸
        new_width = int(orig_width * ratio)
        new_height = int(orig_height * ratio)
        
        # 调整大小
        resized = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # 创建新图像并填充
        new_image = Image.new("RGB", (target_width, target_height), (114, 114, 114))
        new_image.paste(resized, ((target_width - new_width) // 2, 
                                (target_height - new_height) // 2))
        
        return new_image

    @staticmethod
    def normalize_image(image: np.ndarray, mean: Tuple[float, float, float] = (0.485, 0.456, 0.406),
                       std: Tuple[float, float, float] = (0.229, 0.224, 0.225)) -> np.ndarray:
        """标准化图像数据"""
        image = image / 255.0
        image = (image - mean) / std
        return image

    @staticmethod
    def prepare_input(image: Image.Image, input_shape: Tuple[int, int, int], 
                     normalize: bool = True) -> np.ndarray:
        """
        准备模型输入数据
        :param input_shape: (channels, height, width)
        """
        # 调整大小
        _, h, w = input_shape
        processed = ImageProcessor.resize_image(image, (w, h))
        
        # 转换为numpy数组
        array = np.array(processed, dtype=np.float32)
        
        # 转换通道顺序 (HWC to CHW)
        array = array.transpose(2, 0, 1)
        
        # 标准化
        if normalize:
            array = ImageProcessor.normalize_image(array)
        
        # 添加batch维度
        array = np.expand_dims(array, axis=0)
        
        return array