Initialized detector with: {'model_name': '人员识别', 'model_filename': 'people_v8.engine', 'input_size': [640, 640], 'classes': ['人'], 'predict_method': 'ObjectDetectorEngine', 'predict_params': {'conf': 0.3}}
Initialized detector with: {'model_name': '火焰烟雾识别', 'model_filename': 'fire_smoke_v3.engine', 'input_size': [640, 640], 'classes': ['火', '烟'], 'predict_method': 'ObjectDetectorEngine', 'predict_params': {'conf': 0.3}}
Initialized detector with: {'model_name': '车辆识别', 'model_filename': 'car_type_v11.engine', 'input_size': [640, 640], 'classes': ['自行车', '车', '面包车', '卡车', '三轮车', '带蓬三轮车', '公交车', '摩托车'], 'predict_method': 'ObjectDetectorEngine', 'predict_params': {'conf': 0.3}}
INFO:     Started server process [79381]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     *************:62658 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:62658 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     *************:59336 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:41270 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:35550 - "GET / HTTP/1.1" 404 Not Found
INFO:     185.218.84.40:44498 - "GET /userLogin.asp HTTP/1.1" 404 Not Found
INFO:     *************:59152 - "GET /userLogin.asp HTTP/1.1" 404 Not Found
INFO:     *************:36788 - "GET /userLogin.asp HTTP/1.1" 404 Not Found
INFO:     *************:57558 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:40762 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:43172 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:50296 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     *************:35030 - "POST /_ignition/execute-solution HTTP/1.1" 404 Not Found
INFO:     47.113.185.166:56274 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:42758 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:33592 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:33646 - "PRI %2A HTTP/2.0" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     *************:60840 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     *************:51532 - "PRI %2A HTTP/2.0" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     *************:55124 - "GET /sitemap.xml HTTP/1.1" 404 Not Found
INFO:     ************:30000 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:30000 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:46130 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:46303 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     *************:30382 - "GET /robots.txt HTTP/1.1" 404 Not Found
INFO:     *************:55024 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:44114 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:39368 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:35506 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:38190 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:38196 - "PRI %2A HTTP/2.0" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     *************:38220 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     *************:38232 - "PRI %2A HTTP/2.0" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     *************:38294 - "GET /.well-known/security.txt HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     *************:59188 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:38656 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:43638 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:32180 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:39872 - "GET http%3A//pingjs.qq.com/ping.js HTTP/1.1" 404 Not Found
INFO:     *************:54308 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:43244 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:47144 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:50488 - "GET http%3A//pingjs.qq.com/ping.js HTTP/1.1" 404 Not Found
INFO:     *************:50524 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:45784 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:34622 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     *************:61040 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     *************:38430 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:56458 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     *************:56460 - "GET /robots.txt HTTP/1.1" 404 Not Found
INFO:     *************:56462 - "GET /sitemap.xml HTTP/1.1" 404 Not Found
INFO:     8.219.209.185:35472 - "GET http%3A//passport.baidu.com/ HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     *************:64128 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:38384 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:38176 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:53308 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     *************:36148 - "GET /aaa9 HTTP/1.1" 404 Not Found
INFO:     *************:47044 - "GET /aab8 HTTP/1.1" 404 Not Found
INFO:     *************:47050 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     *************:60182 - "GET http%3A//passport.baidu.com/ HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     *************:47266 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:41002 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:34380 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:47674 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:48374 - "GET http%3A//clientapi.ipip.net/echo.php?info=20250702005705 HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     *************:36114 - "GET / HTTP/1.1" 404 Not Found
INFO:     ***************:61812 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:33438 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:50736 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:22738 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:40974 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:57986 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:56466 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:56482 - "PRI %2A HTTP/2.0" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     *************:56524 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     *************:58994 - "PRI %2A HTTP/2.0" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     *************:59040 - "GET /robots.txt HTTP/1.1" 404 Not Found
INFO:     *************:50832 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:56232 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:34304 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:40598 - "PRI %2A HTTP/2.0" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     *************:44416 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     *************:44428 - "PRI %2A HTTP/2.0" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     *************:34906 - "GET /robots.txt HTTP/1.1" 404 Not Found
INFO:     185.226.196.8:55885 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     *************:45542 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:58742 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:52220 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     *************:58768 - "GET / HTTP/1.1" 404 Not Found
INFO:     64.62.197.32:21520 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     *************:60852 - "GET http%3A//pingjs.qq.com/ping.js HTTP/1.1" 404 Not Found
INFO:     *************:30000 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:30630 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:30740 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     *************:31174 - "GET /robots.txt HTTP/1.1" 404 Not Found
INFO:     ************:30000 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:40974 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:43936 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:39586 - "GET http%3A//pingjs.qq.com/ping.js HTTP/1.1" 404 Not Found
INFO:     *************:53794 - "GET / HTTP/1.1" 404 Not Found
INFO:     47.237.177.222:51740 - "GET http%3A//passport.baidu.com/ HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     *************:46154 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:40622 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:34570 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:36388 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     *************:40150 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:57636 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:57652 - "PRI %2A HTTP/2.0" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     *************:52494 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     *************:52500 - "PRI %2A HTTP/2.0" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     *************:45458 - "GET /robots.txt HTTP/1.1" 404 Not Found
INFO:     *************:56984 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:52876 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     *************:41440 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:58610 - "GET / HTTP/1.1" 404 Not Found
INFO:     167.71.133.68:60654 - "GET /aaa9 HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     167.71.133.68:33198 - "GET /aab8 HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     167.71.133.68:49908 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     *************:49432 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     **************:34632 - "GET / HTTP/1.1" 404 Not Found
INFO:     ************:30000 - "GET / HTTP/1.1" 404 Not Found
INFO:     44.220.188.130:46960 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     44.220.188.130:55006 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     *************:56180 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:43326 - "GET / HTTP/1.1" 404 Not Found
INFO:     20.163.33.22:55798 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:59094 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     *************:54910 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     *************:52910 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:37256 - "GET / HTTP/1.1" 404 Not Found
INFO:     205.210.31.184:57608 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     222.182.62.149:7529 - "GET /ISAPI/Security/sessionLogin/capabilities?username=admin&random=10009639 HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     222.182.62.149:6871 - "GET /ISAPI/Security/sessionLogin/capabilities?username=admin&random=10009701 HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     222.182.62.149:8827 - "GET /ISAPI/Security/sessionLogin/capabilities?username=admin&random=10009753 HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     222.182.62.149:10256 - "GET /ISAPI/Security/sessionLogin/capabilities?username=admin&random=10009806 HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     222.182.62.149:8215 - "GET /ISAPI/Security/sessionLogin/capabilities?username=admin&random=10009858 HTTP/1.1" 404 Not Found
INFO:     *************:41380 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     *************:42644 - "GET / HTTP/1.1" 404 Not Found
INFO:     64.62.156.24:12972 - "GET / HTTP/1.1" 404 Not Found
INFO:     ************:30000 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:45530 - "GET / HTTP/1.1" 404 Not Found
INFO:     94.102.56.99:60800 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:54526 - "GET / HTTP/1.1" 404 Not Found
INFO:     3.131.215.38:32832 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     3.131.215.38:53912 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     *************:41780 - "GET / HTTP/1.1" 404 Not Found
INFO:     199.45.154.124:56412 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     199.45.154.124:41560 - "POST / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     137.184.171.49:34330 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:47158 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:40112 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     *************:32798 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     *************:53788 - "GET / HTTP/1.1" 404 Not Found
INFO:     185.247.137.154:55679 - "GET / HTTP/1.1" 404 Not Found
INFO:     185.247.137.175:45139 - "GET / HTTP/1.0" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     185.247.137.175:33663 - "GET /nice%20ports%2C/Trinity.txt.bak HTTP/1.0" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     *************:43602 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     167.94.146.50:51690 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     167.94.146.50:34044 - "GET / HTTP/1.1" 404 Not Found
INFO:     ************:30000 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     *************:45330 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:48166 - "GET / HTTP/1.1" 404 Not Found
INFO:     198.235.24.213:59860 - "GET / HTTP/1.1" 404 Not Found
INFO:     198.235.24.213:60370 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     *************:42528 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:46340 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:60486 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:54298 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:60138 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:63258 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:50130 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:58564 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:42494 - "CONNECT ipv4-internet.yandex.net%3A443 HTTP/1.1" 404 Not Found
INFO:     *************:45796 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:53134 - "GET / HTTP/1.1" 404 Not Found
INFO:     ***********:59054 - "GET / HTTP/1.1" 404 Not Found
INFO:     ***********:59060 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     ***********:29842 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     ************:30000 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:46930 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [79381]
Traceback (most recent call last):
  File "/home/<USER>/modelManger_deply/app.py", line 1, in <module>
    from fastapi import FastAPI, HTTPException, UploadFile, File
ModuleNotFoundError: No module named 'fastapi'
Traceback (most recent call last):
  File "/home/<USER>/modelManger_deply/app.py", line 3, in <module>
    from model_manager import ModelManager
  File "/home/<USER>/modelManger_deply/model_manager.py", line 9, in <module>
    from predict_method import get_process
  File "/home/<USER>/modelManger_deply/predict_method/__init__.py", line 2, in <module>
    from .object_detector import ObjectDetector
  File "/home/<USER>/modelManger_deply/predict_method/object_detector.py", line 10, in <module>
    from runtime import OnnxModelRuntime
  File "/home/<USER>/modelManger_deply/runtime/__init__.py", line 1, in <module>
    from .onnx_model_runtime import OnnxModelRuntime
  File "/home/<USER>/modelManger_deply/runtime/onnx_model_runtime.py", line 4, in <module>
    from ultralytics import YOLO
  File "/home/<USER>/miniconda3/envs/python312/lib/python3.12/site-packages/ultralytics/__init__.py", line 11, in <module>
    from ultralytics.models import NAS, RTDETR, SAM, YOLO, YOLOE, FastSAM, YOLOWorld
  File "/home/<USER>/miniconda3/envs/python312/lib/python3.12/site-packages/ultralytics/models/__init__.py", line 3, in <module>
    from .fastsam import FastSAM
  File "/home/<USER>/miniconda3/envs/python312/lib/python3.12/site-packages/ultralytics/models/fastsam/__init__.py", line 3, in <module>
    from .model import FastSAM
  File "/home/<USER>/miniconda3/envs/python312/lib/python3.12/site-packages/ultralytics/models/fastsam/model.py", line 6, in <module>
    from ultralytics.engine.model import Model
  File "/home/<USER>/miniconda3/envs/python312/lib/python3.12/site-packages/ultralytics/engine/model.py", line 8, in <module>
    import torch
  File "/home/<USER>/miniconda3/envs/python312/lib/python3.12/site-packages/torch/__init__.py", line 409, in <module>
    from torch._C import *  # noqa: F403
    ^^^^^^^^^^^^^^^^^^^^^^
ImportError: libcusparseLt.so.0: cannot open shared object file: No such file or directory
Initialized detector with: {'model_name': '人员识别', 'model_filename': 'people_v8.engine', 'input_size': [640, 640], 'classes': ['人'], 'predict_method': 'ObjectDetectorEngine', 'predict_params': {'conf': 0.3}}
Initialized detector with: {'model_name': '火焰烟雾识别', 'model_filename': 'fire_smoke_v3.engine', 'input_size': [640, 640], 'classes': ['火', '烟'], 'predict_method': 'ObjectDetectorEngine', 'predict_params': {'conf': 0.3}}
Initialized detector with: {'model_name': '车辆识别', 'model_filename': 'car_type_v11.engine', 'input_size': [640, 640], 'classes': ['自行车', '车', '面包车', '卡车', '三轮车', '带蓬三轮车', '公交车', '摩托车'], 'predict_method': 'ObjectDetectorEngine', 'predict_params': {'conf': 0.3}}
INFO:     Started server process [1352054]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [1352054]
Initialized detector with: {'model_name': '人员识别', 'model_filename': 'people_v8.engine', 'input_size': [640, 640], 'classes': ['人'], 'predict_method': 'ObjectDetectorEngine', 'predict_params': {'conf': 0.3}}
Initialized detector with: {'model_name': '火焰烟雾识别', 'model_filename': 'fire_smoke_v3.engine', 'input_size': [640, 640], 'classes': ['火', '烟'], 'predict_method': 'ObjectDetectorEngine', 'predict_params': {'conf': 0.3}}
Initialized detector with: {'model_name': '车辆识别', 'model_filename': 'car_type_v11.engine', 'input_size': [640, 640], 'classes': ['自行车', '车', '面包车', '卡车', '三轮车', '带蓬三轮车', '公交车', '摩托车'], 'predict_method': 'ObjectDetectorEngine', 'predict_params': {'conf': 0.3}}
INFO:     Started server process [1391765]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [1391765]
2025-07-15 09:46:29,712 - INFO - Loading weights from lib/deep_sort/deep_sort/deep/checkpoint/ckpt.t7... Done!
✅ 车牌检测模型加载成功
✅ 车牌白名单加载成功，共 3 个车牌
✅ 车牌识别器初始化完成
Initialized detector with: {'model_name': '人员识别', 'model_filename': 'people_v8.engine', 'input_size': [640, 640], 'classes': ['人'], 'predict_method': 'ObjectDetectorEngine', 'predict_params': {'conf': 0.3}}
Initialized detector with: {'model_name': '车辆测速', 'model_filename': 'car_type_v11.engine', 'input_size': [640, 640], 'classes': ['自行车', '车', '面包车', '卡车', '三轮车', '带蓬三轮车', '公交车', '摩托车'], 'predict_method': 'CarVelocityEngine', 'predict_params': {'conf': 0.3}}
Initialized detector with: {'model_name': '黄色网格禁停区违停识别', 'model_filename': 'yellow_grid_v6.engine', 'car_model': 'car_type_v11.engine', 'input_size': [640, 640], 'classes': ['违停车辆'], 'predict_method': 'CarViolationEngine', 'predict_params': {'conf': 0.4}}
Initialized detector with: {'model_name': '火焰烟雾识别', 'model_filename': 'fire_smoke_v3.engine', 'input_size': [640, 640], 'classes': ['火', '烟'], 'predict_method': 'ObjectDetectorEngine', 'predict_params': {'conf': 0.3}}
Initialized detector with: {'model_name': '骑车安全帽检测', 'model_filename': 'motor_helmet_v1.engine', 'input_size': [1280, 1280], 'classes': ['未佩戴头盔', '佩戴头盔', '人', '自行车'], 'alarms_classes': ['未佩戴头盔'], 'predict_method': 'MotorHelmetEngine', 'predict_params': {'conf': 0.3}}
Initialized detector with: {'model_name': '人员聚集识别', 'model_filename': 'people_v8_crowd.engine', 'input_size': [1280, 1280], 'classes': ['people'], 'predict_method': 'PersonCrowdEngine_2', 'predict_params': {'conf': 0.3}}
✅ 横幅检测模型加载成功
✅ 横幅识别器初始化完成
Initialized detector with: {'model_name': '车辆识别', 'model_filename': 'car_type_v11.engine', 'input_size': [640, 640], 'classes': ['自行车', '车', '面包车', '卡车', '三轮车', '带蓬三轮车', '公交车', '摩托车'], 'predict_method': 'ObjectDetectorEngine', 'predict_params': {'conf': 0.3}}
INFO:     Started server process [1441749]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     **************:47872 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:47888 - "GET /favicon.ico HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     **************:56294 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:56296 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     **************:49330 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:49334 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     3.131.215.38:37990 - "GET / HTTP/1.1" 404 Not Found
INFO:     3.131.215.38:37992 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     **************:33342 - "GET http%3A//ossweb-img.qq.com/images/js/eas/eas.js HTTP/1.1" 404 Not Found
INFO:     **************:57338 - "GET / HTTP/1.1" 404 Not Found
INFO:     Started server process [1508866]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8001 (Press CTRL+C to quit)
启动车牌匹配服务...
API文档地址: http://localhost:8001/docs
健康检查: http://localhost:8001/health
测试接口: http://localhost:8001/test_match
INFO:     **************:51452 - "GET /docs HTTP/1.1" 200 OK
INFO:     **************:51451 - "GET /openapi.json HTTP/1.1" 200 OK
INFO:     **************:51499 - "GET /docs HTTP/1.1" 200 OK
INFO:     **************:51499 - "GET /openapi.json HTTP/1.1" 200 OK
==================================================
接收到启动任务请求:
  任务ID: 1944962216901419010
  设备SN: 1581F6Q8D24AA00GRS99
  应用代码: car
  输入流: rtmp://*************:19403/live/1581F6Q8D24AA00GRS99_81-0-0?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************.fJO8Kdj6hBA6YN0jNxAJFfLMpbeyaMqCqqvA1qmOR7A
  输出流: rtmp://*************:19403/live/1944962216901419010
  回调URL: http://***************:3100/hisun-boot/dock/zhimou/ai-hook2
  MQTT配置: 未提供，将使用默认配置
==================================================
解析的模型列表: ['car']
Initialized detector with: {'model_name': '车辆识别', 'model_filename': 'car_type_v11.engine', 'input_size': [640, 640], 'classes': ['自行车', '车', '面包车', '卡车', '三轮车', '带蓬三轮车', '公交车', '摩托车'], 'predict_method': 'ObjectDetectorEngine', 'predict_params': {'conf': 0.3}}
INFO:     **************:59016 - "POST /start HTTP/1.1" 200 OK
['cuda', 'drm']
Connected to MQTT Broker!
Video stream format: <av.VideoFormat cuda, 1920x1080>
Video stream color range: 1
分辨率从 0x0 变为 1920x1080
Loading /tmp/tmplsq34nig.engine for TensorRT inference...
Loading /tmp/tmplsq34nig.engine for TensorRT inference...
[07/15/2025-11:28:28] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
Loading /tmp/tmplsq34nig.engine for TensorRT inference...
[07/15/2025-11:28:28] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
Loading /tmp/tmplsq34nig.engine for TensorRT inference...
[07/15/2025-11:28:28] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
[07/15/2025-11:28:28] [TRT] [I] Loaded engine size: 45 MiB
[07/15/2025-11:28:28] [TRT] [I] Loaded engine size: 45 MiB
[07/15/2025-11:28:28] [TRT] [I] Loaded engine size: 45 MiB
[07/15/2025-11:28:28] [TRT] [I] Loaded engine size: 45 MiB
[07/15/2025-11:28:28] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +1, GPU +55, now: CPU 1, GPU 212 (MiB)
[07/15/2025-11:28:28] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +2, GPU +109, now: CPU 3, GPU 321 (MiB)
[07/15/2025-11:28:28] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +2, GPU +55, now: CPU 4, GPU 321 (MiB)
[07/15/2025-11:28:28] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +0, GPU +54, now: CPU 4, GPU 375 (MiB)
INFO:     **************:49187 - "GET /docs HTTP/1.1" 200 OK
INFO:     **************:49186 - "GET /openapi.json HTTP/1.1" 200 OK
Task 1944962216901419010 - Attempting to restart FFmpeg (attempt 1)
Task 1944962216901419010 - FFmpeg restarted successfully
Warning: Thread for task 1944962216901419010 did not stop gracefully
INFO:     **************:59510 - "POST /stop HTTP/1.1" 200 OK
INFO:     206.168.34.66:51356 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     **************:30000 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:39883 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:40206 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     **************:40718 - "GET /robots.txt HTTP/1.1" 404 Not Found
INFO:     ************:30000 - "GET / HTTP/1.1" 404 Not Found
INFO:     205.210.31.55:63632 - "GET / HTTP/1.1" 404 Not Found
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [1508866]
INFO:     **************:43508 - "GET http%3A//azenv.net/ HTTP/1.1" 404 Not Found
INFO:     **************:42070 - "GET /chs/js/lang_zh_tw.js HTTP/1.1" 404 Not Found
INFO:     **************:42080 - "GET /index.php HTTP/1.1" 404 Not Found
INFO:     **************:42082 - "GET /index.php HTTP/1.1" 404 Not Found
INFO:     **************:42086 - "GET /login/stylesheets/theme.css HTTP/1.1" 404 Not Found
INFO:     **************:42088 - "GET /stylesheets/theme.css HTTP/1.1" 404 Not Found
INFO:     **************:42090 - "GET /customer/js/lang_zh_tw.js HTTP/1.1" 404 Not Found
INFO:     **************:42092 - "GET /ips/index.php HTTP/1.1" 404 Not Found
INFO:     **************:42094 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:42096 - "GET /public/stylesheets/theme.css HTTP/1.1" 404 Not Found
INFO:     **************:45744 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:46442 - "CONNECT google.com%3A443 HTTP/1.1" 404 Not Found
INFO:     **************:45512 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:32796 - "PRI %2A HTTP/2.0" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     **************:58164 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     **************:58224 - "PRI %2A HTTP/2.0" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     **************:54402 - "GET /.well-known/security.txt HTTP/1.1" 404 Not Found
INFO:     **************:35078 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:42388 - "PRI %2A HTTP/2.0" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     **************:56452 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     **************:36046 - "PRI %2A HTTP/2.0" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     **************:36112 - "GET /wiki HTTP/1.1" 404 Not Found
INFO:     **************:35678 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     **************:45740 - "GET http%3A//ossweb-img.qq.com/images/js/eas/eas.js HTTP/1.1" 404 Not Found
INFO:     **************:52816 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:44166 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:44178 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     8.219.209.185:60228 - "GET http%3A//passport.baidu.com/ HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     **************:40030 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:40034 - "GET /login HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     *************:41768 - "GET http%3A//azenv.net/ HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     **************:50664 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:58769 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     **************:52447 - "GET http%3A//api.ipify.org/?format=json HTTP/1.1" 404 Not Found
INFO:     **************:44247 - "CONNECT www.shadowserver.org%3A443 HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     **************:39310 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:53734 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:53750 - "PRI %2A HTTP/2.0" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     **************:42242 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     **************:42258 - "PRI %2A HTTP/2.0" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     **************:51710 - "GET /security.txt HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     **************:60160 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:60162 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     **************:60164 - "GET /robots.txt HTTP/1.1" 404 Not Found
INFO:     **************:60170 - "GET /sitemap.xml HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     152.32.151.39:41662 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     **************:38150 - "GET /dispatch.asp HTTP/1.1" 404 Not Found
INFO:     149.50.96.114:60538 - "GET /dispatch.asp HTTP/1.1" 404 Not Found
INFO:     **************:63382 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:44301 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:35847 - "GET /api/session/properties HTTP/1.1" 404 Not Found
INFO:     **************:58195 - "GET /console HTTP/1.1" 404 Not Found
INFO:     **************:38067 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     **************:56749 - "GET /admin/ HTTP/1.1" 404 Not Found
INFO:     **************:47707 - "GET /owncloud/status.php HTTP/1.1" 404 Not Found
INFO:     **************:49109 - "GET /status.php HTTP/1.1" 404 Not Found
INFO:     **************:55439 - "GET /helpdesk/WebObjects/Helpdesk.woa HTTP/1.1" 404 Not Found
INFO:     185.247.137.161:44933 - "GET / HTTP/1.1" 404 Not Found
INFO:     185.247.137.162:51175 - "GET / HTTP/1.0" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     185.247.137.162:58197 - "GET /nice%20ports%2C/Trinity.txt.bak HTTP/1.0" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     *************:43672 - "GET http%3A//azenv.net/ HTTP/1.1" 404 Not Found
INFO:     **************:57512 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:57402 - "GET http%3A//azenv.net/ HTTP/1.1" 404 Not Found
INFO:     **************:51328 - "GET http%3A//azenv.net/ HTTP/1.1" 404 Not Found
INFO:     ************:30000 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:30000 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:38405 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:38653 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     **************:39254 - "GET /robots.txt HTTP/1.1" 404 Not Found
INFO:     **************:46710 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:52404 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:52440 - "PRI %2A HTTP/2.0" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     **************:57418 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     **************:57450 - "PRI %2A HTTP/2.0" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     **************:57542 - "GET /sitemap.xml HTTP/1.1" 404 Not Found
INFO:     **************:59210 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     **************:14494 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     8.219.209.185:55980 - "GET http%3A//passport.baidu.com/ HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     **************:60738 - "GET / HTTP/1.1" 404 Not Found
INFO:     128.199.34.41:42582 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:64018 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:38910 - "GET http%3A//azenv.net/ HTTP/1.1" 404 Not Found
INFO:     **************:8490 - "GET / HTTP/1.1" 404 Not Found
INFO:     *************:60430 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:39410 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:60541 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     **************:23405 - "GET http%3A//api.ipify.org/?format=json HTTP/1.1" 404 Not Found
INFO:     **************:16025 - "CONNECT www.shadowserver.org%3A443 HTTP/1.1" 404 Not Found
INFO:     **************:35502 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:39691 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     **************:18587 - "GET http%3A//api.ipify.org/?format=json HTTP/1.1" 404 Not Found
INFO:     **************:5377 - "CONNECT www.shadowserver.org%3A443 HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
Traceback (most recent call last):
  File "/home/<USER>/modelManger_deply/plate_match_service.py", line 7, in <module>
    from fastapi import FastAPI, HTTPException
ModuleNotFoundError: No module named 'fastapi'
INFO:     Started server process [1573414]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8001 (Press CTRL+C to quit)
启动车牌匹配服务...
API文档地址: http://localhost:8001/docs
健康检查: http://localhost:8001/health
测试接口: http://localhost:8001/test_match
INFO:     **************:50963 - "GET /docs HTTP/1.1" 200 OK
INFO:     **************:50963 - "GET /openapi.json HTTP/1.1" 200 OK
开始匹配车牌:
  检测车牌: [PlateInfo(id='det_001', plate_number='赣AZ36E1'), PlateInfo(id='det_002', plate_number='川ADM8396'), PlateInfo(id='det_003', plate_number='京A12345')]
  白名单车牌: ['粤BD2589', '沪AF3695', '津EK7852', '渝GZ1234', '冀JX4567', '晋LM7890', '辽NB8520', '吉PC9631', '黑RD1478', '苏SE2580', '浙TF3691', '川ADM8336', '闽VH5823', '赣WI6934', '鲁XJ7145', '豫YK8256', '鄂ZL9367', '湘AM1478', '粤BN2589', '赣AZ36E2']
INFO:     **************:50962 - "POST /test_match HTTP/1.1" 200 OK
INFO:     **************:57618 - "GET /docs HTTP/1.1" 200 OK
INFO:     **************:41974 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     *************:36248 - "GET / HTTP/1.1" 404 Not Found
==================================================
接收到启动任务请求:
  任务ID: 1945300432451997698
  设备SN: 1581F6Q8D24AA00GRS99
  应用代码: car
  输入流: rtmp://*************:19403/live/1581F6Q8D24AA00GRS99_81-0-0?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTI2MzQzMzksInVzZXJuYW1lIjoiZWNhZGZmOTYtYTcxMS00YzMzLTk5MmYtNmNlNTViZDhiMWZkIn0.xiROZx6PYSmscJ_AajItWReosLUfSQdv_s7CnH489n4
  输出流: rtmp://*************:19403/live/1945300432451997698
  回调URL: http://***************:3100/hisun-boot/dock/zhimou/ai-hook2
  MQTT配置: 未提供，将使用默认配置
==================================================
解析的模型列表: ['car']
Initialized detector with: {'model_name': '车辆识别', 'model_filename': 'car_type_v11.engine', 'input_size': [640, 640], 'classes': ['自行车', '车', '面包车', '卡车', '三轮车', '带蓬三轮车', '公交车', '摩托车'], 'predict_method': 'ObjectDetectorEngine', 'predict_params': {'conf': 0.3}}
INFO:     **************:44482 - "POST /start HTTP/1.1" 200 OK
['cuda', 'drm']
Connected to MQTT Broker!
Video stream format: <av.VideoFormat cuda, 1440x1080>
Video stream color range: 1
分辨率从 0x0 变为 1440x1080
Loading /tmp/tmpch6l2y35.engine for TensorRT inference...
[07/16/2025-09:52:25] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
Loading /tmp/tmpch6l2y35.engine for TensorRT inference...
[07/16/2025-09:52:25] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
[07/16/2025-09:52:25] [TRT] [I] Loaded engine size: 45 MiB
[07/16/2025-09:52:25] [TRT] [I] Loaded engine size: 45 MiB
Loading /tmp/tmpch6l2y35.engine for TensorRT inference...
[07/16/2025-09:52:25] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
Loading /tmp/tmpch6l2y35.engine for TensorRT inference...
[07/16/2025-09:52:25] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
[07/16/2025-09:52:25] [TRT] [I] Loaded engine size: 45 MiB
[07/16/2025-09:52:25] [TRT] [I] Loaded engine size: 45 MiB
[07/16/2025-09:52:25] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +2, GPU +149, now: CPU 8, GPU 642 (MiB)
[07/16/2025-09:52:25] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +2, GPU +163, now: CPU 8, GPU 696 (MiB)
[07/16/2025-09:52:25] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +2, GPU +203, now: CPU 8, GPU 696 (MiB)
[07/16/2025-09:52:25] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +0, GPU +55, now: CPU 9, GPU 751 (MiB)
Task 1945300432451997698 - Attempting to restart FFmpeg (attempt 1)
Task 1945300432451997698 - FFmpeg restarted successfully
分辨率从 1440x1080 变为 720x540
分辨率从 720x540 变为 1440x1080
Error in frame writer: write to closed file
Warning: Thread for task 1945300432451997698 did not stop gracefully
INFO:     **************:44702 - "POST /stop HTTP/1.1" 200 OK
==================================================
接收到启动任务请求:
  任务ID: 1945300788179308545
  设备SN: 1581F6Q8D24AA00GRS99
  应用代码: car
  输入流: rtmp://*************:19403/live/1581F6Q8D24AA00GRS99_81-0-0?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTI2MzQ0MjQsInVzZXJuYW1lIjoiZWNhZGZmOTYtYTcxMS00YzMzLTk5MmYtNmNlNTViZDhiMWZkIn0.8l9kyuhqQpNR-DEucL8js9Mu7HvHKGUzMZuKplGt9WY
  输出流: rtmp://*************:19403/live/1945300788179308545
  回调URL: http://***************:3100/hisun-boot/dock/zhimou/ai-hook2
  MQTT配置: 未提供，将使用默认配置
==================================================
解析的模型列表: ['car']
Initialized detector with: {'model_name': '车辆识别', 'model_filename': 'car_type_v11.engine', 'input_size': [640, 640], 'classes': ['自行车', '车', '面包车', '卡车', '三轮车', '带蓬三轮车', '公交车', '摩托车'], 'predict_method': 'ObjectDetectorEngine', 'predict_params': {'conf': 0.3}}
INFO:     **************:44830 - "POST /start HTTP/1.1" 200 OK
['cuda', 'drm']
Connected to MQTT Broker!
Video stream format: <av.VideoFormat cuda, 1440x1080>
Video stream color range: 1
分辨率从 0x0 变为 1440x1080
Loading /tmp/tmp8pnogdpm.engine for TensorRT inference...
[07/16/2025-09:53:50] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
Loading /tmp/tmp8pnogdpm.engine for TensorRT inference...
[07/16/2025-09:53:50] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
[07/16/2025-09:53:50] [TRT] [I] Loaded engine size: 45 MiB
[07/16/2025-09:53:50] [TRT] [I] Loaded engine size: 45 MiB
Loading /tmp/tmp8pnogdpm.engine for TensorRT inference...
[07/16/2025-09:53:50] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
Loading /tmp/tmp8pnogdpm.engine for TensorRT inference...
[07/16/2025-09:53:50] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
[07/16/2025-09:53:50] [TRT] [I] Loaded engine size: 45 MiB
[07/16/2025-09:53:50] [TRT] [I] Loaded engine size: 45 MiB
[07/16/2025-09:53:50] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +2, GPU +188, now: CPU 12, GPU 1017 (MiB)
[07/16/2025-09:53:50] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +2, GPU +188, now: CPU 12, GPU 1017 (MiB)
[07/16/2025-09:53:50] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +1, GPU +109, now: CPU 14, GPU 1126 (MiB)
[07/16/2025-09:53:50] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +2, GPU +109, now: CPU 14, GPU 1126 (MiB)
Task 1945300788179308545 - Attempting to restart FFmpeg (attempt 1)
Task 1945300788179308545 - FFmpeg restarted successfully
Warning: Thread for task 1945300788179308545 did not stop gracefully
INFO:     **************:45000 - "POST /stop HTTP/1.1" 200 OK
INFO:     **************:44804 - "GET http%3A//azenv.net/ HTTP/1.1" 404 Not Found
==================================================
接收到启动任务请求:
  任务ID: 1945301695629561858
  设备SN: 1581F6Q8D24AA00GRS99
  应用代码: yellow_grid
  输入流: rtmp://*************:19403/live/1581F6Q8D24AA00GRS99_81-0-0?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTI2MzQ2NDAsInVzZXJuYW1lIjoiZWNhZGZmOTYtYTcxMS00YzMzLTk5MmYtNmNlNTViZDhiMWZkIn0.gS-f2OrYBJ5AqjMu4GFh3wRchoaiCdfdWT7PPJVaj74
  输出流: rtmp://*************:19403/live/1945301695629561858
  回调URL: http://***************:3100/hisun-boot/dock/zhimou/ai-hook2
  MQTT配置: 未提供，将使用默认配置
==================================================
解析的模型列表: ['yellow_grid']
Initialized detector with: {'model_name': '黄色网格禁停区违停识别', 'model_filename': 'yellow_grid_v6.engine', 'car_model': 'car_type_v11.engine', 'input_size': [640, 640], 'classes': ['违停车辆'], 'predict_method': 'CarViolationEngine', 'predict_params': {'conf': 0.4}}
INFO:     **************:45474 - "POST /start HTTP/1.1" 200 OK
['cuda', 'drm']
Connected to MQTT Broker!
Video stream format: <av.VideoFormat cuda, 1440x1080>
Video stream color range: 1
分辨率从 0x0 变为 1440x1080
Loading /tmp/tmpfmaetj57.engine for TensorRT inference...
[07/16/2025-09:57:27] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
[07/16/2025-09:57:27] [TRT] [I] Loaded engine size: 139 MiB
[07/16/2025-09:57:27] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +1, GPU +301, now: CPU 16, GPU 1559 (MiB)
Loading /tmp/tmpx7b4ed1a.engine for TensorRT inference...
[07/16/2025-09:57:27] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
[07/16/2025-09:57:27] [TRT] [I] Loaded engine size: 45 MiB
[07/16/2025-09:57:27] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +1, GPU +55, now: CPU 17, GPU 1653 (MiB)
Task 1945301695629561858 - Attempting to restart FFmpeg (attempt 1)
Task 1945301695629561858 - FFmpeg restarted successfully
Error in frame writer: write to closed file
Warning: Thread for task 1945301695629561858 did not stop gracefully
INFO:     **************:45884 - "POST /stop HTTP/1.1" 200 OK
INFO:     **************:42238 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:56220 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:56232 - "PRI %2A HTTP/2.0" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     **************:46256 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     **************:46270 - "PRI %2A HTTP/2.0" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     **************:46290 - "GET /login HTTP/1.1" 404 Not Found
INFO:     ***********:56194 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:55830 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:48546 - "GET / HTTP/1.1" 404 Not Found
INFO:     ***********:56986 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:46382 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:57462 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:44554 - "GET / HTTP/1.1" 404 Not Found
INFO:     **************:61816 - "GET / HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
==================================================
接收到启动任务请求:
  任务ID: 1945316585282932737
  设备SN: 1581F8HGX254W00A0CGK
  应用代码: yellow_grid
  输入流: rtmp://*************:19403/live/1581F8HGX254W00A0CGK_99-0-0?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTI2MzgxOTAsInVzZXJuYW1lIjoiZWNhZGZmOTYtYTcxMS00YzMzLTk5MmYtNmNlNTViZDhiMWZkIn0.Ik-9sY3si_wOOcIAVjDXfuM0Csp3AKQ-WVaqxpowDiU
  输出流: rtmp://*************:19403/live/1945316585282932737
  回调URL: http://***************:3100/hisun-boot/dock/zhimou/ai-hook2
  MQTT配置: 未提供，将使用默认配置
==================================================
解析的模型列表: ['yellow_grid']
Initialized detector with: {'model_name': '黄色网格禁停区违停识别', 'model_filename': 'yellow_grid_v6.engine', 'car_model': 'car_type_v11.engine', 'input_size': [640, 640], 'classes': ['违停车辆'], 'predict_method': 'CarViolationEngine', 'predict_params': {'conf': 0.4}}
INFO:     **************:51760 - "POST /start HTTP/1.1" 200 OK
['cuda', 'drm']
Connected to MQTT Broker!
Video stream format: <av.VideoFormat cuda, 960x720>
Video stream color range: 1
分辨率从 0x0 变为 960x720
Loading /tmp/tmpb7s2vfcy.engine for TensorRT inference...
[07/16/2025-10:56:37] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
[07/16/2025-10:56:37] [TRT] [I] Loaded engine size: 139 MiB
[07/16/2025-10:56:37] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +1, GPU +300, now: CPU 18, GPU 2085 (MiB)
Loading /tmp/tmpfcdb9_dm.engine for TensorRT inference...
[07/16/2025-10:56:37] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
[07/16/2025-10:56:37] [TRT] [I] Loaded engine size: 45 MiB
[07/16/2025-10:56:37] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +0, GPU +54, now: CPU 19, GPU 2179 (MiB)
2025-07-16 10:56:48,504 - INFO - 0
2025-07-16 10:56:53,659 - INFO - 1752634608.5047045
2025-07-16 10:56:58,672 - INFO - 1752634613.659463
Task 1945316585282932737 - Attempting to restart FFmpeg (attempt 1)
Task 1945316585282932737 - FFmpeg restarted successfully
================================
================================
================================
Error in frame writer: write to closed file
Warning: Thread for task 1945316585282932737 did not stop gracefully
INFO:     **************:51834 - "POST /stop HTTP/1.1" 200 OK
==================================================
接收到启动任务请求:
  任务ID: 1945316995175485442
  设备SN: 1581F8HGX254W00A0CGK
  应用代码: yellow_grid
  输入流: rtmp://*************:19403/live/1581F8HGX254W00A0CGK_99-0-0?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTI2MzgyODgsInVzZXJuYW1lIjoiZWNhZGZmOTYtYTcxMS00YzMzLTk5MmYtNmNlNTViZDhiMWZkIn0.1iSGs4ka9krfkuAtghkevWue_co3OCZcn7uQBf4rfRc
  输出流: rtmp://*************:19403/live/1945316995175485442
  回调URL: http://***************:3100/hisun-boot/dock/zhimou/ai-hook2
  MQTT配置: 未提供，将使用默认配置
==================================================
解析的模型列表: ['yellow_grid']
Initialized detector with: {'model_name': '黄色网格禁停区违停识别', 'model_filename': 'yellow_grid_v6.engine', 'car_model': 'car_type_v11.engine', 'input_size': [640, 640], 'classes': ['违停车辆'], 'predict_method': 'CarViolationEngine', 'predict_params': {'conf': 0.4}}
INFO:     **************:51860 - "POST /start HTTP/1.1" 200 OK
['cuda', 'drm']
Connected to MQTT Broker!
Video stream format: <av.VideoFormat cuda, 960x720>
Video stream color range: 1
分辨率从 0x0 变为 960x720
Loading /tmp/tmp28r82b_p.engine for TensorRT inference...
[07/16/2025-10:58:14] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
[07/16/2025-10:58:14] [TRT] [I] Loaded engine size: 139 MiB
[07/16/2025-10:58:14] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +1, GPU +301, now: CPU 21, GPU 2612 (MiB)
Loading /tmp/tmp2yjvg36l.engine for TensorRT inference...
[07/16/2025-10:58:14] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
[07/16/2025-10:58:14] [TRT] [I] Loaded engine size: 45 MiB
[07/16/2025-10:58:14] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +1, GPU +55, now: CPU 22, GPU 2706 (MiB)
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [1441749]
2025-07-16 14:42:44,514 - INFO - Loading weights from lib/deep_sort/deep_sort/deep/checkpoint/ckpt.t7... Done!
✅ 车牌检测模型加载成功
✅ 车牌白名单加载成功，共 3 个车牌
✅ 车牌识别器初始化完成
Initialized detector with: {'model_name': '人员识别', 'model_filename': 'people_v8.engine', 'input_size': [640, 640], 'classes': ['人'], 'predict_method': 'ObjectDetectorEngine', 'predict_params': {'conf': 0.3}}
Initialized detector with: {'model_name': '车辆测速', 'model_filename': 'car_type_v11.engine', 'input_size': [640, 640], 'classes': ['自行车', '车', '面包车', '卡车', '三轮车', '带蓬三轮车', '公交车', '摩托车'], 'predict_method': 'CarVelocityEngine', 'predict_params': {'conf': 0.3}}
Initialized detector with: {'model_name': '黄色网格禁停区违停识别', 'model_filename': 'yellow_grid_v6.engine', 'car_model': 'car_type_v11.engine', 'input_size': [640, 640], 'classes': ['违停车辆'], 'predict_method': 'CarViolationEngine', 'predict_params': {'conf': 0.4}}
Initialized detector with: {'model_name': '火焰烟雾识别', 'model_filename': 'fire_smoke_v3.engine', 'input_size': [640, 640], 'classes': ['火', '烟'], 'predict_method': 'ObjectDetectorEngine', 'predict_params': {'conf': 0.3}}
Initialized detector with: {'model_name': '骑车安全帽检测', 'model_filename': 'motor_helmet_v1.engine', 'input_size': [1280, 1280], 'classes': ['未佩戴头盔', '佩戴头盔', '人', '自行车'], 'alarms_classes': ['未佩戴头盔'], 'predict_method': 'MotorHelmetEngine', 'predict_params': {'conf': 0.3}}
Initialized detector with: {'model_name': '人员聚集识别', 'model_filename': 'people_v8_crowd.engine', 'input_size': [1280, 1280], 'classes': ['people'], 'predict_method': 'PersonCrowdEngine_2', 'predict_params': {'conf': 0.3}}
✅ 横幅检测模型加载成功
✅ 横幅识别器初始化完成
Initialized detector with: {'model_name': '车辆识别', 'model_filename': 'car_type_v11.engine', 'input_size': [640, 640], 'classes': ['自行车', '车', '面包车', '卡车', '三轮车', '带蓬三轮车', '公交车', '摩托车'], 'predict_method': 'ObjectDetectorEngine', 'predict_params': {'conf': 0.3}}
INFO:     Started server process [1754700]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [1754700]
2025-07-16 14:44:20,527 - INFO - Loading weights from lib/deep_sort/deep_sort/deep/checkpoint/ckpt.t7... Done!
✅ 车牌检测模型加载成功
✅ 车牌识别器初始化完成
Initialized detector with: {'model_name': '人员识别', 'model_filename': 'people_v8.engine', 'input_size': [640, 640], 'classes': ['人'], 'predict_method': 'ObjectDetectorEngine', 'predict_params': {'conf': 0.3}}
Initialized detector with: {'model_name': '车辆测速', 'model_filename': 'car_type_v11.engine', 'input_size': [640, 640], 'classes': ['自行车', '车', '面包车', '卡车', '三轮车', '带蓬三轮车', '公交车', '摩托车'], 'predict_method': 'CarVelocityEngine', 'predict_params': {'conf': 0.3}}
Initialized detector with: {'model_name': '黄色网格禁停区违停识别', 'model_filename': 'yellow_grid_v6.engine', 'car_model': 'car_type_v11.engine', 'input_size': [640, 640], 'classes': ['违停车辆'], 'predict_method': 'CarViolationEngine', 'predict_params': {'conf': 0.4}}
Initialized detector with: {'model_name': '火焰烟雾识别', 'model_filename': 'fire_smoke_v3.engine', 'input_size': [640, 640], 'classes': ['火', '烟'], 'predict_method': 'ObjectDetectorEngine', 'predict_params': {'conf': 0.3}}
Initialized detector with: {'model_name': '骑车安全帽检测', 'model_filename': 'motor_helmet_v1.engine', 'input_size': [1280, 1280], 'classes': ['未佩戴头盔', '佩戴头盔', '人', '自行车'], 'alarms_classes': ['未佩戴头盔'], 'predict_method': 'MotorHelmetEngine', 'predict_params': {'conf': 0.3}}
Initialized detector with: {'model_name': '人员聚集识别', 'model_filename': 'people_v8_crowd.engine', 'input_size': [1280, 1280], 'classes': ['people'], 'predict_method': 'PersonCrowdEngine_2', 'predict_params': {'conf': 0.3}}
✅ 横幅检测模型加载成功
✅ 横幅识别器初始化完成
Initialized detector with: {'model_name': '车辆识别', 'model_filename': 'car_type_v11.engine', 'input_size': [640, 640], 'classes': ['自行车', '车', '面包车', '卡车', '三轮车', '带蓬三轮车', '公交车', '摩托车'], 'predict_method': 'ObjectDetectorEngine', 'predict_params': {'conf': 0.3}}
INFO:     Started server process [1755966]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
==================================================
接收到启动任务请求:
  任务ID: 1945377119443144706
  设备SN: 1581F8HGX252500A00CG
  应用代码: car_plate
  输入流: rtmp://*************:19403/live/1581F8HGX252500A00CG_99-0-0?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTI2NTI2MjMsInVzZXJuYW1lIjoiZWNhZGZmOTYtYTcxMS00YzMzLTk5MmYtNmNlNTViZDhiMWZkIn0.dz1rvxr7gLPu0owpz7RnPkzPXmA3s088uny8bVAmfZA
  输出流: rtmp://*************:19403/live/1945377119443144706
  回调URL: http://***************:3200/hisun-boot/dock/zhimou/ai-hook2
  MQTT配置: 未提供，将使用默认配置
==================================================
解析的模型列表: ['car_plate']
✅ 车牌检测模型加载成功
✅ 车牌识别器初始化完成
INFO:     **************:53528 - "POST /start HTTP/1.1" 200 OK
['cuda', 'drm']
Connected to MQTT Broker!
Video stream format: <av.VideoFormat cuda, 960x720>
Video stream color range: 1
分辨率从 0x0 变为 960x720
Loading /tmp/tmp6xgkvd6f.engine for TensorRT inference...
Loading /tmp/tmp6xgkvd6f.engine for TensorRT inference...
[07/16/2025-14:57:09] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
Loading /tmp/tmp6xgkvd6f.engine for TensorRT inference...
[07/16/2025-14:57:09] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
Loading /tmp/tmp6xgkvd6f.engine for TensorRT inference...
[07/16/2025-14:57:09] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
[07/16/2025-14:57:09] [TRT] [I] Loaded engine size: 94 MiB
[07/16/2025-14:57:09] [TRT] [I] Loaded engine size: 94 MiB
[07/16/2025-14:57:09] [TRT] [I] Loaded engine size: 94 MiB
[07/16/2025-14:57:09] [TRT] [I] Loaded engine size: 94 MiB
[07/16/2025-14:57:09] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +2, GPU +287, now: CPU 2, GPU 641 (MiB)
[07/16/2025-14:57:09] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +1, GPU +191, now: CPU 3, GPU 736 (MiB)
[07/16/2025-14:57:09] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +1, GPU +95, now: CPU 3, GPU 736 (MiB)
[07/16/2025-14:57:09] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +3, GPU +382, now: CPU 3, GPU 736 (MiB)
Task 1945377119443144706 - Attempting to restart FFmpeg (attempt 1)
Task 1945377119443144706 - FFmpeg restarted successfully
识别到车牌号码: 赣EKN001
Warning: Thread for task 1945377119443144706 did not stop gracefully
INFO:     **************:54026 - "POST /stop HTTP/1.1" 200 OK
INFO:     **************:54030 - "POST /stop HTTP/1.1" 200 OK
INFO:     **************:52184 - "GET /docs HTTP/1.1" 200 OK
INFO:     **************:52195 - "GET /openapi.json HTTP/1.1" 200 OK
==================================================
接收到启动任务请求:
  任务ID: 1945377811733991425
  设备SN: 1581F8HGX252500A00CG
  应用代码: motor_helmet,car_plate
  输入流: rtmp://*************:19403/live/1581F8HGX252500A00CG_99-0-0?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTI2NTI3ODgsInVzZXJuYW1lIjoiZWNhZGZmOTYtYTcxMS00YzMzLTk5MmYtNmNlNTViZDhiMWZkIn0.OsvqEC0ow4MzURvm1LX99FuxLaO65ICJEPUH00pdmXE
  输出流: rtmp://*************:19403/live/1945377811733991425
  回调URL: http://***************:3200/hisun-boot/dock/zhimou/ai-hook2
  MQTT配置: 未提供，将使用默认配置
==================================================
解析的模型列表: ['motor_helmet', 'car_plate']
✅ 车牌检测模型加载成功
✅ 车牌识别器初始化完成
Initialized detector with: {'model_name': '骑车安全帽检测', 'model_filename': 'motor_helmet_v1.engine', 'input_size': [1280, 1280], 'classes': ['未佩戴头盔', '佩戴头盔', '人', '自行车'], 'alarms_classes': ['未佩戴头盔'], 'predict_method': 'MotorHelmetEngine', 'predict_params': {'conf': 0.3}}
INFO:     **************:54048 - "POST /start HTTP/1.1" 200 OK
Connected to MQTT Broker!
['cuda', 'drm']
Video stream format: <av.VideoFormat cuda, 960x720>
Video stream color range: 1
分辨率从 0x0 变为 960x720
Loading /tmp/tmpbhzaxyib.engine for TensorRT inference...
[07/16/2025-14:59:55] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
Loading /tmp/tmpbhzaxyib.engine for TensorRT inference...
[07/16/2025-14:59:55] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
Loading /tmp/tmpbhzaxyib.engine for TensorRT inference...
[07/16/2025-14:59:55] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
Loading /tmp/tmpbhzaxyib.engine for TensorRT inference...
[07/16/2025-14:59:55] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
[07/16/2025-14:59:55] [TRT] [I] Loaded engine size: 94 MiB
[07/16/2025-14:59:55] [TRT] [I] Loaded engine size: 94 MiB[07/
16/2025-14:59:55] [TRT] [I] Loaded engine size: 94 MiB
[07/16/2025-14:59:55] [TRT] [I] Loaded engine size: 94 MiB
[07/16/2025-14:59:55] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +1, GPU +96, now: CPU 5, GPU 1187 (MiB)
[07/16/2025-14:59:55] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +3, GPU +287, now: CPU 7, GPU 1473 (MiB)
[07/16/2025-14:59:55] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +2, GPU +286, now: CPU 7, GPU 1473 (MiB)
[07/16/2025-14:59:55] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +2, GPU +286, now: CPU 7, GPU 1473 (MiB)
Loading /tmp/tmpae99lzh4.engine for TensorRT inference...
[07/16/2025-14:59:55] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
Loading /tmp/tmpae99lzh4.engine for TensorRT inference...
[07/16/2025-14:59:56] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
Loading /tmp/tmpae99lzh4.engine for TensorRT inference...
[07/16/2025-14:59:56] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
Loading /tmp/tmpae99lzh4.engine for TensorRT inference...
[07/16/2025-14:59:56] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
[07/16/2025-14:59:56] [TRT] [I] Loaded engine size: 93 MiB
[07/16/2025-14:59:56] [TRT] [I] Loaded engine size: 93 MiB
[07/16/2025-14:59:56] [TRT] [I] Loaded engine size: 93 MiB
[07/16/2025-14:59:56] [TRT] [I] Loaded engine size: 93 MiB
[07/16/2025-14:59:56] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +1, GPU +464, now: CPU 8, GPU 2113 (MiB)
[07/16/2025-14:59:56] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +2, GPU +576, now: CPU 10, GPU 2689 (MiB)
[07/16/2025-14:59:56] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +2, GPU +863, now: CPU 10, GPU 2976 (MiB)
[07/16/2025-14:59:56] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +0, GPU +287, now: CPU 10, GPU 2976 (MiB)
WARNING:  Invalid HTTP request received.
Task 1945377811733991425 - Attempting to restart FFmpeg (attempt 1)
Task 1945377811733991425 - FFmpeg restarted successfully
识别到车牌号码: 粤EDF1847
识别到车牌号码: 赣JF6K96
识别到车牌号码: 鄂FF95788
识别到车牌号码: 赣E95L52
识别到车牌号码: 川E3436F
识别到车牌号码: E366F
识别到车牌号码: 赣E861Z6
识别到车牌号码: 赣ED05584
识别到车牌号码: 桂ED05584
识别到车牌号码: 湘AF81588
识别到车牌号码: 川AF81588
识别到车牌号码: 赣E2G012
识别到车牌号码: 赣EM8F05
识别到车牌号码: 赣EE0U57
识别到车牌号码: 辽EC2N67
Warning: Thread for task 1945377811733991425 did not stop gracefully
INFO:     **************:54280 - "POST /stop HTTP/1.1" 200 OK
==================================================
接收到启动任务请求:
  任务ID: 1945378601097809921
  设备SN: 1581F8HGX252500A00CG
  应用代码: car_plate
  输入流: rtmp://*************:19403/live/1581F8HGX252500A00CG_99-0-0?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTI2NTI5NzYsInVzZXJuYW1lIjoiZWNhZGZmOTYtYTcxMS00YzMzLTk5MmYtNmNlNTViZDhiMWZkIn0.nUAjDR8u1eo4g--3_BZAQmSihBfdBK2r58MvT8mHc5Q
  输出流: rtmp://*************:19403/live/1945378601097809921
  回调URL: http://***************:3200/hisun-boot/dock/zhimou/ai-hook2
  MQTT配置: 未提供，将使用默认配置
==================================================
解析的模型列表: ['car_plate']
✅ 车牌检测模型加载成功
✅ 车牌识别器初始化完成
INFO:     **************:54304 - "POST /start HTTP/1.1" 200 OK
['cuda', 'drm']
Connected to MQTT Broker!
Video stream format: <av.VideoFormat cuda, 960x720>
Video stream color range: 1
分辨率从 0x0 变为 960x720
Loading /tmp/tmpz4ho4p1i.engine for TensorRT inference...
[07/16/2025-15:03:00] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
Loading /tmp/tmpz4ho4p1i.engine for TensorRT inference...
[07/16/2025-15:03:00] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
Loading /tmp/tmpz4ho4p1i.engine for TensorRT inference...
[07/16/2025-15:03:00] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
Loading /tmp/tmpz4ho4p1i.engine for TensorRT inference...
[07/16/2025-15:03:00] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
[07/16/2025-15:03:00] [TRT] [I] Loaded engine size: 94 MiB
[07/16/2025-15:03:00] [TRT] [I] Loaded engine size: 94 MiB
[07/16/2025-15:03:00] [TRT] [I] Loaded engine size: 94 MiB
[07/16/2025-15:03:00] [TRT] [I] Loaded engine size: 94 MiB
[07/16/2025-15:03:00] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +1, GPU +185, now: CPU 12, GPU 3427 (MiB)
[07/16/2025-15:03:00] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +2, GPU +286, now: CPU 14, GPU 3713 (MiB)
[07/16/2025-15:03:00] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +2, GPU +286, now: CPU 14, GPU 3713 (MiB)
[07/16/2025-15:03:00] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +2, GPU +286, now: CPU 14, GPU 3713 (MiB)
开始匹配车牌:
  检测车牌: [PlateInfo(id='det_001', plate_number='赣AZ36E1'), PlateInfo(id='det_002', plate_number='川ADM8396'), PlateInfo(id='det_003', plate_number='京A12345')]
  白名单车牌: ['粤BD2589', '沪AF3695', '津EK7852', '渝GZ1234', '冀JX4567', '晋LM7890', '辽NB8520', '吉PC9631', '黑RD1478', '苏SE2580', '浙TF3691', '川ADM8336', '闽VH5823', '赣WI6934', '鲁XJ7145', '豫YK8256', '鄂ZL9367', '湘AM1478', '粤BN2589', '赣AZ36E2']
INFO:     **************:53066 - "POST /test_match HTTP/1.1" 200 OK
Task 1945378601097809921 - Attempting to restart FFmpeg (attempt 1)
Task 1945378601097809921 - FFmpeg restarted successfully
识别到车牌号码: 赣E910J3
识别到车牌号码: 赣ED88484
识别到车牌号码: 闽ED88494
识别到车牌号码: 川ED38950
识别到车牌号码: 赣EWB212
Error in frame writer: write to closed file
Warning: Thread for task 1945378601097809921 did not stop gracefully
INFO:     **************:54348 - "POST /stop HTTP/1.1" 200 OK
开始匹配车牌:
  检测车牌: [PlateInfo(id='det_001', plate_number='赣AZ36E1'), PlateInfo(id='det_002', plate_number='川ADM8396'), PlateInfo(id='det_003', plate_number='京A12345')]
  白名单车牌: ['粤BD2589', '沪AF3695', '津EK7852', '渝GZ1234', '冀JX4567', '晋LM7890', '辽NB8520', '吉PC9631', '黑RD1478', '苏SE2580', '浙TF3691', '川ADM8336', '闽VH5823', '赣WI6934', '鲁XJ7145', '豫YK8256', '鄂ZL9367', '湘AM1478', '粤BN2589', '赣AZ36E2']
INFO:     **************:53082 - "POST /test_match HTTP/1.1" 200 OK
==================================================
接收到启动任务请求:
  任务ID: 1945379073506463745
  设备SN: 1581F8HGX252500A00CG
  应用代码: motor_helmet
  输入流: rtmp://*************:19403/live/1581F8HGX252500A00CG_99-0-0?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTI2NTMwODksInVzZXJuYW1lIjoiZWNhZGZmOTYtYTcxMS00YzMzLTk5MmYtNmNlNTViZDhiMWZkIn0.5fQT6r7kZnOb_6WvUr_TrkdFley5gJXF3_BxPIaXAFY
  输出流: rtmp://*************:19403/live/1945379073506463745
  回调URL: http://***************:3200/hisun-boot/dock/zhimou/ai-hook2
  MQTT配置: 未提供，将使用默认配置
==================================================
解析的模型列表: ['motor_helmet']
Initialized detector with: {'model_name': '骑车安全帽检测', 'model_filename': 'motor_helmet_v1.engine', 'input_size': [1280, 1280], 'classes': ['未佩戴头盔', '佩戴头盔', '人', '自行车'], 'alarms_classes': ['未佩戴头盔'], 'predict_method': 'MotorHelmetEngine', 'predict_params': {'conf': 0.3}}
INFO:     **************:54378 - "POST /start HTTP/1.1" 200 OK
['cuda', 'drm']
Connected to MQTT Broker!
Video stream format: <av.VideoFormat cuda, 960x720>
Video stream color range: 1
分辨率从 0x0 变为 960x720
Loading /tmp/tmphwidfoee.engine for TensorRT inference...
[07/16/2025-15:04:55] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
Loading /tmp/tmphwidfoee.engine for TensorRT inference...
[07/16/2025-15:04:55] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
Loading /tmp/tmphwidfoee.engine for TensorRT inference...
[07/16/2025-15:04:55] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
Loading /tmp/tmphwidfoee.engine for TensorRT inference...
[07/16/2025-15:04:55] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
[07/16/2025-15:04:55] [TRT] [I] Loaded engine size: 93 MiB
[07/16/2025-15:04:55] [TRT] [I] Loaded engine size: 93 MiB
[07/16/2025-15:04:55] [TRT] [I] Loaded engine size: 93 MiB
[07/16/2025-15:04:56] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +0, GPU +376, now: CPU 15, GPU 4265 (MiB)
[07/16/2025-15:04:56] [TRT] [I] Loaded engine size: 93 MiB
[07/16/2025-15:04:56] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +2, GPU +664, now: CPU 17, GPU 4929 (MiB)
[07/16/2025-15:04:56] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +3, GPU +951, now: CPU 18, GPU 5216 (MiB)
[07/16/2025-15:04:56] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +1, GPU +287, now: CPU 18, GPU 5216 (MiB)
Task 1945379073506463745 - Attempting to restart FFmpeg (attempt 1)
Task 1945379073506463745 - FFmpeg restarted successfully
Error in frame writer: write to closed file
Warning: Thread for task 1945379073506463745 did not stop gracefully
INFO:     **************:54578 - "POST /stop HTTP/1.1" 200 OK
INFO:     **************:54582 - "POST /stop HTTP/1.1" 200 OK
INFO:     **************:54586 - "POST /stop HTTP/1.1" 200 OK
==================================================
接收到启动任务请求:
  任务ID: 1945379349198065665
  设备SN: 1581F8HGX252500A00CG
  应用代码: motor_helmet,car_plate
  输入流: rtmp://*************:19403/live/1581F8HGX252500A00CG_99-0-0?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTI2NTMxNTQsInVzZXJuYW1lIjoiZWNhZGZmOTYtYTcxMS00YzMzLTk5MmYtNmNlNTViZDhiMWZkIn0.KgG98xmZiRJ999Y6AhyC8c0KbFIoUmMTj2k8K2Bb5lY
  输出流: rtmp://*************:19403/live/1945379349198065665
  回调URL: http://***************:3200/hisun-boot/dock/zhimou/ai-hook2
  MQTT配置: 未提供，将使用默认配置
==================================================
解析的模型列表: ['motor_helmet', 'car_plate']
✅ 车牌检测模型加载成功
✅ 车牌识别器初始化完成
Initialized detector with: {'model_name': '骑车安全帽检测', 'model_filename': 'motor_helmet_v1.engine', 'input_size': [1280, 1280], 'classes': ['未佩戴头盔', '佩戴头盔', '人', '自行车'], 'alarms_classes': ['未佩戴头盔'], 'predict_method': 'MotorHelmetEngine', 'predict_params': {'conf': 0.3}}
INFO:     **************:54600 - "POST /start HTTP/1.1" 200 OK
['cuda', 'drm']
Connected to MQTT Broker!
Video stream format: <av.VideoFormat cuda, 960x720>
Video stream color range: 1
分辨率从 0x0 变为 960x720
Loading /tmp/tmpglm8ykyw.engine for TensorRT inference...
[07/16/2025-15:05:58] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
Loading /tmp/tmpglm8ykyw.engine for TensorRT inference...
[07/16/2025-15:05:58] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
Loading /tmp/tmpglm8ykyw.engine for TensorRT inference...
[07/16/2025-15:05:58] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
Loading /tmp/tmpglm8ykyw.engine for TensorRT inference...
[07/16/2025-15:05:58] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
[07/16/2025-15:05:59] [TRT] [I] Loaded engine size: 94 MiB
[07/16/2025-15:05:59] [TRT] [I] Loaded engine size: 94 MiB
[07/16/2025-15:05:59] [TRT] [I] Loaded engine size: 94 MiB
[07/16/2025-15:05:59] [TRT] [I] Loaded engine size: 94 MiB
[07/16/2025-15:05:59] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +0, GPU +361, now: CPU 18, GPU 5666 (MiB)
Loading /tmp/tmpg4il3shb.engine for TensorRT inference...
[07/16/2025-15:05:59] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
[07/16/2025-15:05:59] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +2, GPU +286, now: CPU 21, GPU 5953 (MiB)
[07/16/2025-15:05:59] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +2, GPU +286, now: CPU 21, GPU 5953 (MiB)
[07/16/2025-15:05:59] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +2, GPU +286, now: CPU 21, GPU 5953 (MiB)
Loading /tmp/tmpg4il3shb.engine for TensorRT inference...
Loading /tmp/tmpg4il3shb.engine for TensorRT inference...
[07/16/2025-15:05:59] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
[07/16/2025-15:05:59] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
Loading /tmp/tmpg4il3shb.engine for TensorRT inference...
[07/16/2025-15:05:59] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
[07/16/2025-15:05:59] [TRT] [I] Loaded engine size: 93 MiB
[07/16/2025-15:05:59] [TRT] [I] Loaded engine size: 93 MiB
[07/16/2025-15:06:00] [TRT] [I] Loaded engine size: 93 MiB
[07/16/2025-15:06:00] [TRT] [I] Loaded engine size: 93 MiB
[07/16/2025-15:06:00] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +1, GPU +751, now: CPU 23, GPU 6881 (MiB)
[07/16/2025-15:06:00] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +1, GPU +575, now: CPU 23, GPU 6881 (MiB)
[07/16/2025-15:06:00] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +2, GPU +576, now: CPU 25, GPU 7457 (MiB)
[07/16/2025-15:06:00] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +1, GPU +288, now: CPU 25, GPU 7457 (MiB)
Task 1945379349198065665 - Attempting to restart FFmpeg (attempt 1)
Task 1945379349198065665 - FFmpeg restarted successfully
识别到车牌号码: 赣E335Q3
Warning: Thread for task 1945379349198065665 did not stop gracefully
INFO:     **************:54668 - "POST /stop HTTP/1.1" 200 OK
WARNING:  Invalid HTTP request received.
开始匹配车牌:
  检测车牌: [PlateInfo(id='det_001', plate_number='赣AZ36E1'), PlateInfo(id='det_002', plate_number='川ADM8396'), PlateInfo(id='det_003', plate_number='京A12345')]
  白名单车牌: ['粤BD2589', '沪AF3695', '津EK7852', '渝GZ1234', '冀JX4567', '晋LM7890', '辽NB8520', '吉PC9631', '黑RD1478', '苏SE2580', '浙TF3691', '川ADM8336', '闽VH5823', '赣WI6934', '鲁XJ7145', '豫YK8256', '鄂ZL9367', '湘AM1478', '粤BN2589', '赣AZ36E2']
INFO:     **************:54482 - "POST /test_match HTTP/1.1" 200 OK
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
开始匹配车牌:
  检测车牌: [PlateInfo(id='det_001', plate_number='赣AZ36E1'), PlateInfo(id='det_002', plate_number='川ADM8396'), PlateInfo(id='det_003', plate_number='京A12345')]
  白名单车牌: ['粤BD2589', '沪AF3695', '津EK7852', '渝GZ1234', '冀JX4567', '晋LM7890', '辽NB8520', '吉PC9631', '黑RD1478', '苏SE2580', '浙TF3691', '川ADM8336', '闽VH5823', '赣WI6934', '鲁XJ7145', '豫YK8256', '鄂ZL9367', '湘AM1478', '粤BN2589', '赣AZ36E2']
INFO:     **************:55245 - "POST /test_match HTTP/1.1" 200 OK
开始匹配车牌:
  检测车牌: [PlateInfo(id='det_001', plate_number='赣AZ36E1'), PlateInfo(id='det_002', plate_number='川ADM8396'), PlateInfo(id='det_003', plate_number='京A12345')]
  白名单车牌: ['粤BD2589', '沪AF3695', '津EK7852', '渝GZ1234', '冀JX4567', '晋LM7890', '辽NB8520', '吉PC9631', '黑RD1478', '苏SE2580', '浙TF3691', '川ADM8336', '闽VH5823', '赣WI6934', '鲁XJ7145', '豫YK8256', '鄂ZL9367', '湘AM1478', '粤BN2589', '赣AZ36E2']
INFO:     **************:55884 - "POST /test_match HTTP/1.1" 200 OK
开始匹配车牌:
  检测车牌: [PlateInfo(id='det_001', plate_number='赣AZ36E1'), PlateInfo(id='det_002', plate_number='川ADM8396'), PlateInfo(id='det_003', plate_number='京A12345')]
  白名单车牌: ['粤BD2589', '沪AF3695', '津EK7852', '渝GZ1234', '冀JX4567', '晋LM7890', '辽NB8520', '吉PC9631', '黑RD1478', '苏SE2580', '浙TF3691', '川ADM8336', '闽VH5823', '赣WI6934', '鲁XJ7145', '豫YK8256', '鄂ZL9367', '湘AM1478', '粤BN2589', '赣AZ36E2']
INFO:     **************:56052 - "POST /test_match HTTP/1.1" 200 OK
INFO:     **************:58357 - "GET /docs HTTP/1.1" 200 OK
INFO:     **************:58357 - "GET /openapi.json HTTP/1.1" 200 OK
开始匹配车牌:
  检测车牌: [PlateInfo(id='det_001', plate_number='赣AZ36E1'), PlateInfo(id='det_002', plate_number='川ADM8396'), PlateInfo(id='det_003', plate_number='京A12345')]
  白名单车牌: ['粤BD2589', '沪AF3695', '津EK7852', '渝GZ1234', '冀JX4567', '晋LM7890', '辽NB8520', '吉PC9631', '黑RD1478', '苏SE2580', '浙TF3691', '川ADM8336', '闽VH5823', '赣WI6934', '鲁XJ7145', '豫YK8256', '鄂ZL9367', '湘AM1478', '粤BN2589', '赣AZ36E2']
INFO:     **************:58356 - "POST /match_plates HTTP/1.1" 200 OK
INFO:     **************:58847 - "GET /docs HTTP/1.1" 200 OK
INFO:     **************:58847 - "GET /openapi.json HTTP/1.1" 200 OK
开始匹配车牌:
  检测车牌: [PlateInfo(id='1943966657790914561', plate_number='赣AP21C2,桂970'), PlateInfo(id='1943966661943275522', plate_number='粤粤7FT10,赣AP21C2'), PlateInfo(id='1943966667106463745', plate_number='川AP21C2'), PlateInfo(id='1943966829564440578', plate_number='川BV8D')]
  白名单车牌: ['赣A11111', '赣A11112', '赣A11113', '赣A11114', '赣A11115', '赣A11116', '赣A11117', '赣A11118', '赣A11119', '赣A11120', '赣A11121', '赣A11122', '赣A11123', '赣A11124', '赣A11125', '赣A11126', '赣A11127', '赣A11128', '赣A11129', '赣A11130']
INFO:     **************:58436 - "POST /match_plates HTTP/1.1" 200 OK
开始匹配车牌:
  检测车牌: [PlateInfo(id='1943966657790914561', plate_number='赣AP21C2,桂970'), PlateInfo(id='1943966661943275522', plate_number='粤粤7FT10,赣AP21C2'), PlateInfo(id='1943966667106463745', plate_number='川AP21C2'), PlateInfo(id='1943966829564440578', plate_number='川BV8D')]
  白名单车牌: ['川AP21C2', '川BV8D', '赣A11111', '赣A11112', '赣A11113', '赣A11114', '赣A11115', '赣A11116', '赣A11118', '赣A11119', '赣A11120', '赣A11121', '赣A11122', '赣A11123', '赣A11124', '赣A11126', '赣A11127', '赣A11128', '赣A11129', '赣A11130']
INFO:     **************:58496 - "POST /match_plates HTTP/1.1" 200 OK
开始匹配车牌:
  检测车牌: [PlateInfo(id='1943966657790914561', plate_number='赣AP21C2,桂970'), PlateInfo(id='1943966661943275522', plate_number='粤粤7FT10,赣AP21C2'), PlateInfo(id='1943966667106463745', plate_number='川AP21C2'), PlateInfo(id='1943966829564440578', plate_number='川BV8D')]
  白名单车牌: ['川AP21C2']
INFO:     **************:58552 - "POST /match_plates HTTP/1.1" 200 OK
开始匹配车牌:
  检测车牌: [PlateInfo(id='1943966657790914561', plate_number='赣AP21C2,桂970'), PlateInfo(id='1943966661943275522', plate_number='粤粤7FT10,赣AP21C2'), PlateInfo(id='1943966667106463745', plate_number='川AP21C2'), PlateInfo(id='1943966829564440578', plate_number='川BV8D')]
  白名单车牌: ['川AP21C2', '川BV8D', '赣A11111', '赣A11112', '赣A11113', '赣A11114', '赣A11115', '赣A11116', '赣A11118', '赣A11119', '赣A11120', '赣A11121', '赣A11122', '赣A11123', '赣A11124', '赣A11126', '赣A11127', '赣A11128', '赣A11129', '赣A11130']
INFO:     **************:58569 - "POST /match_plates HTTP/1.1" 200 OK
INFO:     **************:57349 - "GET /whitelist HTTP/1.1" 200 OK
开始匹配车牌:
  检测车牌: [PlateInfo(id='det_001', plate_number='赣AZ36E1'), PlateInfo(id='det_002', plate_number='川ADM8396'), PlateInfo(id='det_003', plate_number='京A12345')]
  白名单车牌: ['粤BD2589', '沪AF3695', '津EK7852', '渝GZ1234', '冀JX4567', '晋LM7890', '辽NB8520', '吉PC9631', '黑RD1478', '苏SE2580', '浙TF3691', '川ADM8336', '闽VH5823', '赣WI6934', '鲁XJ7145', '豫YK8256', '鄂ZL9367', '湘AM1478', '粤BN2589', '赣AZ36E2']
INFO:     **************:56721 - "POST /match_plates HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [1573414]
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [1755966]
Traceback (most recent call last):
  File "/home/<USER>/modelManger_deply/app.py", line 1, in <module>
    from fastapi import FastAPI, HTTPException, UploadFile, File
ModuleNotFoundError: No module named 'fastapi'
2025-07-16 15:53:10,751 - INFO - Loading weights from lib/deep_sort/deep_sort/deep/checkpoint/ckpt.t7... Done!
✅ 车牌检测模型加载成功
✅ 车牌识别器初始化完成
Initialized detector with: {'model_name': '人员识别', 'model_filename': 'people_v8.engine', 'input_size': [640, 640], 'classes': ['人'], 'predict_method': 'ObjectDetectorEngine', 'predict_params': {'conf': 0.3}}
Initialized detector with: {'model_name': '车辆测速', 'model_filename': 'car_type_v11.engine', 'input_size': [640, 640], 'classes': ['自行车', '车', '面包车', '卡车', '三轮车', '带蓬三轮车', '公交车', '摩托车'], 'predict_method': 'CarVelocityEngine', 'predict_params': {'conf': 0.3}}
Initialized detector with: {'model_name': '黄色网格禁停区违停识别', 'model_filename': 'yellow_grid_v6.engine', 'car_model': 'car_type_v11.engine', 'input_size': [640, 640], 'classes': ['违停车辆'], 'predict_method': 'CarViolationEngine', 'predict_params': {'conf': 0.4}}
Initialized detector with: {'model_name': '火焰烟雾识别', 'model_filename': 'fire_smoke_v3.engine', 'input_size': [640, 640], 'classes': ['火', '烟'], 'predict_method': 'ObjectDetectorEngine', 'predict_params': {'conf': 0.3}}
Initialized detector with: {'model_name': '骑车安全帽检测', 'model_filename': 'motor_helmet_v1.engine', 'input_size': [1280, 1280], 'classes': ['未佩戴头盔', '佩戴头盔', '人', '自行车'], 'alarms_classes': ['未佩戴头盔'], 'predict_method': 'MotorHelmetEngine', 'predict_params': {'conf': 0.3}}
Initialized detector with: {'model_name': '人员聚集识别', 'model_filename': 'people_v8_crowd.engine', 'input_size': [1280, 1280], 'classes': ['people'], 'predict_method': 'PersonCrowdEngine_2', 'predict_params': {'conf': 0.3}}
✅ 横幅检测模型加载成功
✅ 横幅识别器初始化完成
Initialized detector with: {'model_name': '车辆识别', 'model_filename': 'car_type_v11.engine', 'input_size': [640, 640], 'classes': ['自行车', '车', '面包车', '卡车', '三轮车', '带蓬三轮车', '公交车', '摩托车'], 'predict_method': 'ObjectDetectorEngine', 'predict_params': {'conf': 0.3}}
INFO:     Started server process [1810119]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
==================================================
接收到启动任务请求:
  任务ID: 1945391832323379201
  设备SN: 1581F8HGX252500A00CG
  应用代码: car_plate
  输入流: rtmp://*************:19403/live/1581F8HGX252500A00CG_99-0-0?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTI2NTYxMzEsInVzZXJuYW1lIjoiZWNhZGZmOTYtYTcxMS00YzMzLTk5MmYtNmNlNTViZDhiMWZkIn0.zgG5-0aM8e7KIq5ivEPxnzHhZvKZO6vf3lgWMjtq77w
  输出流: rtmp://*************:19403/live/1945391832323379201
  回调URL: http://***************:3200/hisun-boot/dock/zhimou/ai-hook2
  MQTT配置: 未提供，将使用默认配置
==================================================
解析的模型列表: ['car_plate']
✅ 车牌检测模型加载成功
✅ 车牌识别器初始化完成
INFO:     **************:56504 - "POST /start HTTP/1.1" 200 OK
['cuda', 'drm']
Connected to MQTT Broker!
Video stream format: <av.VideoFormat cuda, 960x720>
Video stream color range: 1
分辨率从 0x0 变为 960x720
Loading /tmp/tmpfem4ro39.engine for TensorRT inference...
Loading /tmp/tmpfem4ro39.engine for TensorRT inference...
[07/16/2025-15:55:43] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
Loading /tmp/tmpfem4ro39.engine for TensorRT inference...
[07/16/2025-15:55:43] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
Loading /tmp/tmpfem4ro39.engine for TensorRT inference...
[07/16/2025-15:55:43] [TRT] [I] The logger passed into createInferRuntime differs from one already provided for an existing builder, runtime, or refitter. Uses of the global logger, returned by nvinfer1::getLogger(), will return the existing value.
[07/16/2025-15:55:43] [TRT] [I] Loaded engine size: 94 MiB
[07/16/2025-15:55:43] [TRT] [I] Loaded engine size: 94 MiB
[07/16/2025-15:55:43] [TRT] [I] Loaded engine size: 94 MiB
[07/16/2025-15:55:43] [TRT] [I] Loaded engine size: 94 MiB
[07/16/2025-15:55:43] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +0, GPU +95, now: CPU 0, GPU 449 (MiB)
[07/16/2025-15:55:43] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +1, GPU +191, now: CPU 2, GPU 641 (MiB)
[07/16/2025-15:55:43] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +1, GPU +191, now: CPU 3, GPU 736 (MiB)
[07/16/2025-15:55:43] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +1, GPU +95, now: CPU 3, GPU 736 (MiB)
