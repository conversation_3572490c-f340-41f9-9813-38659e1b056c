#!/usr/bin/env python3
"""
横幅文字识别测试脚本
"""

import cv2
import numpy as np
from config.model_config import ModelConfig
from predict_method.banner_text_recognition import BannerTextRecognition
import time
import json


class MockTask:
    """模拟任务对象"""
    def __init__(self):
        self.device_sn = "test_device_001"
        self.business_id = "banner_text_test"
        self.callback_url = None  # 可以设置为实际的回调URL
        self.latitude = 39.9042
        self.longitude = 116.4074


def create_test_image_with_text():
    """创建包含文字的测试图像"""
    # 创建白色背景
    img = np.ones((400, 800, 3), dtype=np.uint8) * 255
    
    # 添加一些测试文字
    texts = [
        "欢迎来到北京",
        "Welcome to Beijing",
        "安全第一 预防为主",
        "Safety First Prevention Oriented"
    ]
    
    y_positions = [80, 140, 240, 300]
    
    for i, text in enumerate(texts):
        cv2.putText(img, text, (50, y_positions[i]), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 0, 0), 2)
    
    # 添加一个红色横幅背景
    cv2.rectangle(img, (30, 200), (770, 260), (0, 0, 200), -1)
    cv2.putText(img, texts[2], (50, 240), 
               cv2.FONT_HERSHEY_SIMPLEX, 1.2, (255, 255, 255), 2)
    
    return img


def test_banner_text_recognition():
    """测试横幅文字识别功能"""
    print("开始测试横幅文字识别...")
    
    try:
        # 创建模拟任务
        task = MockTask()
        
        # 加载模型配置
        config_path = "config/banner_text_recognition_config.yaml"
        model_config = ModelConfig(config_path)
        
        # 创建识别器
        recognizer = BannerTextRecognition(model_config, task)
        
        print(f"模型初始化成功")
        print(f"API地址: {recognizer.api_base}")
        print(f"模型名称: {recognizer.model_name}")
        
        # 创建测试图像
        test_image = create_test_image_with_text()
        
        # 保存测试图像
        cv2.imwrite("test_banner_image.jpg", test_image)
        print("测试图像已保存为 test_banner_image.jpg")
        
        # 运行预测管道
        print("\n开始文字识别...")
        start_time = time.time()
        
        predictions = recognizer.predict_pipeline(test_image)
        
        end_time = time.time()
        print(f"识别完成，耗时: {end_time - start_time:.2f}秒")
        
        # 打印识别结果
        print("\n识别结果:")
        print(json.dumps(predictions, ensure_ascii=False, indent=2))
        
        # 可视化结果
        result_image = recognizer.visualize(test_image, predictions)
        
        # 保存结果图像
        cv2.imwrite("test_banner_result.jpg", result_image)
        print("\n结果图像已保存为 test_banner_result.jpg")
        
        # 测试回调功能
        print("\n测试回调功能...")
        recognizer.callback(task, result_image, predictions, time.time())
        
        # 显示图像（如果在图形环境中）
        try:
            cv2.imshow("Original Image", test_image)
            cv2.imshow("Recognition Result", result_image)
            print("\n按任意键关闭图像窗口...")
            cv2.waitKey(0)
            cv2.destroyAllWindows()
        except:
            print("无法显示图像（可能不在图形环境中）")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_with_real_image(image_path):
    """使用真实图像测试"""
    print(f"\n使用真实图像测试: {image_path}")
    
    try:
        # 创建模拟任务
        task = MockTask()
        
        # 加载模型配置
        config_path = "config/banner_text_recognition_config.yaml"
        model_config = ModelConfig(config_path)
        
        # 创建识别器
        recognizer = BannerTextRecognition(model_config, task)
        
        # 加载图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"无法加载图像: {image_path}")
            return False
        
        print(f"图像尺寸: {image.shape}")
        
        # 运行预测
        start_time = time.time()
        predictions = recognizer.predict_pipeline(image)
        end_time = time.time()
        
        print(f"识别完成，耗时: {end_time - start_time:.2f}秒")
        print("\n识别结果:")
        print(json.dumps(predictions, ensure_ascii=False, indent=2))
        
        # 可视化结果
        result_image = recognizer.visualize(image, predictions)
        
        # 保存结果
        result_path = f"result_{image_path.split('/')[-1]}"
        cv2.imwrite(result_path, result_image)
        print(f"\n结果图像已保存为: {result_path}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("=" * 60)
    print("横幅文字识别测试")
    print("=" * 60)
    
    # 测试1: 使用生成的测试图像
    success1 = test_banner_text_recognition()
    
    # 测试2: 如果有真实图像，可以取消注释下面的代码进行测试
    # image_path = "path/to/your/test/image.jpg"
    # success2 = test_with_real_image(image_path)
    
    print("\n" + "=" * 60)
    if success1:
        print("✅ 测试完成！")
        print("\n使用说明:")
        print("1. 检查生成的配置文件: config/banner_text_recognition_config.yaml")
        print("2. 查看测试图像: test_banner_image.jpg")
        print("3. 查看识别结果: test_banner_result.jpg")
        print("4. 根据需要调整配置参数")
    else:
        print("❌ 测试失败，请检查配置和网络连接")
    print("=" * 60)
