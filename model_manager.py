import os
import onnxruntime as ort
from pathlib import Path
from typing import Dict, Any, List, Tuple
import concurrent.futures
import numpy as np
from image_processor import ImageProcessor
from config import ModelConfig
from predict_method import get_process




class ModelManager:
    def __init__(self, models_dir: str = "models", max_workers: int = 4):
        self.models_dir = models_dir
        self.loaded_models: Dict[str, Dict[str, Any]] = {}
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers)
        os.makedirs(models_dir, exist_ok=True)
    
    def load_all_models(self):
        """加载 models 目录下所有 .pki 后缀的模型文件"""
        for model_file in Path(self.models_dir).glob("*.pki"):
            if model_file.is_file():
                self.load_model(model_file)  # 或者 model_file.stem 如果不需要后缀
    
    def load_model(self, model_dir_name: str):
        """加载单个模型并获取输入尺寸信息"""
        model_config= ModelConfig(model_dir_name)
        
        # 保存模型信息
        self.loaded_models[model_config.get_model_id()] = get_process(model_config)
        return self.loaded_models[model_config.get_model_id()]

    def get_model_input_size(self, model_id: str) -> Tuple[int, int]:
        """获取模型的期望输入尺寸 (高度, 宽度)"""
        if model_id not in self.loaded_models:
            raise ValueError(f"Model {model_id} not loaded")
        _, h, w = self.loaded_models[model_id]["input_shape"]
        return (h, w)
    
    def predict_single(self, model_id: str, image_data: str, model_params: Dict[str, Any] = None):
        """使用单个模型进行图片预测"""
        if model_id not in self.loaded_models:
            raise ValueError(f"Model {model_id} not loaded")
        
        model_info = self.loaded_models[model_id]
        params = model_params or {}
        try:
            # 解码图片
            image = ImageProcessor.decode_image(image_data)
            
            final_output=model_info.predict(image)
            
            return {
                "model_id": model_id,
                "result": final_output,
                "model_name": model_info["config"]["model_name"],
                "params_used": params
            }
        except Exception as e:
            return {
                "model_id": model_id,
                "error": str(e),
                "success": False
            }
    
    def predict_multiple(self, model_specs: List[Dict[str, Any]], image_data: str):
        """
        使用多个模型并行预测同一图片
        model_specs格式: [{"model_id": "model1", "params": {...}}, ...]
        """
        futures = []
        results = []
        
        # 提交所有预测任务
        for spec in model_specs:
            model_id = spec["model_id"]
            params = spec.get("params", {})
            
            future = self.executor.submit(
                self.predict_single, 
                model_id, 
                image_data, 
                params
            )
            futures.append((model_id, future))
        # 获取所有结果
        for model_id, future in futures:
            results.append(future.result())
        
        return results

    def get_loaded_models(self) -> Dict[str, Dict[str, Any]]:
        """获取所有已加载模型的信息，包括输入尺寸"""
        return {
            model_id: {
                "model_name": info.get_model_name(),
                "supported_params": info.get_predict_params()
            }
            for model_id, info in self.loaded_models.items()
        }