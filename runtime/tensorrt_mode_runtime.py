import tensorrt as trt
import pycuda.driver as cuda
import numpy as np
from threading import Lock

lock =Lock()

class TensorRTModelRunTime:
    def __init__(self, file):
        import pycuda.autoinit
        # 初始化TensorRT
        self.logger = trt.Logger(trt.Logger.WARNING)
        self.engine = self.load_engine(file)
        self.context = self.engine.create_execution_context()
        self.inputs, self.outputs, self.bindings, self.stream = self.allocate_buffers()
        
    def load_engine(self, file):
        with  trt.Runtime(self.logger) as runtime:
            return runtime.deserialize_cuda_engine(file)
        
    def allocate_buffers(self):
        inputs = []
        outputs = []
        bindings = []
        
        for i in range(self.engine.num_io_tensors):
            tensor_name = self.engine.get_tensor_name(i)
            tensor_shape = self.engine.get_tensor_shape(tensor_name)
            dtype = self.engine.get_tensor_dtype(tensor_name)
            size = trt.volume(tensor_shape) * dtype.itemsize
            device_mem = cuda.mem_alloc(size)
            bindings.append(int(device_mem))
            
            if self.engine.get_tensor_mode(tensor_name) == trt.TensorIOMode.INPUT:
                inputs.append({
                    'name': tensor_name,
                    'memory': device_mem,
                    'shape': tensor_shape,
                    'dtype': dtype
                })
                self.context.set_tensor_address(tensor_name, device_mem)
            else:
                outputs.append({
                    'name': tensor_name,
                    'memory': device_mem,
                    'shape': tensor_shape,
                    'dtype': dtype
                })
                self.context.set_tensor_address(tensor_name, device_mem)
        
        return inputs, outputs, bindings, cuda.Stream()
    
    def predict(self, image):
        with lock:
            return self.infer(image)
    
    def infer(self, input_data):
        # 确保输入数据形状匹配
        assert input_data.shape == tuple(self.inputs[0]['shape']), \
            f"Input shape mismatch: expected {self.inputs[0]['shape']}, got {input_data.shape}"
        
        # 传输输入数据到GPU
        cuda.memcpy_htod_async(self.inputs[0]['memory'], input_data.ravel(), self.stream)
        
        # 执行推理
        self.context.execute_async_v3(self.stream.handle)
        
        # 为输出分配主机内存并传输回来
        output_data = []
        for out in self.outputs:
            host_mem = np.empty(out['shape'], dtype=trt.nptype(out['dtype']))
            cuda.memcpy_dtoh_async(host_mem, out['memory'], self.stream)
            output_data.append(host_mem)
        
        # 同步流
        self.stream.synchronize()
        
        return output_data

    def __del__(self):
        # 清理资源
        del self.context
        del self.engine
if __name__ == "__main__":

    TensorRTModelRunTime('/home/<USER>/project/modelManger/models/car/model.engine')
        