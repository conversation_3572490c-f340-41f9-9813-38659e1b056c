import time
import cv2
import numpy as np
import onnxruntime
import os
from threading import Lock
import os
os.environ["CUDA_VISIBLE_DEVICES"] = "1" 
class OnnxModelRuntime:
    def __init__(self, path,num_workers=1):
        # Initialize model
        self.num_workers=num_workers
        self.initialize_model(path)
       

    def predict(self, image):
        return self.detect_objects(image)

    def initialize_model(self, path):
        sess_options = onnxruntime.SessionOptions()
        print(onnxruntime.get_available_providers())
        self.sessions =[{'session':onnxruntime.InferenceSession(path,sess_options=sess_options,providers=['CUDAExecutionProvider']),'lock':Lock()} for _ in range(self.num_workers)]
        # Get model info
        self.session = self.sessions[0]['session']
        self.get_input_details()
        self.get_output_details()

    def detect_objects(self, image):
        for i in range(len(self.sessions)):
            # Try to acquire the lock (non-blocking)
            if self.sessions[i]['lock'].acquire(blocking=False):
                try:
                    # Prepare input tensor
                    input_tensor = self.prepare_input(image)
                    # Perform inference
                    outputs =  outputs = self.inference(self.sessions[i]['session'],input_tensor)
                    return outputs
                finally:
                    # Ensure the lock is always released
                    self.sessions[i]['lock'].release()
        # Perform inference on the image
        with self.sessions[0]['lock']:
            input_tensor = self.prepare_input(image)
            outputs = self.inference(self.sessions[0]['session'],input_tensor)
            return outputs
    def prepare_input(self, img):
        """
        对输入图像进行预处理，以便进行推理。
        参数：
            img: 输入图像数据（BGR格式）
        返回：
            image_data: 经过预处理的图像数据，准备进行推理。
        """
        # 获取输入图像的高度和宽度
        # 通过除以 255.0 来归一化图像数据
        image_data = np.array(img) / 255.0
 
        # 将图像的通道维度移到第一维
        image_data = np.transpose(image_data, (2, 0, 1))  # 通道优先
 
        # 扩展图像数据的维度，以匹配模型输入的形状
        image_data = np.expand_dims(image_data, axis=0).astype(np.float32)
 
        # 返回预处理后的图像数据
        return image_data
 
 

 
 
    def inference(self,session, input_tensor):
        start=time.time()
        outputs = session.run(self.output_names, {self.input_names[0]: input_tensor})
        end=time.time()
        print(f"运行耗时: {end-start:.4f} 秒")
        return outputs

    def get_input_details(self):
        model_inputs = self.session.get_inputs()
        self.input_names = [model_inputs[i].name for i in range(len(model_inputs))]

        self.input_shape = model_inputs[0].shape
        self.input_height = self.input_shape[2]
        self.input_width = self.input_shape[3]


    def get_output_details(self):
        model_outputs = self.session.get_outputs()
        self.output_names = [model_outputs[i].name for i in range(len(model_outputs))]


if __name__ == "__main__":
    model_path = "/home/<USER>/project/carTrack/people_dr/train7/weights/best.onnx"
    model = OnnxModelRuntime(model_path)
    out=model.predict( cv2.imread('/home/<USER>/project/carTrack/people_v4/images/Train/frame_000030.png'))
    print(out)