import time
import cv2
import numpy as np
from ultralytics import YOLO
import os
from threading import Lock

import tempfile
import shutil

def save_stream_to_temp(stream, suffix=None):
    """
    将文件流保存到临时文件
    :param stream: 文件流对象
    :param suffix: 文件后缀名
    :return: 临时文件路径
    """
    # 创建临时文件
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=suffix)
    temp_path = temp_file.name
    
    try:
        # 将流内容写入临时文件
        if hasattr(stream, 'read'):
            shutil.copyfileobj(stream, temp_file)
        else:
            temp_file.write(stream)
    finally:
        temp_file.close()
    
    return temp_path
import os
os.environ["CUDA_DEVICE_ORDER"]="PCI_BUS_ID"
import threading
thread_local = threading.local()

class YoloModelRuntime:
    def __init__(self, path,num_workers=4):
        # Initialize model
        self.num_workers=num_workers
        self.initialize_model(path)

    def predict(self, image):
        return self.detect_objects(image)

    def initialize_model(self, path):
        file=save_stream_to_temp(path,'.pt')
        self.sessions =[{'session':YOLO(file),'lock':Lock()} for _ in range(self.num_workers)]
        # Get model info
        self.session = self.sessions[0]['session']

    def detect_objects(self, image):
        for i in range(len(self.sessions)):
            # Try to acquire the lock (non-blocking)
            if self.sessions[i]['lock'].acquire(blocking=False):
                try:
                    # Prepare input tensor
                    # Perform inference
                    outputs =  outputs = self.inference(self.sessions[i]['session'],image)
                    return outputs
                finally:
                    # Ensure the lock is always released
                    self.sessions[i]['lock'].release()
        # Perform inference on the image
        with self.sessions[0]['lock']:
            outputs = self.inference(self.sessions[0]['session'],image)
            return outputs
    def inference(self,session, image):
        start_time = time.time()  # 记录开始时间
        outputs = session.predict(image, device=3)
        end_time = time.time()
        elapsed_time = end_time - start_time  # 计算耗时（秒）
        print(f"运行耗时: {elapsed_time:.4f} 秒")  # 保留4位小数
        return outputs

