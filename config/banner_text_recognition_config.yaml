# 横幅文字识别模型配置文件
model_name: "qwen2.5-vl-7b-instruct"
model_type: "banner_text_recognition"
model_id: "banner_text_001"
predict_method: "BannerTextRecognition"

# OpenAI API 配置
api_base: "http://192.168.171.53:30001/v1-openai"
api_key: "gpustack_b21d482da5b75d39_f3ff70a20a773dbae3b64303b6061292"

# 预测参数
predict_params:
  confidence_threshold: 0.5  # 置信度阈值
  max_tokens: 1000          # 最大token数
  temperature: 0.1          # 温度参数，控制输出的随机性

# 目标检测参数
detection_params:
  detection_confidence: 0.5  # 检测置信度阈值
  input_size: [640, 640]     # 检测模型输入尺寸
  classes: ["banner"]        # 检测类别

# 横幅识别专门参数
banner_detection:
  focus_only_banners: true   # 只关注横幅内容
  min_banner_length: 4       # 横幅文字最小长度
  exclude_license_plates: true  # 排除车牌号码
  exclude_door_numbers: true    # 排除门牌号码
  exclude_prices: true          # 排除价格信息

# 图像预处理参数
preprocessing:
  max_width: 1920          # 最大图像宽度
  jpeg_quality: 95         # JPEG压缩质量

# 可视化参数
visualization:
  font_scale: 0.7          # 字体大小
  font_thickness: 2        # 字体粗细
  max_display_length: 50   # 最大显示文字长度
  
# 颜色配置 (BGR格式)
colors:
  high_confidence: [0, 255, 0]    # 绿色 - 高置信度 (>=0.8)
  medium_confidence: [0, 255, 255] # 黄色 - 中等置信度 (>=0.5)
  low_confidence: [0, 165, 255]   # 橙色 - 低置信度 (<0.5)
  error_color: [0, 0, 255]        # 红色 - 错误信息
  info_color: [255, 255, 255]     # 白色 - 一般信息

# 回调配置
callback:
  enabled: true
  include_raw_response: false  # 是否包含原始API响应
  
# 日志配置
logging:
  level: "INFO"
  log_api_requests: false   # 是否记录API请求详情
  log_responses: false      # 是否记录API响应详情
