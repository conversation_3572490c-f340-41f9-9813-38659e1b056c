from abc import ABC, abstractmethod

from typing import Union, Any, Dict, Optional, BinaryIO
from PIL import Image
import numpy as np
from pathlib import Path
import zipfile
import yaml



class ModelConfig(ABC):
    def __init__(self, config_path: Union[str, Path]):
        """Initialize predictor from YAML or PKI (ZIP) file without extraction.
        
        Args:
            config_path: Path to YAML config or PKI (ZIP) file.
        """
        self.config_path = Path(config_path)
        self.pki_zip = None  # type: Optional[zipfile.ZipFile]
        self.config = None  # type: Dict[str, Any]

        if self.config_path.suffix.lower() == '.pki':
            # Handle PKI (ZIP) - load YAML from ZIP without extraction
            self.pki_zip = zipfile.ZipFile(self.config_path, 'r')
            self.config = self._load_config_from_zip()
        else:
            # Handle standalone YAML
            self.config = self._load_config_from_file(self.config_path)
    def get_model_id(self): 
        return self.config_path.name.replace('.pki', '')
    def get_model_name(self): 
        return self.config.get('model_name')
    def get_predict_params(self): 
        return self.config.get('predict_params')
    def _get_model_file(self) -> BinaryIO:
        """Get the model file from PKI ZIP or local path."""
        return self._get_file_by_key('model_filename')
    def _get_file_by_key(self,key) -> BinaryIO:
        """Get the model file from PKI ZIP or local path."""
        model_filename = self.config.get(key)
        if model_filename is None:
            raise KeyError(key+" not found in config.")
        if self.pki_zip is not None:
            # Read from ZIP
            return self.pki_zip.open(model_filename, 'r')
        else:
            # Read from local file
            return open(model_filename, 'rb')
    def _load_config_from_file(self, config_path: Path) -> Dict[str, Any]:
        """Load YAML from a standalone file."""
        with open(config_path, 'r') as f:
            return yaml.safe_load(f)

    def _load_config_from_zip(self) -> Dict[str, Any]:
        """Load YAML from PKI (ZIP) without extraction."""
        if self.pki_zip is None:
            raise ValueError("PKI ZIP file not opened.")

        # Search for YAML in the ZIP
        yaml_files = [
            name for name in self.pki_zip.namelist()
            if name.endswith(('.yaml', '.yml'))
        ]

        if not yaml_files:
            raise FileNotFoundError("No YAML config found in PKI package.")
        
        # Use the first YAML found
        with self.pki_zip.open(yaml_files[0], 'r') as yaml_file:
            return yaml.safe_load(yaml_file)

    def _read_file_from_zip(self, file_path: str) -> BinaryIO:
        """Read a file from PKI ZIP (if available)."""
        if self.pki_zip is None:
            raise ValueError("Not a PKI package or ZIP not loaded.")
        return self.pki_zip.open(file_path, 'r')

    def __del__(self):
        """Ensure ZIP file is closed on object destruction."""
        if self.pki_zip is not None:
            self.pki_zip.close()