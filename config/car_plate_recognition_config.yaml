# 车牌识别模型配置文件
model_name: "qwen2.5-vl-7b-instruct"
model_type: "car_plate_recognition"
model_id: "car_plate_001"
predict_method: "CarPlateRecognition"

# OpenAI API 配置
api_base: "http://192.168.171.53:30001/v1"
api_key: "gpustack_5e7a262b5803b6bc_16b314c2d6113ca26b285e5190180f24"

# 预测参数
predict_params:
  max_tokens: 1000          # 最大token数
  temperature: 0.1          # 温度参数，控制输出的随机性

# 目标检测参数
detection_params:
  detection_confidence: 0.5  # 检测置信度阈值
  input_size: [640, 640]     # 检测模型输入尺寸
  classes: ["car_plate"]     # 检测类别

# 图像预处理参数
preprocessing:
  max_width: 960           # 最大图像宽度
  jpeg_quality: 95         # JPEG压缩质量

# 可视化参数
visualization:
  font_scale: 0.7          # 字体大小
  font_thickness: 2        # 字体粗细
  
# 颜色配置 (BGR格式)
colors:
  detection_box: [0, 255, 0]    # 绿色 - 检测框
  text_color: [255, 255, 255]   # 白色 - 文字
  error_color: [0, 0, 255]      # 红色 - 错误信息

# 回调配置
callback:
  enabled: true
  async_recognition: true   # 异步识别车牌号码
  
# 日志配置
logging:
  level: "INFO"
  log_api_requests: false   # 是否记录API请求详情
