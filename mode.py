
from typing import Any, Dict, Optional

from pydantic import BaseModel, Field, field_validator


class ModelPredictionSpec(BaseModel):
    model_id: str
    params: Dict[str, Any] = {}  # 每个模型独立的参数

class MqttConfig(BaseModel):
    host: Optional[str] = Field(None, alias="host")
    port: Optional[int] = Field(None, alias="port")
    username: Optional[str] = Field(None, alias="username")
    password: Optional[str] = Field(None, alias="password")

class StartTaskRequest(BaseModel):
    input_rtmp_url: str = Field(..., alias="inputRtmpUrl")
    task_id: str = Field(..., alias="taskId")
    device_sn: Optional[str] = Field(None, alias="deviceSn")
    app_code: str = Field(..., alias="appCode")
    out_rtmp_url: str = Field(..., alias="outRtmpUrl")
    business_id: str = Field(..., alias="businessId")
    callback_url: str = Field(..., alias="callbackUrl")
    mqtt_config: Optional[MqttConfig] = Field(None, alias="mqttConfig")
    @field_validator('input_rtmp_url')
    def input_rtmp_url_must_not_be_empty(cls, v):
        if not v.strip():
            raise ValueError("输入流地址不能为空")
        return v
    @field_validator('task_id')
    def task_id_must_not_be_empty(cls, v):
        if not v.strip():
            raise ValueError("任务ID不能为空")
        return v
    @field_validator('app_code')
    def app_code_must_not_be_empty(cls, v):
        if not v.strip():
            raise ValueError("app_code不能为空")
        return v
    @field_validator('out_rtmp_url')
    def out_rtmp_url_not_be_empty(cls, v):
        if not v.strip():
            raise ValueError("输出流地址不能为空")
        return v

class StopTaskRequest(BaseModel):
    task_id:str=Field(..., alias="taskId")
    @field_validator('task_id')
    def task_id_must_not_be_empty(cls, v):
        if not v.strip():
            raise ValueError("任务ID不能为空")
        return v