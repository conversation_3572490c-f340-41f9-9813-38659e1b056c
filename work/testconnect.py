import av
import time
import cv2
import numpy as np
import subprocess
import 
# 打开视频流
def check_stream_resolution(stream_url):
    cmd = [
        'ffprobe',
        '-v', 'error',
        '-select_streams', 'v:0',
        '-show_entries', 'stream=width,height',
        '-of', 'json',
        stream_url
    ]
    
    try:
        result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, check=True)
        data = json.loads(result.stdout)
        width = data['streams'][0]['width']
        height = data['streams'][0]['height']
        return width, height
    except Exception as e:
        print(f"Error getting resolution: {e}")
        return None, None

    
    print(f"Initial resolution: {width}x{height}")

for i in range(1, 100):
    start =time.time()
    check_stream_resolution('rtmp://**************:1935/live/stream2')
    end =time.time()
    print(end-start)


