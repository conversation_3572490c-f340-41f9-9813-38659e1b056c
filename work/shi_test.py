from sahi import AutoDetectionModel
import time
start=time.time()
detection_model = AutoDetectionModel.from_pretrained(
    model_type="ultralytics",
    model_path='car_type_v13.pt',
    confidence_threshold=0.45,
    device="cuda:0",  # or 'cuda:0'
)
from sahi.predict import get_sliced_prediction

result = get_sliced_prediction(
    "/home/<USER>/modelManger_deply/work/p1镜头6080.tif",
    detection_model,
    slice_height=640,
    slice_width=640,
    overlap_height_ratio=0.2,
    overlap_width_ratio=0.2,
)
end = time.time()
print("推理时间：", end - start)
result.export_visuals(export_dir="demo_data/")