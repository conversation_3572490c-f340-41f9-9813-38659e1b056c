from ultralytics import YOLO
import cv2
import numpy as np
from tqdm import tqdm
import time
import threading
from queue import Queue

# 1. 加载TensorRT引擎模型
model_path = "/home/<USER>/project/modelManger/models/car/car_type_v5_p2.engine"
model = YOLO(model_path)

# 2. 视频文件路径
video_path = "/root/car_8.mp4"

# 3. 批处理参数设置
batch_size = 8  # 根据GPU内存调整
frame_skip = 0  # 跳帧处理(0表示不跳过)

# 创建队列
frame_queue = Queue(maxsize=20)
result_queue = Queue()
processed_frames = 0
total_frames = 0

def frame_reader(video_path, frame_queue):
    global total_frames
    cap = cv2.VideoCapture(video_path)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        current_frame = int(cap.get(cv2.CAP_PROP_POS_FRAMES))
        if frame_skip > 0 and current_frame % (frame_skip + 1) != 0:
            continue
        
        frame_queue.put((current_frame, frame))
    
    frame_queue.put(None)  # 结束信号
    cap.release()

def batch_processor(frame_queue, result_queue):
    batch_frames = []
    frame_ids = []
    
    while True:
        item = frame_queue.get()
        if item is None:  # 结束信号
            if batch_frames:  # 处理剩余的帧
                res =model(batch_frames, verbose=False)
                result_queue.put(len(batch_frames))
            frame_queue.put(None)  # 传递给其他可能的工作线程
            break
        
        frame_id, frame = item
        batch_frames.append(frame)
        frame_ids.append(frame_id)
        
        if len(batch_frames) >= batch_size:
            res = model(batch_frames, verbose=False)
            result_queue.put(len(batch_frames))
            batch_frames = []
            frame_ids = []

def progress_monitor():
    global processed_frames
    pbar = tqdm(total=total_frames, desc="Processing Video")
    
    while True:
        num = result_queue.get()
        if num == -1:  # 结束信号
            break
        processed_frames += num
        pbar.update(num)
    
    pbar.close()

# 4. 执行多线程处理
start_time = time.time()

# 创建并启动线程
reader_thread = threading.Thread(target=frame_reader, args=(video_path, frame_queue))
processor_threads = [threading.Thread(target=batch_processor, args=(frame_queue, result_queue)) for _ in range(4)]  # 2个处理线程
monitor_thread = threading.Thread(target=progress_monitor)

reader_thread.start()
for t in processor_threads:
    t.start()
monitor_thread.start()

# 等待线程完成
reader_thread.join()
for t in processor_threads:
    t.join()
result_queue.put(-1)  # 通知监控线程结束
monitor_thread.join()

end_time = time.time()

# 5. 打印统计信息
print(f"\n视频处理完成!")
print(f"处理帧数: {processed_frames}")
print(f"批处理大小: {batch_size}")
print(f"总耗时: {end_time - start_time:.2f}秒")
print(f"平均FPS: {processed_frames/(end_time - start_time):.2f}")