import cv2
import time
# 从文件读取视频流
video_path = 'rtmp://117.41.183.120:19403/live/8UUDMCT00AX6S7_165-0-7'
cap = cv2.VideoCapture(video_path)

# 或者从摄像头读取视频流
# cap = cv2.VideoCapture(0)  # 0 表示默认摄像头

while cap.isOpened():
    ret, frame = cap.read()
    # 如果正确读取帧，ret为True
    if not ret:
        print("无法读取视频流或视频结束")
        break
    frame_pos = cap.get(cv2.CAP_PROP_POS_FRAMES)
    
    # 获取当前时间戳（毫秒）
    timestamp_ms = cap.get(cv2.CAP_PROP_POS_MSEC)
    
    # 计算当前时间（秒）
    timestamp_sec = timestamp_ms / 1000.0
    
    print(f"帧号: {frame_pos}, 时间戳: {timestamp_ms}ms, 相对时间: {timestamp_sec:.2f}s")