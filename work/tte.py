import tensorrt as trt
import pycuda.driver as cuda
import pycuda.autoinit
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import matplotlib.pyplot as plt
import cv2
import cv2 as cv
from pathlib import Path
CLASS_NAMES = {
    0: 'bicycle',
    1: 'car',
    2: 'van',
    3: 'truck',
    4: 'tricycle',
    5: 'awning-tricycle',
    6: 'bus',
    7: 'motor'
}
import time
from typing import Union, Any, Dict, List, Tuple
class TensorRTInfer:
    def __init__(self, engine_path):
        # 初始化TensorRT
        self.classes = CLASS_NAMES
        self.color_palette = np.random.uniform(0, 255, size=(len(self.classes), 3))
        self.logger = trt.Logger(trt.Logger.WARNING)
        self.engine = self.load_engine(engine_path)
        self.context = self.engine.create_execution_context()
        self.inputs, self.outputs, self.bindings, self.stream = self.allocate_buffers()
        self.confidence_thres = 0.25
        self.iou_thres = 0.3
        
    def load_engine(self, engine_path):
        with open(engine_path, "rb") as f, trt.Runtime(self.logger) as runtime:
            return runtime.deserialize_cuda_engine(f.read())
    def preprocess(self, image_path, target_shape):
        """
        优化后的预处理方法，整合了图像加载、letterbox处理和归一化
        参数：
            image_path: 图像路径或numpy数组
            target_shape: 目标形状 (batch, channel, height, width)
        返回：
            np.ndarray: 预处理后的图像数据
        """
        # 1. 图像加载优化
        if isinstance(image_path, (str, Path)):
            # 使用cv2.imread直接读取，避免中间转换
            img = cv2.imread(str(image_path))
            if img is None:
                raise ValueError(f"无法加载图像: {image_path}")
        elif isinstance(image_path, Image.Image):
            # PIL.Image转numpy优化
            img = np.array(image_path)[..., ::-1]  # RGB转BGR
        else:
            img = image_path
        
        # 记录原始尺寸
        self.img_height, self.img_width = img.shape[:2]
        
        # 2. letterbox处理优化
        # 使用更高效的letterbox实现
        img, self.ratio, (self.dw, self.dh) = self.optimized_letterbox(
            img, 
            new_shape=(target_shape[2], target_shape[3])
        )
        
        # 3. 颜色空间转换和归一化优化
        # 使用更高效的转换方式
        img = img.astype(np.float32) / 255.0  # 归一化
        img = np.transpose(img, (2, 0, 1))    # HWC转CHW
        img = np.expand_dims(img, axis=0)     # 添加batch维度
        
        return img

    def optimized_letterbox(self, img, new_shape=(640, 640), color=(114, 114, 114)):
        """
        优化的letterbox实现，减少不必要的计算和内存分配
        """
        # 获取原始尺寸
        h, w = img.shape[:2]
        self.input_width, self.input_height = new_shape
        
        # 计算缩放比例
        scale = min(new_shape[0] / h, new_shape[1] / w)
        
        # 计算新尺寸
        new_unpad = (int(round(w * scale)), int(round(h * scale)))
        
        # 直接使用cv2.resize的dsize参数，避免额外计算
        if (w, h) != new_unpad:
            img = cv2.resize(img, new_unpad, interpolation=cv2.INTER_LINEAR)
        
        # 计算填充
        dw = new_shape[1] - new_unpad[0]
        dh = new_shape[0] - new_unpad[1]
        dw /= 2
        dh /= 2
        
        # 使用更高效的填充方式
        top, bottom = int(round(dh)), int(round(dh))
        left, right = int(round(dw)), int(round(dw))
        
        # 使用cv2.copyMakeBorder的常量填充
        img = cv2.copyMakeBorder(img, top, bottom, left, right, 
                            cv2.BORDER_CONSTANT, value=color)
        
        return img, (scale, scale), (dw, dh)

    def postprocess(self, output):
        """
        优化后的后处理方法，使用向量化操作加速处理
        参数：
            output (numpy.ndarray): 模型的输出
        返回：
            list: 包含检测结果的字典列表
        """
        # 转置并压缩输出，以匹配预期形状
        outputs = np.transpose(np.squeeze(output[0]))
        
        # 向量化操作提取所有框和分数
        boxes_xywh = outputs[:, :4]
        classes_scores = outputs[:, 4:]
        
        # 向量化计算最大分数和类别ID
        max_scores = np.amax(classes_scores, axis=1)
        class_ids = np.argmax(classes_scores, axis=1)
        
        # 提前过滤低置信度结果
        mask = max_scores >= self.confidence_thres
        boxes_xywh = boxes_xywh[mask]
        max_scores = max_scores[mask]
        class_ids = class_ids[mask]
        
        if len(boxes_xywh) == 0:
            return []
        
        # 向量化调整框到原始图像尺寸
        boxes_xywh[:, 0] = (boxes_xywh[:, 0] - self.dw) / self.ratio[0]  # x
        boxes_xywh[:, 1] = (boxes_xywh[:, 1] - self.dh) / self.ratio[1]  # y
        boxes_xywh[:, 2] = boxes_xywh[:, 2] / self.ratio[0]             # w
        boxes_xywh[:, 3] = boxes_xywh[:, 3] / self.ratio[1]             # h
        
        # 转换为(left, top, width, height)格式
        boxes = np.empty_like(boxes_xywh)
        boxes[:, 0] = boxes_xywh[:, 0] - boxes_xywh[:, 2] / 2  # left
        boxes[:, 1] = boxes_xywh[:, 1] - boxes_xywh[:, 3] / 2  # top
        boxes[:, 2] = boxes_xywh[:, 2]                         # width
        boxes[:, 3] = boxes_xywh[:, 3]                         # height
        boxes = boxes.astype(np.int32)
        
        # 执行NMS - 使用OpenCV的NMSBoxes
        nms_start = time.time()
        indices = cv2.dnn.NMSBoxes(boxes.tolist(), max_scores.tolist(), 
                                self.confidence_thres, self.iou_thres)
        nms_end = time.time()
        # 构建结果列表
        if isinstance(indices, np.ndarray):
            indices = indices.flatten()
            detections = [{
                'box': boxes[i].tolist(),
                'score': float(max_scores[i]),
                'class_id': int(class_ids[i])
            } for i in indices]
        else:
            detections = []
        return detections
 
    def allocate_buffers(self):
        inputs = []
        outputs = []
        bindings = []
        
        for i in range(self.engine.num_io_tensors):
            tensor_name = self.engine.get_tensor_name(i)
            tensor_shape = self.engine.get_tensor_shape(tensor_name)
            dtype = self.engine.get_tensor_dtype(tensor_name)
            size = trt.volume(tensor_shape) * dtype.itemsize
            device_mem = cuda.mem_alloc(size)
            bindings.append(int(device_mem))
            
            if self.engine.get_tensor_mode(tensor_name) == trt.TensorIOMode.INPUT:
                inputs.append({
                    'name': tensor_name,
                    'memory': device_mem,
                    'shape': tensor_shape,
                    'dtype': dtype
                })
                self.context.set_tensor_address(tensor_name, device_mem)
            else:
                outputs.append({
                    'name': tensor_name,
                    'memory': device_mem,
                    'shape': tensor_shape,
                    'dtype': dtype
                })
                self.context.set_tensor_address(tensor_name, device_mem)
        
        return inputs, outputs, bindings, cuda.Stream()
    

    def infer(self, input_data):
        # 确保输入数据形状匹配
        assert input_data.shape == tuple(self.inputs[0]['shape']), \
            f"Input shape mismatch: expected {self.inputs[0]['shape']}, got {input_data.shape}"
        
        # 传输输入数据到GPU
        cuda.memcpy_htod_async(self.inputs[0]['memory'], input_data.ravel(), self.stream)
        
        # 执行推理
        self.context.execute_async_v3(self.stream.handle)
        
        # 为输出分配主机内存并传输回来
        output_data = []
        for out in self.outputs:
            host_mem = np.empty(out['shape'], dtype=trt.nptype(out['dtype']))
            cuda.memcpy_dtoh_async(host_mem, out['memory'], self.stream)
            output_data.append(host_mem)
        
        # 同步流
        self.stream.synchronize()
        
        return output_data

 
    def draw_detections(self, img, box, score, class_id):
        """
        在输入图像上绘制检测到的边界框和标签。
        参数：
            img: 用于绘制检测结果的输入图像。
            box: 检测到的边界框。
            score: 对应的检测分数。
            class_id: 检测到的目标类别 ID。
        
        返回：
            None
        """
        # 提取边界框的坐标
        x1, y1, w, h = box
 
        # 获取类别对应的颜色
        color = self.color_palette[class_id]
 
        # 在图像上绘制边界框
        cv2.rectangle(img, (int(x1), int(y1)), (int(x1 + w), int(y1 + h)), color, 2)
 
        # 创建包含类别名和分数的标签文本
        label = f"{self.classes[class_id]}: {score:.2f}"
 
        # 计算标签文本的尺寸
        (label_width, label_height), _ = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)
 
        # 计算标签文本的位置
        label_x = x1
        label_y = y1 - 10 if y1 - 10 > label_height else y1 + 10
 
        # 绘制填充的矩形作为标签文本的背景
        cv2.rectangle(img, (label_x, label_y - label_height), (label_x + label_width, label_y + label_height), color, cv2.FILLED)
 
        # 在图像上绘制标签文本
        cv2.putText(img, label, (label_x, label_y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1, cv2.LINE_AA)
 
    def parse_detections(self, output_data, confidence_threshold=0.5):
        """
        解析检测结果，假设输出格式为:
        [batch_size, num_detections, 6] 其中每个检测包含:
        [x1, y1, x2, y2, confidence, class_id]
        或者根据实际输出格式调整
        """
        detections = []
        # 假设output_data[0]包含检测结果
        for detection in output_data[0][0]:  # 假设batch_size=1
            # 确保我们只取前6个值，以防输出格式不同
            if len(detection) >= 6:
                x1, y1, x2, y2, conf, class_id = detection[:6]
                if conf > confidence_threshold:
                    detections.append({
                        'bbox': [x1, y1, x2, y2],
                        'confidence': float(conf),
                        'class_id': int(class_id)
                    })
        return detections
    
    def visualize_detections(self,image, detections, class_names=None):
        """
        在图像上绘制检测框和类别信息
        """
        # 创建绘图对象
        img = np.array(image.copy())
        img = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)  # PIL使用RGB，OpenCV使用BGR
        
        # 定义颜色列表
        colors = [
            (255, 0, 0),    # 红
            (0, 255, 0),    # 绿
            (0, 0, 255),    # 蓝
            (255, 255, 0),  # 黄
            (255, 0, 255),  # 紫
            (0, 255, 255),  # 青
            (255, 165, 0),  # 橙
            (128, 0, 128),  # 紫罗兰
            (0, 128, 128),  # 橄榄
            (128, 128, 0)   # 茶色
        ]
        
        for det in detections:
            x1, y1, w, h = det['box']
            class_id = det['class_id']
            
            # 转换为整数坐标
            
            # 选择颜色
            color = colors[class_id % len(colors)]
            
            # 绘制边界框
            cv2.rectangle(img, (int(x1), int(y1)), (int(x1 + w), int(y1 + h)), color, 2)
            
            # 准备标签文本
            # class_name = class_names[class_id] if class_names else f"Class {class_id}"
            # label = f"{class_name}: {confidence:.2f}"
            
            # 计算文本大小
            # (text_width, text_height), _ = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)
            
            # 绘制文本背景
            # cv2.rectangle(img, (x1, y1 - text_height - 5), (x1 + text_width, y1), color, -1)
            # 
            # 绘制文本
            # cv2.putText(img, label, (x1, y1 - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # 转换回RGB格式显示
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        

        
        return img
    
    def __del__(self):
        # 清理资源
        del self.context
        del self.engine
import os
os.environ["CUDA_VISIBLE_DEVICES"] = "1" 
# 使用示例
if __name__ == "__main__":
    # 假设的类别名称 - 替换为您的实际类别
    CLASS_NAMES = [
        "Sedan", "car", "Truck", "Van", "Sports Car", 
        "Electric Car", "Hatchback", "Convertible", "Minivan", "Coupe"
    ]
    import time
    # 初始化推理器
    trt_infer = TensorRTInfer("/home/<USER>/project/modelManger/models/car/model.engine")
    image = cv.imread("/home/<USER>/project/modelManger/work/微信图片_20250616101011.png")
    # for i in range(10):
        # 预处理输入图像
    input_shape = trt_infer.inputs[0]['shape']  # 通常为 (1, 3, 224, 224)
    start = time.time()
    input_data = trt_infer.preprocess(image, input_shape)
    end = time.time()
    print("预处理时间：", end - start)
    # 执行推理
    start = time.time()
    outputs = trt_infer.infer(input_data)
    end = time.time()
    print("推理时间：", end - start)
    
    start = time.time()
    out = trt_infer.postprocess( outputs)
    end = time.time()
    print("后处理推理时间：", end - start)
    ima=trt_infer.visualize_detections(image,out)
    cv2.imwrite("out.jpg",ima)
  