import cv2
from tensorrt_yolo.infer import InferOption, DetectModel, generate_labels, visualize

def main():
    # -------------------- 初始化配置 --------------------
    # 配置推理设置
    option = InferOption()
    option.enable_swap_rb()  # 将OpenCV默认的BGR格式转为RGB格式
    # 特殊模型配置示例（如PP-YOLOE系列需取消下方注释）
    # option.set_normalize_params([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])

    # -------------------- 模型初始化 --------------------
    # 加载TensorRT引擎文件（注意检查文件路径）
    # 提示：首次加载引擎可能需要较长时间进行优化
    model = DetectModel(engine_file="/home/<USER>/project/modelManger/models/car/model.engine",
                      option=option)

    # -------------------- 数据预处理 --------------------
    # 加载测试图片（建议添加文件存在性检查）
    input_img = cv2.imread("/home/<USER>/project/modelManger/微信图片_20250616101011.png")
    if input_img is None:
        raise FileNotFoundError("测试图片加载失败，请检查文件路径")

    # -------------------- 执行推理 --------------------
    # 执行目标检测（返回结果包含边界框、置信度、类别信息）
    detection_result = model.predict(input_img)
    print(f"==> detection_result: {detection_result}")

    # -------------------- 结果可视化 --------------------
    # 加载类别标签（需确保labels.txt与模型匹配）
    class_labels = generate_labels(labels_file="labels.txt")
    # 生成可视化结果
    visualized_img = visualize(
        image=input_img,
        result=detection_result,
        labels=class_labels,
    )
    cv2.imwrite("vis_image.jpg", visualized_img)

    # -------------------- 模型克隆演示 --------------------
    # 克隆模型实例（适用于多线程场景）
    cloned_model = model.clone()  # 创建独立副本，避免资源竞争
    # 验证克隆模型推理一致性
    cloned_result = cloned_model.predict(input_img)
    print(f"==> cloned_result: {cloned_result}")

if __name__ == "__main__":
    main()