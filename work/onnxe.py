import time
import cv2
import cv2 as cv
import numpy as np
import onnxruntime
import os
from threading import Lock
import os
from PIL import Image
import numpy as np
from pathlib import Path

from typing import Union, Any, Dict, List, Tuple
os.environ["CUDA_VISIBLE_DEVICES"] = "1" 
class OnnxModelRuntime:
    def __init__(self, path,num_workers=1):
        # Initialize model
        self.num_workers=num_workers
        self.initialize_model(path)
    def preprocess(self, image_input: Union[str, Image.Image, np.ndarray]) -> np.ndarray:
        """预处理图像数据以适应模型输入要求"""
        # 如果输入是文件路径，加载图像
        if isinstance(image_input, str):
            image = cv.imread(image_input)
        elif isinstance(image_input, Path):
            image = cv.imread(str(image_input))
        elif isinstance(image_input, Image.Image):
            image =  np.array(image_input)  
           
        else:
            image = image_input
        self.img_height, self.img_width = image.shape[:2]
                # 将图像颜色空间从 BGR 转换为 RGB
        img_rgb = cv.cvtColor(image, cv.COLOR_BGR2RGB)
 
        # 保持宽高比，进行 letterbox 填充, 使用模型要求的输入尺寸
        img, self.ratio, (self.dw, self.dh) = self.letterbox(img_rgb, new_shape=(self.input_width, self.input_height))
        return img
    

    def letterbox(self, img, new_shape=(640, 640), color=(114, 114, 114), auto=False, scaleFill=False, scaleup=True):
        """
        将图像进行 letterbox 填充，保持纵横比不变，并缩放到指定尺寸。
        """
        shape = img.shape[:2]  # 当前图像的宽高
        print(f"Original image shape: {shape}")
 
        if isinstance(new_shape, int):
            new_shape = (new_shape, new_shape)
 
        # 计算缩放比例
        r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])  # 选择宽高中最小的缩放比
        if not scaleup:  # 仅缩小，不放大
            r = min(r, 1.0)
 
        # 缩放后的未填充尺寸
        new_unpad = (int(round(shape[1] * r)), int(round(shape[0] * r)))
 
        # 计算需要的填充
        dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]  # 计算填充的尺寸
        dw /= 2  # padding 均分
        dh /= 2
 
        # 缩放图像
        if shape[::-1] != new_unpad:  # 如果当前图像尺寸不等于 new_unpad，则缩放
            img = cv.resize(img, new_unpad, interpolation=cv.INTER_LINEAR)
 
        # 为图像添加边框以达到目标尺寸
        top, bottom = int(round(dh)), int(round(dh))
        left, right = int(round(dw)), int(round(dw))
        img = cv.copyMakeBorder(img, top, bottom, left, right, cv.BORDER_CONSTANT, value=color)
        print(f"Final letterboxed image shape: {img.shape}")
 
        return img, (r, r), (dw, dh)


    def predict(self, image):
        return self.detect_objects(image)

    def initialize_model(self, path):
        sess_options = onnxruntime.SessionOptions()
        print(onnxruntime.get_available_providers())
        self.sessions =[{'session':onnxruntime.InferenceSession(path,sess_options=sess_options,providers=['CUDAExecutionProvider']),'lock':Lock()} for _ in range(self.num_workers)]
        # Get model info
        self.session = self.sessions[0]['session']
        self.get_input_details()
        self.get_output_details()

    def detect_objects(self, image):
        for i in range(len(self.sessions)):
            # Try to acquire the lock (non-blocking)
            if self.sessions[i]['lock'].acquire(blocking=False):
                try:
                    # Prepare input tensor
                    input_tensor = self.prepare_input(image)
                    # Perform inference
                    outputs =  outputs = self.inference(self.sessions[i]['session'],input_tensor)
                    return outputs
                finally:
                    # Ensure the lock is always released
                    self.sessions[i]['lock'].release()
        # Perform inference on the image
        with self.sessions[0]['lock']:
            input_tensor = self.prepare_input(image)
            outputs = self.inference(self.sessions[0]['session'],input_tensor)
            return outputs
    def prepare_input(self, img):
        """
        对输入图像进行预处理，以便进行推理。
        参数：
            img: 输入图像数据（BGR格式）
        返回：
            image_data: 经过预处理的图像数据，准备进行推理。
        """
        # 获取输入图像的高度和宽度
        # 通过除以 255.0 来归一化图像数据
        image_data = np.array(img) / 255.0
 
        # 将图像的通道维度移到第一维
        image_data = np.transpose(image_data, (2, 0, 1))  # 通道优先
 
        # 扩展图像数据的维度，以匹配模型输入的形状
        image_data = np.expand_dims(image_data, axis=0).astype(np.float32)
 
        # 返回预处理后的图像数据
        return image_data
 
 

 
 
    def inference(self,session, input_tensor):
        start=time.time()
        outputs = session.run(self.output_names, {self.input_names[0]: input_tensor})
        end=time.time()
        print(f"运行耗时: {end-start:.4f} 秒")
        return outputs

    def get_input_details(self):
        model_inputs = self.session.get_inputs()
        self.input_names = [model_inputs[i].name for i in range(len(model_inputs))]

        self.input_shape = model_inputs[0].shape
        self.input_height = self.input_shape[2]
        self.input_width = self.input_shape[3]


    def get_output_details(self):
        model_outputs = self.session.get_outputs()
        self.output_names = [model_outputs[i].name for i in range(len(model_outputs))]


if __name__ == "__main__":
    model_path = "/home/<USER>/project/modelManger/models/car/car_type_v5_p2.onnx"
    model = OnnxModelRuntime(model_path)
    out=model.predict( model.preprocess(cv2.imread('/home/<USER>/project/modelManger/微信图片_20250616101011.png')))
    print(out)