import tensorrt as trt
import pycuda.driver as cuda
import pycuda.autoinit
import numpy as np
import matplotlib.pyplot as plt

class TensorRTInfer:
    def __init__(self, engine_path):
        # 初始化TensorRT
        self.logger = trt.Logger(trt.Logger.WARNING)
        self.engine = self.load_engine(engine_path)
        self.context = self.engine.create_execution_context()
        self.inputs, self.outputs, self.bindings, self.stream = self.allocate_buffers()
        self.confidence_thres = 0.25
        self.iou_thres = 0.3
        
    def load_engine(self, engine_path):
        with open(engine_path, "rb") as f, trt.Runtime(self.logger) as runtime:
            return runtime.deserialize_cuda_engine(f.read())
   
    def allocate_buffers(self):
        inputs = []
        outputs = []
        bindings = []
        
        for i in range(self.engine.num_io_tensors):
            tensor_name = self.engine.get_tensor_name(i)
            tensor_shape = self.engine.get_tensor_shape(tensor_name)
            dtype = self.engine.get_tensor_dtype(tensor_name)
            size = trt.volume(tensor_shape) * dtype.itemsize
            device_mem = cuda.mem_alloc(size)
            bindings.append(int(device_mem))
            
            if self.engine.get_tensor_mode(tensor_name) == trt.TensorIOMode.INPUT:
                inputs.append({
                    'name': tensor_name,
                    'memory': device_mem,
                    'shape': tensor_shape,
                    'dtype': dtype
                })
                self.context.set_tensor_address(tensor_name, device_mem)
            else:
                outputs.append({
                    'name': tensor_name,
                    'memory': device_mem,
                    'shape': tensor_shape,
                    'dtype': dtype
                })
                self.context.set_tensor_address(tensor_name, device_mem)
        
        return inputs, outputs, bindings, cuda.Stream()
    

    def __del__(self):
        # 清理资源
        del self.context
        del self.engine
import os
os.environ["CUDA_VISIBLE_DEVICES"] = "1" 
# 使用示例
if __name__ == "__main__":
    trt_infer = TensorRTInfer("/home/<USER>/project/modelManger/models/car/model.engine")
  