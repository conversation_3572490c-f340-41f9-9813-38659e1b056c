# from predict_method import ObjectDetector
# from config import ModelConfig
import cv2
# model_config =ModelConfig('/home/<USER>/project/modelManger/models/car_o.pki')
# model = ObjectDetector(model_config)
img = cv2.imread('/home/<USER>/project/modelManger/微信图片_20250616101011.png')
# out=model.predict_pipeline(img)
# im=model.visualize(img,out)
# cv2.imwrite('out.png',im)
import os
from ultralytics import YOLO
# from ultralytics import YOLO
model = YOLO("/home/<USER>/project/modelManger/models/car/car_type_v5_p2.pt")
model.export(format='engine', half=True, dynamic=True,batch=0)





# os.environ["CUDA_VISIBLE_DEVICES"] = "3" 

# model = YOLO("/home/<USER>/project/modelManger/models/car/car_type_v5_p2.engine")
# for i in range(1000):
#     results = model.predict(source=img,half=True)

    