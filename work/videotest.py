import cv2
import subprocess
import numpy as np

RTMP_URL = "rtmp://192.168.171.53:1935/live/stream5"
VIDEO_PATH = "rtmp://192.168.171.53:1935/live/stream1"

def push_stream():
    cap = cv2.VideoCapture(VIDEO_PATH)
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

    # 关键修改：添加 `-re` 并调整参数顺序
    ffmpeg_cmd = [
        "ffmpeg",
        "-re",                   # 按原始速率读取输入
        "-y",                    # 覆盖输出文件
        "-f", "rawvideo",         # 输入格式
        "-vcodec", "rawvideo",    # 输入编码
        "-pix_fmt", "bgr24",      # OpenCV 的像素格式
        "-s", f"{width}x{height}",
        "-r", str(fps),          # 输入帧率（必须与视频一致）
        "-i", "-",                # 从标准输入读取
        "-c:v", "libx264",        # 重新编码为 H.264
        "-pix_fmt", "yuv420p",    # RTMP 兼容格式
        "-preset", "ultrafast",   # 低延迟编码
        "-f", "flv",              # 输出格式
        RTMP_URL
    ]
    ffmpeg_process = subprocess.Popen(ffmpeg_cmd, stdin=subprocess.PIPE)
    import time
    while cap.isOpened():
        start =time.time()
        ret, frame = cap.read()
        end = time.time()
        print("读流时间：", end - start)
        if not ret:
            break

        # 在此处处理帧（示例：转换为灰度）
        # processed_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        # processed_frame = cv2.cvtColor(processed_frame, cv2.COLOR_GRAY2BGR)  # 保持 3 通道

        # 推送到 FFmpeg
        ffmpeg_process.stdin.write(frame.tobytes())
       
        

    cap.release()
    ffmpeg_process.stdin.close()
    ffmpeg_process.wait()

if __name__ == "__main__":
    push_stream()