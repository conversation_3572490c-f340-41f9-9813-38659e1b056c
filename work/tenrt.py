import cv2
from tensorrt_yolo.infer import InferOption, DetectModel, generate_labels, visualize
import os
import time
import json
os.environ["CUDA_DEVICE_ORDER"]="PCI_BUS_ID"
os.environ["CUDA_VISIBLE_DEVICES"] = "1" 

# 配置推理选项
option = InferOption()
option.enable_swap_rb()

#trtexec --onnx=/home/<USER>/project/modelManger/output/car_type_v5_p2.onnx --saveEngine=/home/<USER>/project/modelManger/output/car_type_v5_p2.engine --minShapes=images:1x3x640x640 --optShapes=images:4x3x640x640 --maxShapes=images:8x3x640x640 --fp16
# 初始化模型
model = DetectModel("/home/<USER>/project/modelManger/people_v8.engine", option)

#model = DetectModel("/home/<USER>/project/modelManger/output/car_type_v5_p2.engine", option)




from ultralytics import YOLO
#model =YOLO('/home/<USER>/project/modelManger/models/car/car_type_v5_p2.engine')
# 加载图片
im = cv2.imread("/home/<USER>/project/modelManger/work/微信图片_20250616101011.png")

# 模型预测
result = model.predict(im)
json_result = json.dumps(result)

print(json_result)
# 进行多次推理并计算平均耗时
total_time = 0.0
num_iterations = 100

for i in range(num_iterations):
    start = time.time()
    clone_result = model.predict(im)
    end = time.time()
    iteration_time = end - start
    total_time += iteration_time
    print(f"第 {i+1} 次推理时间：{iteration_time:.4f} 秒")

average_time = total_time / num_iterations
print(f"\n==> 平均推理时间：{average_time:.4f} 秒")
