[TOC]

### ChangeLog
2024-12-09：by 彭雄友
* 新建文档



### 1. 结构体说明

该服务提供了人脸、全目标 2 种类型的结构化特征提取服务，并将不同类型目标之间的关联关系进行梳理。这里，全目标包括人脸、人体、机动车、非机动车 4 种目标。“人脸 + 人体”构成的联合体称为“人”（Person），“人 + 机动车”构成的联合体称为“Vehinion”，“人 + 非机动车”构成的联合体称为“NonVehinion”。

#### 1.1 Box 结构体

| 参数名 | 类型  | 描述                 | 必须 |
| ------ | ----- | -------------------- | ---- |
| score  | float | 标记框置信度，[0, 1] | 是   |
| x1     | float | 标记框左上角 x 坐标  | 是   |
| x2     | float | 标记框右下角 x 坐标  | 是   |
| y1     | float | 标记框左上角 y 坐标  | 是   |
| y2     | float | 标记框右下角 y 坐标  | 是   |



#### 1.2 Person 结构体：(人 = 人脸 + 人体)

| 参数名        | 类型                   | 描述           | 必须 |
| ------------- | ---------------------- | -------------- | ---- |
| **face**      | Face Object            | 人脸结构化信息 | 否   |
| **body**      | Body Object            | 人体结构化信息 | 否   |
| **attribute** | PersonAttribute Object | 人体属性信息   | 否   |

##### Face 结构体

| 参数名    | 类型       | 描述                 | 必须 |
| --------- | ---------- | -------------------- | ---- |
| box       | Box Object | 人脸框               | 是   |
| feature   | String     | 人脸特征base64字符串 | 是   |
| quality   | float      | 人脸质量，[0, 100]   | 否   |
| yaw       | float      | 偏航角度             | 否   |
| pitch     | float      | 俯仰角度             | 否   |
| pupil_dis | float      | 瞳距，单位：像素     | 否   |

##### Body 结构体

| 参数名  | 类型       | 描述                 | 必须 |
| ------- | ---------- | -------------------- | ---- |
| box     | Box Object | 人体框               | 是   |
| feature | String     | 人体特征base64字符串 | 是   |

##### PersonAttribute  结构体

| 参数名         | 类型 | 描述                                                    | 必须 |
| -------------- | ---- | ------------------------------------------------------- | ---- |
| age            | int  | 年龄段，1: 小孩，2: 青年，3: 老人                       | 否   |
| gender         | int  | 性别， 1: 男性，2: 女性，3: 未知性别                    | 否   |
| eyeglass       | bool | 是否戴眼镜，0: 未戴眼镜，1:戴眼镜                       | 否   |
| hat            | bool | 是否戴帽子， 0: 未戴帽子，1:戴帽子                      | 否   |
| upper_clothing | int  | 上身服饰，1: 长袖，2: 短袖，3: 无袖（赤膊）             | 否   |
| lower_clothing | int  | 下身服饰 ，1: 短裤，2: 裙子，3: 长裤，4: 长大衣         | 否   |
| shoe_style     | int  | 鞋子款式，1: 皮鞋，2: 运动鞋/休闲鞋，3: 凉鞋，4: 其他鞋 | 否   |

#### 1.3 Vehinion 结构体：（机动车 + 人）

| 参数名      | 类型           | 描述             | 必须 |
| ----------- | -------------- | ---------------- | ---- |
| **vehicle** | Vehicle Object | 机动车结构化信息 | 是   |
| driver      | Person         | 司机             | 否   |
| passengers  | Array[Person]  | 乘客             | 否   |

##### Vehicle 结构体

| 参数名        | 类型                    | 描述                   | 必须 |
| ------------- | ----------------------- | ---------------------- | ---- |
| box           | Box Object              | 机动车框               | 是   |
| feature       | String                  | 机动车特征base64字符串 | 是   |
| **attribute** | VehicleAttribute Object | 机动车属性信息         | 是   |
| **plate**     | Plate Object            | 机动车车牌信息         | 否   |

##### VehicleAttribute 结构体

| 参数名        | 类型 | 描述                                                         | 必须 |
| ------------- | ---- | ------------------------------------------------------------ | ---- |
| specialty     | int  | 特种车辆，1: 普通车、2: 消防车、3: 救护车、4: 工程车、5: 抢险车、6: 洒水车、7: 搅拌车、8: 油罐车、9: 渣土车、10: 邮政车、11: 校车、12: 警车、13: 其它 | 否   |
| body_covering | int  | 车身遮挡，1: 无遮挡，2: 半遮挡，3: 全遮挡                    | 否   |
| cargo_on_roof | int  | 车顶载货，1: 否、2: 是，3: 其它                              | 否   |
| unlicensed    | bool | 是否为无牌车辆，0: 否、1: 是                                 | 否   |
| dangerous     | bool | 是否为危险车辆，0: 否，1:是                                  | 否   |
| plate_visible | bool | 车牌是否可见，0: 否，1:是                                    | 否   |
| color         | int  | 车辆颜色，1: 白色、2: 黑色、3: 银色、4: 灰色、5: 红色、6: 蓝色、7: 橙色、8: 黄色、9: 绿色、10: 粉色、11: 紫色、12: 棕色、13: 其它 | 否   |
| type          | int  | 车辆类型，1: 轿车、2: 越野车、3: 面包车、4: 掀背车、5: 商务车、6: 皮卡、7: 大巴、8: 卡车、9: 旅行车、10: 其它 | 否   |
| brand         | int  | 车辆品牌，1: 大众、2: 丰田、3: 本田、4: 五菱、5: 日产、6: 东风、7: 通用、8: 江淮、9: 吉利、10: 长城、11: 比亚迪、12: 合众、13: 北汽、14: 上汽、15: 广汽、16: 一汽、17: 长安、18: 奔驰、19: 宝马、20: 奥迪、21: 福特、22: 雪佛兰、23: 沃尔沃、24: 马自达、25: 凯迪拉克、26: 红旗、哈弗、现代、英菲尼迪、雪铁龙、启辰、标致、江铃、铃木、领克、别克、五十铃、三菱、欧拉、起亚、特斯拉、极氪、小鹏、理想、蔚来、零跑、赛力斯、埃安、哪吒、深蓝、路虎、捷豹、保时捷、雷克塞斯、法拉利、劳斯莱斯、宾利、玛莎拉蒂、帝豪、兰博基尼、奇瑞、宝骏、大运、中国重汽、陆风、汉腾、海格、宇通、金龙、70: 其它、71: 福田 | 否   |
| orientation   | int  | 车辆朝向，1: 向左、2: 向右、3: 向上、4: 向下、5: 左上、6: 左下、7: 右上、8: 右下 | 否   |

##### Plate 结构体

| 参数名  | 类型       | 描述                                                      | 必须 |
| ------- | ---------- | --------------------------------------------------------- | ---- |
| box     | Box Object | 车牌标记框                                                | 是   |
| license | String     | 车牌号                                                    | 是   |
| color   | int        | 车牌颜色，1: 黑色， 2: 蓝色， 3: 绿色， 4: 白色， 5: 黄色 | 是   |



#### 1.4 NonVehinion 结构体 （非机动车 + 人）

| 参数名         | 类型              | 描述               | 必须 |
| -------------- | ----------------- | ------------------ | ---- |
| **nonvehicle** | NonVehicle Object | 非机动车结构化信息 | 是   |
| driver         | Person            | 司机               | 否   |
| passengers     | Array[Person]     | 乘客               | 否   |

##### NonVehicle 结构体

| 参数名  | 类型       | 描述                     | 必须 |
| ------- | ---------- | ------------------------ | ---- |
| box     | Box Object | 非机动车框               | 是   |
| feature | String     | 非机动车特征base64字符串 | 是   |

### 2. 接口定义

#### 2.1 文件任务结构化接口

##### 2.1.1 图片文件结构化

- 接口功能：上传一张图片，返回图片结构化信息
- 接口名称：`/structure/v1/image`
- 请求类型：`POST`
- 请求参数：

| 参数名     | 类型   | 描述                                                         | 必须 |
| ---------- | ------ | ------------------------------------------------------------ | ---- |
| id         | String | 任务ID（由外部传入）                                         | 是   |
| url        | String | 通过schema://host:port/path来指定数据源地址。<br /> 支持的schema类型包括：http  （url与base64二选一） | 否   |
| base64     | String | 图片的base64字符串；（url与base64二选一）                    | 否   |
| type       | Int    | 结构化任务类别，1：人脸，2：全目标                           | 是   |
| min_width  | Int    | 目标框最小宽度，单位：像素                                   | 否   |
| min_height | Int    | 目标框最小高度，单位：像素                                   | 否   |

* 请求示例：

```json
{
  "id": "task-id",
  "url": "http://host:port/path/xxx.jpg",
  "type": 2,
  "min_width":50,
  "min_height":50
}
```

- 返回字段：

| 参数名   | 类型   | 必须       | 描述 |
| -------- | ------ | ---------- | ---- |
| msgcode  | number | 返回状态码 | 是   |
| msg      | string | 返回信息   | 是   |
| cost     | number | 耗时：毫秒 | 是   |
| **task** | Object | 任务信息   | 否   |
| **data** | Object | 结构化数据 | 否   |

**task 字段说明**

| 参数名     | 类型   | 描述                                                         | 必须 |
| ---------- | ------ | ------------------------------------------------------------ | ---- |
| id         | String | 任务ID                                                       | 是   |
| url        | String | 通过schema://host:port/path来指定数据源地址。<br /> 支持的schema类型包括：http  （url与base64二选一） | 否   |
| base64     | String | 图片的base64字符串；（url与base64二选一）                    | 否   |
| type       | Int    | 结构化任务类别，1：人脸，2：全目标                           | 是   |
| min_width  | Int    | 目标框最小宽度，单位：像素                                   | 否   |
| min_height | Int    | 目标框最小高度，单位：像素                                   | 否   |

**data 字段说明**

| 参数名           | 类型               | 描述                      | 必须 |
| ---------------- | ------------------ | ------------------------- | ---- |
| person_list      | Array[Person]      | Person结构化信息数组      | 否   |
| vehinion_list    | Array[Vehinion]    | Vehinion结构化信息数组    | 否   |
| nonvehinion_list | Array[NonVehinion] | NonVehinion结构化信息数组 | 否   |

* 返回示例:

```json
{
  "msgcode":200,
  "msg":"Success",
  "cost":66,
  "task":
  {
    "id": "task-name",
    "url": "*****************************:port/path/xxx.jpg",
    "type": 2,
    "min_width":50,
    "min_height":50
  },
  "data":
  {
    "person_list":
    [
      {
        "face":
        {
          "box":{"score":0.888, "x1":123.0, "x2":666.0, "y1":123.0, "y2":266.0},
          "feature":"face 256 float feature base64 string"
        },
        "body":
        {
          "box":{"score":0.888, "x1":123.0, "x2":666.0, "y1":123.0, "y2":966.0},
          "feature":"body 768 float feature base64 string"
        },
        "attribute": {"key":value}
      }
    ],
    "vehinion_list": [],
    "nonvehinion_list":[]
  }
}
```

- 状态码

| 状态码 | 描述                                 |
| ------ | ------------------------------------ |
| 200    | 请求成功，返回正确的数据             |
| 400    | 参数错误，可能缺少参数或者类型不正确 |
| 500    | 系统内部错误                         |
| 503    | 结构化资源不足                       |

#### 2.2 流式任务结构化接口

##### 2.2.1 创建视频流结构化任务

- 接口功能：创建一个视频流结构化任务
- 接口名称：`/structure/v1/task`
- 请求类型：`POST`
- 请求参数：

| 参数名     | 类型                | 描述                                                         | 必须 |
| ---------- | ------------------- | ------------------------------------------------------------ | ---- |
| id         | String              | 任务ID（由外部传入）                                         | 是   |
| name       | String              | 任务名称                                                     | 否   |
| url        | String              | 通过schema://username:password@host:port/path?args来指定数据源地址。<br/>支持的schema类型包括：rtsp、file<br />数据源形式：实时视频流、历史视频流 | 是   |
| type       | Int                 | 结构化任务类别，1：人脸，2：全目标                           | 是   |
| interval   | float               | 结构化间隔，单位: 秒<br />若大于 0，每隔若干秒抽帧检测；<br />若小于 0，进行 I 帧检测；<br />若等于 0，根据系统默认配置进行检测，默认配置 2s | 否   |
| min_width  | Int                 | 目标框最小宽度，单位：像素                                   | 否   |
| min_height | Int                 | 目标框最小高度，单位：像素                                   | 否   |
| roi        | Array[Array[Point]] | 感兴趣区域，一组多边形区域。默认值：全图范围<br />多边形区域：用顶点集合表示，比如 [[0.1, 0.2], [0.2, 0.3], [0.3, 0.1]]被用来表示一个三角形区域<br />顶点：[x, y]，分别为顶点的 x、y 坐标。<br />坐标系说明：图片左上角为原点，向右为 x 正向，向下为 y 正向 | 否   |
| path       | String              | 结构化数据上报地址                                           | 是   |


* 请求示例：

```json
{
  "id": "task-id",
  "name":"task-name",
  "url": "rtsp://192.168.2.88:8888/live/test",
  "type": 2,
  "interval":-1,
  "min_width":50,
  "min_height":50,
  "roi":
  [
    [[0.1, 0.2], [0.2, 0.3], [0.3, 0.1]],  // 一个三角形区域
    [[66.0, 11.0], [66.0, 88.0], [99.0, 88.0], [99.0, 11.0]] // 一个四边形区域
  ],
  "path":"http://structure/info/upload/addr"
}
```

- 返回字段：

| 参数名  | 类型   | 必须       | 描述 |
| ------- | ------ | ---------- | ---- |
| msgcode | number | 返回状态码 | 是   |
| msg     | string | 返回信息   | 是   |
| cost    | number | 耗时：毫秒 | 是   |

- 返回示例：

```json
{
  "msgcode": 200,
  "msg": "Success",
  "cost":8.88
}
```

- 状态码

| 状态码 | 描述                                 |
| ------ | ------------------------------------ |
| 200    | 请求成功，返回正确的数据             |
| 400    | 参数错误，可能缺少参数或者类型不正确 |
| 500    | 系统内部错误                         |
| 503    | 结构化资源不足                       |



结构化数据上报，需要全目标结构化引擎服务提供接口，上传的结构化数据格式如下：

| 参数名  | 类型          | 描述                 | 必须 |
| ------- | ------------- | -------------------- | ---- |
| img     | String        | 图片二进制数据       | 是   |
| message | Object String | 结构化数据json字符串 | 是   |

message 字段说明如下：

| 参数名                | 类型               | 描述                                                         | 必须 |
| --------------------- | ------------------ | ------------------------------------------------------------ | ---- |
| id                    | String             | 任务ID                                                       | 是   |
| name                  | String             | 任务名称                                                     | 否   |
| url                   | String             | 通过schema://username:password@host:port/path?args来指定数据源地址。<br/>支持的schema类型包括：rtsp、file<br />数据源形式：实时视频流、历史视频流 | 是   |
| type                  | int                | 结构化任务类型                                               | 是   |
| timestamp             | Int64              | 结构化时间，单位：秒                                         | 是   |
| data                  | Object             | 结构化数据                                                   | 是   |
| data@person_list      | Array[Person]      | Person结构化信息数组                                         | 是   |
| data@vehinion_list    | Array[Vehinion]    | Vehinion结构化信息数组                                       | 是   |
| data@nonvehinion_list | Array[NonVehinion] | NonVehinion结构化信息数组                                    | 是   |

```
curl --location --request POST 'http://*************:9624/manyi/v1/video/tasks/upload_image' \
--form 'img="tupianerjinzhishujuliu"' \
--form 'data="jiegouhuashujujsonzifuchuan"'
```

```json
{
  "id": "task-id",
  "name":"task-name",
  "url": "http://host:port/path/xxx.jpg",
  "type": 2,
  "timestampe":1734344324,
  "min_width":50,
  "min_height":50,
  "data":
  {
    "person_list":
    [
      {
        "face":
        {
          "box":{"score":0.888, "x1":123.0, "x2":666.0, "y1":123.0, "y2":266.0},
          "feature":"face 256 float feature base64 string"
        },
        "body":
        {
          "box":{"score":0.888, "x1":123.0, "x2":666.0, "y1":123.0, "y2":966.0},
          "feature":"body 768 float feature base64 string"
        },
        "attribute": {"key":value}
      }
    ],
    "vehinion_list": [],
    "nonvehinion_list":[]
  }
}
```

##### 2.2.2 修改结构化任务（删除）

- 接口功能：修改某个视频流结构化任务
- 接口名称：`/structure/v1/task`
- 请求类型：`PUT`
- 请求参数：同 2.3.1

##### 2.2.3 暂停/开启结构化任务

- 接口功能：暂停/开启某个视频流结构化任务
- 接口名称：`/structure/v1/task/switch`
- 请求类型：`POST`
- 请求参数：

| 参数名 | 类型   | 必须                          | 描述 |
| ------ | ------ | ----------------------------- | ---- |
| id     | string | 任务ID                        | 是   |
| sw     | bool   | 开关，true: 开启，false: 关闭 | 是   |


- 请求示例：

```shell
{
	"id":"task-id",
	"sw": false
}
```

- 返回字段：

| 参数名  | 类型   | 必须       | 描述 |
| ------- | ------ | ---------- | ---- |
| msgcode | number | 返回状态码 | 是   |
| msg     | string | 返回信息   | 是   |
| cost    | number | 耗时：毫秒 | 是   |

- 返回示例：

```json
{
  "msgcode": 200,
  "msg": "Success",
  "cost":8.88
}
```

- 状态码

| 状态码 | 描述                                 |
| ------ | ------------------------------------ |
| 200    | 请求成功，返回正确的数据             |
| 400    | 参数错误，可能缺少参数或者类型不正确 |
| 500    | 系统内部错误                         |
| 503    | 结构化资源不足                       |

##### 2.2.4 删除结构化任务

- 接口功能：移除某个视频流结构化任务
- 接口名称：`/structure/v1/task?id=xxx`
- 请求类型：`DELETE`
- 请求包体：无
- 请求示例：

```shell
curl --location --request DELETE 'http://192.168.2.88:8888/structure/v1/task?id=xxx' \
```

- 返回字段：

| 参数名  | 类型   | 必须       | 描述 |
| ------- | ------ | ---------- | ---- |
| msgcode | number | 返回状态码 | 是   |
| msg     | string | 返回信息   | 是   |
| cost    | number | 耗时：毫秒 | 是   |

- 返回示例：

```json
{
  "msgcode": 200,
  "msg": "Success",
  "cost":8.88
}
```

- 状态码

| 状态码 | 描述                                 |
| ------ | ------------------------------------ |
| 200    | 请求成功，返回正确的数据             |
| 400    | 参数错误，可能缺少参数或者类型不正确 |
| 500    | 系统内部错误                         |
| 503    | 结构化资源不足                       |

##### 2.2.5 获取任务列表
- 接口功能：获取视频流结构化任务列表
- 接口名称：`/structure/v1/tasklist`
- 请求类型：`GET`
- 返回字段：

| 参数名    | 类型                | 必须         | 描述 |
| --------- | ------------------- | ------------ | ---- |
| msgcode   | number              | 返回状态码   | 是   |
| msg       | string              | 返回信息     | 是   |
| cost      | number              | 耗时：毫秒   | 是   |
| count     | int                 | 任务总数     | 是   |
| task_list | Array[**TaskInfo**] | 任务信息列表 | 是   |

**TaskInfo 结构体：**

| 参数名      | 类型   | 必须                                                         | 描述 |
| ----------- | ------ | ------------------------------------------------------------ | ---- |
| id          | String | 任务ID                                                       | 是   |
| name        | String | 任务名称                                                     | 否   |
| status      | Int    | 任务状态，0: 分配资源中，1: 解析中，2: 暂停，3: 等待， 4: 重试 | 是   |
| type        | Int    | 结构化任务类别，1：人脸，2：全目标                           | 是   |
| latest_time | Int64  | 最近解析时间                                                 | 否   |
| creat_time  | Int64  | 任务创建时间                                                 | 是   |

- 返回示例：

```json
{
  "msgcode": 200,
  "msg": "Success",
  "cost":8.88,
  "count":1,
  "task_list":
  [
    {
      "id": "task-id",
      "name":"task-name",
      "statue": 3,
      "type": 2,
      "latest_time":**********,
      "creat_time":**********
    }
  ]
}
```

- 状态码

| 状态码 | 描述                                 |
| ------ | ------------------------------------ |
| 200    | 请求成功，返回正确的数据             |
| 400    | 参数错误，可能缺少参数或者类型不正确 |
| 500    | 系统内部错误                         |
| 503    | 结构化资源不足                       |

#### 2.3 监控统计接口

##### 2.3.1 系统状态接口（仅 GPUInfo.licensed 有效）

- 接口功能：返回服务资源占用相关信息
- 接口地址：`/structure/v1/sysinfo`
- 请求方法：`GET`
- 请求参数：`无`
- 返回字段：

| 字段        | 类型               | 描述                 | 必须 |
| ----------- | ------------------ | -------------------- | ---- |
| msgcode     | number             | 返回状态码           | 是   |
| msg         | string             | 返回信息             | 是   |
| cost        | number             | 耗时：毫秒           | 是   |
| host_name   | string             | 服务器名称           | 是   |
| host_ip     | string             | 服务器 IP            | 是   |
| cpu         | float              | CPU 使用率，单位：%  | 是   |
| memory      | float              | 内存使用率，单位：%  | 是   |
| input_band  | float              | 输入带宽，单位：Mbps | 是   |
| output_band | float              | 输出带宽，单位：Mbps | 是   |
| gpu_list    | Array[**GPUInfo**] | GPU占用信息          | 是   |

GPUInfo 结构体：

| 字段     | 类型   | 描述                        | 必须 |
| -------- | ------ | --------------------------- | ---- |
| id       | number | GPU 编号                    | 是   |
| name     | string | GPU 型号                    | 是   |
| uuid     | string | GPU UUID                    | 是   |
| tmem     | float  | GPU总显存，单位：GB         | 是   |
| licensed | bool   | GPU 是否授权，0: 否，1: 是  | 是   |
| power    | float  | 当前 GPU 功耗，单位：瓦     | 是   |
| tempture | float  | 当前 GPU 温度，单位：摄氏度 | 是   |
| mem      | float  | 当前显存使用率，单位：%     | 是   |
| gpu      | float  | 当前 GPU 使用率，单位：%    | 是   |

* 返回示例

```json
{  
  "msgcode":200,  
  "msg":"Success",  
  "cost":8.88,
  "cpu":66.6,  
  "memory":22.2,  
  "gpu_list":
  [
    {
      "id":0,
      "name":"NVIDIA GeForce GTX 1080 Ti",
      "uuid":"GPU-e70af051-c34e-7b7b-1b65-4e59810ba6fe",
      "tmem": 24,
      "licensed":1,
      "power":80,
      "tempture":24,
      "mem": 33.3,
      "gpu":88.8
    }
  ]
}
```