#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片结构化识别脚本
使用结构化接口识别图片中的人脸、人体、机动车、非机动车等目标
"""

import base64
import json
import requests
import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import os
import sys

def image_to_base64(image_path):
    """将图片转换为base64字符串"""
    try:
        with open(image_path, 'rb') as image_file:
            encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
        return encoded_string
    except Exception as e:
        print(f"图片转换base64失败: {e}")
        return None

def call_structure_api(image_path, api_url="http://58.250.54.56:9628/structure/v1/image"):
    """调用结构化接口"""
    # 将图片转换为base64
    base64_image = image_to_base64(image_path)
    if not base64_image:
        return None
    
    # 构建请求参数
    payload = {
        "id": "task-" + os.path.basename(image_path),
        "base64": base64_image,
        "type": 2,  # 全目标类型
        "min_width": 50,
        "min_height": 50
    }
    
    headers = {
        'Content-Type': 'application/json'
    }
    
    try:
        print(f"正在调用结构化接口识别图片: {image_path}")
        response = requests.post(api_url, headers=headers, data=json.dumps(payload), timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"接口调用成功，耗时: {result.get('cost', 0)}ms")
            return result
        else:
            print(f"接口调用失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return None
            
    except Exception as e:
        print(f"接口调用异常: {e}")
        return None

def draw_detection_results(image_path, result_data, output_path):
    """在图片上绘制检测结果"""
    try:
        # 读取原图
        image = cv2.imread(image_path)
        if image is None:
            print(f"无法读取图片: {image_path}")
            return False
            
        height, width = image.shape[:2]
        
        # 转换为PIL图像以便绘制中文
        image_pil = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(image_pil)
        
        # 尝试加载中文字体
        try:
            font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 20)
        except:
            font = ImageFont.load_default()
        
        colors = {
            'person': (255, 0, 0),      # 红色 - 人
            'vehicle': (0, 255, 0),     # 绿色 - 机动车
            'nonvehicle': (0, 0, 255),  # 蓝色 - 非机动车
            'face': (255, 255, 0),      # 黄色 - 人脸
            'body': (255, 0, 255),      # 紫色 - 人体
            'plate': (0, 255, 255)      # 青色 - 车牌
        }
        
        detection_count = 0
        
        # 绘制人员检测结果
        if 'person_list' in result_data:
            for i, person in enumerate(result_data['person_list']):
                detection_count += 1
                
                # 绘制人脸框
                if 'face' in person and 'box' in person['face']:
                    box = person['face']['box']
                    x1, y1, x2, y2 = int(box['x1']), int(box['y1']), int(box['x2']), int(box['y2'])
                    draw.rectangle([x1, y1, x2, y2], outline=colors['face'], width=2)
                    draw.text((x1, y1-25), f"Face-{i+1} ({box['score']:.2f})", fill=colors['face'], font=font)
                
                # 绘制人体框
                if 'body' in person and 'box' in person['body']:
                    box = person['body']['box']
                    x1, y1, x2, y2 = int(box['x1']), int(box['y1']), int(box['x2']), int(box['y2'])
                    draw.rectangle([x1, y1, x2, y2], outline=colors['body'], width=2)
                    draw.text((x1, y1-25), f"Body-{i+1} ({box['score']:.2f})", fill=colors['body'], font=font)
        
        # 绘制机动车检测结果
        if 'vehinion_list' in result_data:
            for i, vehinion in enumerate(result_data['vehinion_list']):
                detection_count += 1
                
                if 'vehicle' in vehinion and 'box' in vehinion['vehicle']:
                    box = vehinion['vehicle']['box']
                    x1, y1, x2, y2 = int(box['x1']), int(box['y1']), int(box['x2']), int(box['y2'])
                    draw.rectangle([x1, y1, x2, y2], outline=colors['vehicle'], width=3)
                    draw.text((x1, y1-25), f"Vehicle-{i+1} ({box['score']:.2f})", fill=colors['vehicle'], font=font)
                    
                    # 绘制车牌
                    if 'plate' in vehinion['vehicle'] and 'box' in vehinion['vehicle']['plate']:
                        plate_box = vehinion['vehicle']['plate']['box']
                        px1, py1, px2, py2 = int(plate_box['x1']), int(plate_box['y1']), int(plate_box['x2']), int(plate_box['y2'])
                        draw.rectangle([px1, py1, px2, py2], outline=colors['plate'], width=2)
                        license_text = vehinion['vehicle']['plate'].get('license', 'Unknown')
                        draw.text((px1, py1-25), f"Plate: {license_text}", fill=colors['plate'], font=font)
        
        # 绘制非机动车检测结果
        if 'nonvehinion_list' in result_data:
            for i, nonvehinion in enumerate(result_data['nonvehinion_list']):
                detection_count += 1
                
                if 'nonvehicle' in nonvehinion and 'box' in nonvehinion['nonvehicle']:
                    box = nonvehinion['nonvehicle']['box']
                    x1, y1, x2, y2 = int(box['x1']), int(box['y1']), int(box['x2']), int(box['y2'])
                    draw.rectangle([x1, y1, x2, y2], outline=colors['nonvehicle'], width=3)
                    draw.text((x1, y1-25), f"NonVehicle-{i+1} ({box['score']:.2f})", fill=colors['nonvehicle'], font=font)
        
        # 转换回OpenCV格式并保存
        result_image = cv2.cvtColor(np.array(image_pil), cv2.COLOR_RGB2BGR)
        
        # 在图片顶部添加统计信息
        cv2.putText(result_image, f"Total Detections: {detection_count}", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        
        cv2.imwrite(output_path, result_image)
        print(f"检测结果图片已保存到: {output_path}")
        print(f"共检测到 {detection_count} 个目标")
        return True
        
    except Exception as e:
        print(f"绘制检测结果失败: {e}")
        return False

def print_detection_summary(result_data):
    """打印检测结果摘要"""
    print("\n=== 检测结果摘要 ===")
    
    # 统计人员
    person_count = len(result_data.get('person_list', []))
    if person_count > 0:
        print(f"检测到 {person_count} 个人员:")
        for i, person in enumerate(result_data['person_list']):
            face_info = "有人脸" if 'face' in person else "无人脸"
            body_info = "有人体" if 'body' in person else "无人体"
            print(f"  人员{i+1}: {face_info}, {body_info}")
            
            # 打印属性信息
            if 'attribute' in person:
                attr = person['attribute']
                age_map = {1: "小孩", 2: "青年", 3: "老人"}
                gender_map = {1: "男性", 2: "女性", 3: "未知性别"}
                
                age = age_map.get(attr.get('age'), "未知年龄")
                gender = gender_map.get(attr.get('gender'), "未知性别")
                print(f"    属性: {age}, {gender}")
    
    # 统计机动车
    vehicle_count = len(result_data.get('vehinion_list', []))
    if vehicle_count > 0:
        print(f"检测到 {vehicle_count} 辆机动车:")
        for i, vehinion in enumerate(result_data['vehinion_list']):
            vehicle_info = "有车辆信息" if 'vehicle' in vehinion else "无车辆信息"
            plate_info = ""
            if 'vehicle' in vehinion and 'plate' in vehinion['vehicle']:
                license_plate = vehinion['vehicle']['plate'].get('license', '未知')
                plate_info = f", 车牌: {license_plate}"
            print(f"  机动车{i+1}: {vehicle_info}{plate_info}")
    
    # 统计非机动车
    nonvehicle_count = len(result_data.get('nonvehinion_list', []))
    if nonvehicle_count > 0:
        print(f"检测到 {nonvehicle_count} 辆非机动车")
    
    total_count = person_count + vehicle_count + nonvehicle_count
    print(f"总计检测到 {total_count} 个目标")

def main():
    # 输入图片路径
    image_path = "微信截图_20250708145411.png"
    
    if not os.path.exists(image_path):
        print(f"图片文件不存在: {image_path}")
        return
    
    # 调用结构化接口
    result = call_structure_api(image_path)
    
    if result is None:
        print("结构化识别失败")
        return
    
    # 检查接口返回状态
    if result.get('msgcode') != 200:
        print(f"接口返回错误: {result.get('msg', '未知错误')}")
        return
    
    # 保存完整的JSON结果
    result_json_path = "detection_result.json"
    with open(result_json_path, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    print(f"完整检测结果已保存到: {result_json_path}")
    
    # 获取检测数据
    data = result.get('data', {})
    
    # 打印检测摘要
    print_detection_summary(data)
    
    # 绘制检测结果并保存图片
    output_image_path = "detection_result.jpg"
    if draw_detection_results(image_path, data, output_image_path):
        print(f"\n检测完成！结果图片保存在: {output_image_path}")
    else:
        print("绘制检测结果失败")

if __name__ == "__main__":
    main()
