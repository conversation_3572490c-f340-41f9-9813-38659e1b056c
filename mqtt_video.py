import paho.mqtt.client as mqtt
import json
import cv2
import numpy as np
from datetime import datetime
import threading
import time
from model_manager import ModelManager
from videio_hander import VideoProcessor
modelManager = ModelManager()
a = {'a':modelManager.load_model('/home/<USER>/project/modelManger/models/car_door.pki')}

class MqttProcessor:
    def __init__(self):
        # MQTT 配置
        self.mqtt_broker = "192.168.171.52"
        self.mqtt_port = 1883
        self.subscribe_topic = "streamOption"
        self.publish_topic = "alarmMessage"
        
        # 初始化 MQTT 客户端
        self.client = mqtt.Client(callback_api_version=mqtt.CallbackAPIVersion.VERSION1)
        self.client.on_connect = self.on_connect
        self.client.on_message = self.on_message
        
        # 存储视频流处理任务
        self.processing_tasks = {}
        
    def on_connect(self, client, userdata, flags, rc):
        print(f"Connected with result code {rc}")
        client.subscribe(self.subscribe_topic)

    def on_message(self, client, userdata, msg):
        try:
            payload = json.loads(msg.payload.decode('utf-8'))
            print(f"Received message: {payload}")
            
            # 验证必要字段
            if not all(k in payload for k in ['client_id', 'streamUrlList', 'algorithmIdList']):
                print("Missing required fields in message")
                return
                
            # 处理每个视频流
            for i, stream_url in enumerate(payload['streamUrlList']):
                algorithm_ids = payload['algorithmIdList'][i]
                client_id = payload['client_id']
                
                # 为每个流启动处理线程
                if stream_url not in self.processing_tasks:
                    thread = threading.Thread(
                        target=self.process_video_stream,
                        args=(stream_url, algorithm_ids, client_id)
                    )
                    self.processing_tasks[stream_url] = thread
                    thread.start()
                    
        except Exception as e:
            print(f"Error processing message: {e}")
    
    def process_video_stream(self, stream_url, algorithm_ids, client_id):
        videoProcessor =VideoProcessor()
        videoProcessor.start_processing('stream_url',client_id,None,stream_url,None, dict(a),max_parallel_frames=1,callback_fun=self.print_result )
    def print_result(self, result):
        print(11111)
    def simulate_algorithm(self, frame, algorithm_id):
        """模拟算法处理，实际应用中替换为真正的算法"""
        # 示例: 使用OpenCV的简单目标检测
        # 实际应用中应该使用真正的AI模型
        
        # 转换为灰度图
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # 简单阈值处理
        _, thresh = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
        
        # 查找轮廓
        contours, _ = cv2.findContours(thresh, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
        
        box_list = []
        class_list = []
        
        # 只处理较大的轮廓
        for cnt in contours:
            area = cv2.contourArea(cnt)
            if area > 500:  # 面积阈值
                x, y, w, h = cv2.boundingRect(cnt)
                box_list.append([x, y, x+w, y+h])
                
                # 根据算法ID模拟不同分类结果
                if algorithm_id == "1":
                    class_list.append("轿车")
                elif algorithm_id == "2":
                    class_list.append("卡车")
                else:
                    class_list.append("未知")
        
        # 保存检测图片示例 (实际应用中可能需要上传到服务器)
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
        image_path = f"detection_{algorithm_id}_{timestamp}.jpg"
        cv2.imwrite(image_path, frame)
        
        return {
            "box_list": box_list,
            "class_list": class_list,
            "image_path": image_path
        }
    
    def send_detection_result(self, stream_url, algorithm_id, results, width, height):
        """发送检测结果到MQTT"""
        message = {
            "stream_url": stream_url,
            "algorithm_name": algorithm_id,
            "image_path": results["image_path"],
            "box_list": results["box_list"],
            "class_list": results["class_list"],
            "resolutionWidth": width,
            "resolutionHeight": height,
            "detectTime": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
        }
        
        try:
            self.client.publish(
                self.publish_topic,
                json.dumps(message),
                qos=1
            )
            print(f"Sent detection result for {stream_url}")
        except Exception as e:
            print(f"Error sending detection result: {e}")
    
    def start(self):
        # 连接MQTT并开始处理
        self.client.connect(self.mqtt_broker, self.mqtt_port, 60)
        self.client.loop_forever()

if __name__ == "__main__":
    processor = MqttProcessor()
    processor.start()