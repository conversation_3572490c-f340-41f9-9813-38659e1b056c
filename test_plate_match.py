#!/usr/bin/env python3
"""
车牌匹配服务测试脚本
"""

import requests
import json

def test_plate_match_service():
    """测试车牌匹配服务"""
    base_url = "http://localhost:8001"
    
    print("=" * 60)
    print("车牌匹配服务测试")
    print("=" * 60)
    
    # 1. 健康检查
    print("1. 健康检查...")
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            print("✅ 服务健康")
            print(f"   响应: {response.json()}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 无法连接到服务: {e}")
        print("请先启动服务: python plate_match_service.py")
        return
    
    # 2. 获取白名单
    print("\n2. 获取默认白名单...")
    try:
        response = requests.get(f"{base_url}/whitelist")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 白名单加载成功，共 {data['count']} 个车牌")
            print(f"   前5个车牌: {data['whitelist'][:5]}")
        else:
            print(f"❌ 获取白名单失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 获取白名单异常: {e}")
    
    # 3. 测试车牌匹配
    print("\n3. 测试车牌匹配...")
    test_data = {
        "detected_plates": ["赣AE28Q3", "川ADM8396", "京A12345"],
        "whitelist_plates": ["赣AE28Q2", "川ADM8397", "沪B67890", "赣AE28Q4", "川ADM8395"]
    }
    
    try:
        response = requests.post(
            f"{base_url}/match_plates",
            headers={"Content-Type": "application/json"},
            json=test_data
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 匹配成功")
            print(f"   状态: {result['success']}")
            print(f"   消息: {result['message']}")
            
            if result['matched_plates']:
                print(f"   最相似的匹配结果:")
                for i, match in enumerate(result['matched_plates'], 1):
                    detected = match['detected_plate']
                    whitelist_plate = match['whitelist_plate']
                    similarity = match['similarity']
                    reason = match['reason']
                    print(f"     [{i}] {detected} -> {whitelist_plate} (相似度: {similarity:.2f})")
                    print(f"         原因: {reason}")
            else:
                print("   无匹配结果")
        else:
            print(f"❌ 匹配失败: {response.status_code}")
            print(f"   错误: {response.text}")
            
    except Exception as e:
        print(f"❌ 匹配请求异常: {e}")
    
    # 4. 测试内置测试接口
    print("\n4. 测试内置测试接口...")
    try:
        response = requests.post(f"{base_url}/test_match")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 内置测试成功")
            print(f"   结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        else:
            print(f"❌ 内置测试失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 内置测试异常: {e}")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

def test_with_custom_data():
    """使用自定义数据测试"""
    base_url = "http://localhost:8001"
    
    print("\n" + "=" * 60)
    print("自定义数据测试")
    print("=" * 60)
    
    # 自定义测试数据
    custom_tests = [
        {
            "name": "相似车牌测试",
            "data": {
                "detected_plates": ["赣AE28Q3", "川ADM8396"],
                "whitelist_plates": ["赣AE28Q2", "赣AE28Q4", "川ADM8397", "川ADM8395"]
            }
        },
        {
            "name": "完全不匹配测试", 
            "data": {
                "detected_plates": ["京A12345", "沪B67890"],
                "whitelist_plates": ["粤C11111", "浙D22222", "苏E33333"]
            }
        },
        {
            "name": "单个车牌测试",
            "data": {
                "detected_plates": ["赣AE28Q3"],
                "whitelist_plates": ["赣AE28Q1", "赣AE28Q2", "赣AE28Q4", "赣AE28Q5"]
            }
        }
    ]
    
    for i, test in enumerate(custom_tests, 1):
        print(f"\n{i}. {test['name']}")
        print(f"   检测车牌: {test['data']['detected_plates']}")
        print(f"   白名单: {test['data']['whitelist_plates']}")
        
        try:
            response = requests.post(
                f"{base_url}/match_plates",
                headers={"Content-Type": "application/json"},
                json=test['data']
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ 匹配结果: {result['message']}")
                
                for match in result.get('matched_plates', []):
                    detected = match['detected_plate']
                    whitelist = match['whitelist_plate']
                    similarity = match['similarity']
                    print(f"     {detected} -> {whitelist} (相似度: {similarity:.2f})")
            else:
                print(f"   ❌ 请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")

if __name__ == "__main__":
    test_plate_match_service()
    test_with_custom_data()
