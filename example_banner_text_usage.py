#!/usr/bin/env python3
"""
横幅文字识别使用示例
演示如何在视频处理中集成横幅文字识别功能
"""

from model_manager import ModelManager
from videio_hander import VideoProcessor
import time


def main():
    """主函数 - 演示横幅文字识别的使用"""
    
    print("=" * 60)
    print("横幅文字识别使用示例")
    print("=" * 60)
    
    try:
        # 1. 初始化模型管理器
        print("1. 初始化模型管理器...")
        model_manager = ModelManager()
        
        # 2. 加载横幅文字识别模型
        print("2. 加载横幅文字识别模型...")
        config_path = "config/banner_text_recognition_config.yaml"
        banner_model = model_manager.load_model(config_path)
        
        if banner_model:
            print(f"✅ 模型加载成功: {banner_model.get_model_name()}")
        else:
            print("❌ 模型加载失败")
            return
        
        # 3. 创建视频处理器
        print("3. 创建视频处理器...")
        processor = VideoProcessor()
        
        # 4. 配置模型字典
        models = {
            'banner_text': banner_model
        }
        
        # 5. 示例：处理视频文件
        print("4. 配置视频处理任务...")
        
        # 配置参数
        task_config = {
            "task_id": "banner_text_demo",
            "device_sn": "demo_device_001", 
            "callback_url": None,  # 可以设置实际的回调URL
            "input_path": "/path/to/your/input/video.mp4",  # 替换为实际的输入视频路径
            "output_path": "rtmp://192.168.171.53:1935/live/banner_text_stream",  # 输出流地址
            "model_list": models,
            "max_parallel_frames": 2  # 由于使用API，建议减少并行帧数
        }
        
        print("配置信息:")
        print(f"  - 任务ID: {task_config['task_id']}")
        print(f"  - 设备SN: {task_config['device_sn']}")
        print(f"  - 输入路径: {task_config['input_path']}")
        print(f"  - 输出路径: {task_config['output_path']}")
        print(f"  - 并行帧数: {task_config['max_parallel_frames']}")
        
        # 注意：这里只是演示配置，实际使用时需要提供真实的视频路径
        print("\n⚠️  注意：请修改 input_path 为实际的视频文件路径后再运行")
        print("⚠️  注意：请确保输出流地址可用")
        
        # 6. 启动处理（注释掉，避免在没有实际视频文件时出错）
        """
        print("5. 启动视频处理...")
        success = processor.start_processing(**task_config)
        
        if success:
            print("✅ 视频处理任务启动成功")
            
            # 运行一段时间后停止（示例）
            print("运行30秒后自动停止...")
            time.sleep(30)
            
            # 停止处理
            processor.stop_processing(task_config["task_id"])
            print("✅ 视频处理任务已停止")
        else:
            print("❌ 视频处理任务启动失败")
        """
        
        # 7. 显示使用说明
        print("\n" + "=" * 60)
        print("使用说明:")
        print("1. 修改 task_config 中的 input_path 为实际视频文件路径")
        print("2. 确保 output_path 的流媒体服务器可用")
        print("3. 根据需要调整 callback_url 用于接收识别结果")
        print("4. 取消注释第6步的代码来实际运行处理")
        print("5. 可以通过 processor.list_tasks() 查看活动任务")
        print("6. 使用 processor.stop_processing(task_id) 停止特定任务")
        
        print("\n配置文件说明:")
        print("- 主配置文件: config/banner_text_recognition_config.yaml")
        print("- 可调整参数: confidence_threshold, max_tokens, temperature")
        print("- API配置: api_base, api_key, model_name")
        
        print("\n测试建议:")
        print("- 运行 test_banner_text_recognition.py 进行功能测试")
        print("- 检查网络连接到 OpenAI API 服务器")
        print("- 确保 API key 有效且有足够的配额")
        
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        import traceback
        traceback.print_exc()


def create_pki_package_example():
    """创建PKI包的示例说明"""
    print("\n" + "=" * 60)
    print("创建PKI包说明:")
    print("=" * 60)
    
    print("如果要创建可分发的PKI包，请按以下步骤:")
    print("1. 创建目录结构:")
    print("   banner_text_model/")
    print("   ├── config.yaml  (配置文件)")
    print("   └── (其他必要文件)")
    
    print("\n2. 将目录打包为ZIP文件:")
    print("   zip -r banner_text_model.pki banner_text_model/")
    
    print("\n3. 将PKI文件放入models目录:")
    print("   mv banner_text_model.pki models/")
    
    print("\n4. 模型管理器会自动加载PKI文件")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
    create_pki_package_example()
