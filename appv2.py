from fastapi import FastAP<PERSON>, HTTPException, UploadFile, File
from pydantic import BaseModel, field_validator
from model_manager import ModelManager
from typing import Dict, Any, List
import uvicorn
from videio_hander import VideoProcessor
import os
os.environ["CUDA_VISIBLE_DEVICES"] = "3" 
processor=VideoProcessor()
app = FastAPI()
model_manager = ModelManager()
model_manager.load_all_models()

class ModelPredictionSpec(BaseModel):
    model_id: str
    params: Dict[str, Any] = {}  # 每个模型独立的参数


class VideoProcessingRequest(BaseModel):
    streamUrlList: List[str]
    algorithmIdList: List[List[str]]
    client_id: str
    @field_validator('streamUrlList')
    def input_rtmp_url_must_not_be_empty(cls, v: List[str]):
        if v is None or len(v) == 0:
            raise ValueError("输入流地址不能为空")
        if  len(v) <2:
            raise ValueError("请输入输入流与输出流地址")
        return v
    @classmethod
    def from_json(cls, json_data: dict):
        return cls(
            streamUrlList=json_data["streamUrlList"],
            algorithmIdList=json_data["algorithmIdList"],
            client_id=json_data["client_id"]
        )

    def to_json(self):
        return {
            "streamUrlList": self.streamUrlList,
            "algorithmIdList": self.algorithmIdList,
            "client_id": self.client_id
        }



class StartTaskRequest(BaseModel):
    input_rtmp_url:str
    task_id:str
    device_sn:str|None
    app_code:str
    out_rtmp_url:str
    @field_validator('input_rtmp_url')
    def input_rtmp_url_must_not_be_empty(cls, v):
        if not v.strip():
            raise ValueError("输入流地址不能为空")
        return v
    @field_validator('task_id')
    def task_id_must_not_be_empty(cls, v):
        if not v.strip():
            raise ValueError("任务ID不能为空")
        return v
    @field_validator('app_code')
    def app_code_must_not_be_empty(cls, v):
        if not v.strip():
            raise ValueError("app_code不能为空")
        return v
    @field_validator('out_rtmp_url')
    def out_rtmp_url_not_be_empty(cls, v):
        if not v.strip():
            raise ValueError("输出流地址不能为空")
        return v

class StopTaskRequest(BaseModel):
    task_id:str
    @field_validator('task_id')
    def task_id_must_not_be_empty(cls, v):
        if not v.strip():
            raise ValueError("任务ID不能为空")
        return v
@app.get("/models")
async def list_models():
    """获取所有已加载模型信息，包括输入尺寸要求"""
    return model_manager.get_loaded_models()





@app.post('/streamOption')
async def streamOption(task:VideoProcessingRequest):
    #input_rtmp_url: rtmp://**************:19403/live/stream1
    #out_rtmp_url: rtmp://**************:19403/live/stream2
    key_list = 'car_door'.split(",")
    models={k: v for k, v in model_manager.loaded_models.items() if k in key_list}
    # if(len(models)==0):
    #     return {"status": "未找到匹配的模型"}
    processor.start_processing(task.client_id,task.streamUrlList[0],task.streamUrlList[1],models ,max_parallel_frames=1)
    return {"status": f"任务 {task.task_id} 已启动"}
@app.post('/stop')
async def stop(task:StopTaskRequest):
    processor.stop_processing(task.task_id)
    return  {"status": f"任务 {task.task_id} 已停止"}

def start_server(host: str = "0.0.0.0", port: int = 8000):
    """启动服务"""
    uvicorn.run(app, host=host, port=port)

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description="启动多模型预测服务")
    parser.add_argument("--host", type=str, default="0.0.0.0", help="服务监听地址")
    parser.add_argument("--port", type=int, default=8005, help="服务监听端口")
    args = parser.parse_args()
    
    start_server(host=args.host, port=args.port)