from fastapi import FastAP<PERSON>, HTTPException, UploadFile, File
from pydantic import BaseModel, field_validator
from model_manager import ModelManager
from typing import Dict, Any, List
import uvicorn
from videio_hander import VideoProcessor
import os
os.environ["CUDA_VISIBLE_DEVICES"] = "3" 
processor=VideoProcessor()
app = FastAPI()
model_manager = ModelManager()
model_manager.load_all_models()

class ModelPredictionSpec(BaseModel):
    model_id: str
    params: Dict[str, Any] = {}  # 每个模型独立的参数

class StartTaskRequest(BaseModel):
    input_rtmp_url:str
    task_id:str
    device_sn:str|None
    app_code:str
    out_rtmp_url:str
    @field_validator('input_rtmp_url')
    def input_rtmp_url_must_not_be_empty(cls, v):
        if not v.strip():
            raise ValueError("输入流地址不能为空")
        return v
    @field_validator('task_id')
    def task_id_must_not_be_empty(cls, v):
        if not v.strip():
            raise ValueError("任务ID不能为空")
        return v
    @field_validator('app_code')
    def app_code_must_not_be_empty(cls, v):
        if not v.strip():
            raise ValueError("app_code不能为空")
        return v
    @field_validator('out_rtmp_url')
    def out_rtmp_url_not_be_empty(cls, v):
        if not v.strip():
            raise ValueError("输出流地址不能为空")
        return v

class StopTaskRequest(BaseModel):
    task_id:str
    @field_validator('task_id')
    def task_id_must_not_be_empty(cls, v):
        if not v.strip():
            raise ValueError("任务ID不能为空")
        return v
@app.get("/models")
async def list_models():
    """获取所有已加载模型信息，包括输入尺寸要求"""
    return model_manager.get_loaded_models()

@app.get("/model/{model_id}/input_size")
async def get_model_input_size(model_id: str):
    """获取指定模型的输入尺寸要求"""
    try:
        h, w = model_manager.get_model_input_size(model_id)
        return {
            "model_id": model_id,
            "input_height": h,
            "input_width": w,
            "input_size": f"{w}x{h}"
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))




@app.post('/start')
async def start_task(task:StartTaskRequest):
    #input_rtmp_url: rtmp://**************:19403/live/stream1
    #out_rtmp_url: rtmp://**************:19403/live/stream2
    key_list = task.app_code.split(",")
    models={k: v for k, v in model_manager.loaded_models.items() if k in key_list}
    # if(len(models)==0):
    #     return {"status": "未找到匹配的模型"}
    processor.start_processing(task.task_id,task.input_rtmp_url,task.out_rtmp_url,models )
    return {"status": f"任务 {task.task_id} 已启动"}
@app.post('/stop')
async def stop(task:StopTaskRequest):
    processor.stop_processing(task.task_id)
    return  {"status": f"任务 {task.task_id} 已停止"}

def start_server(host: str = "0.0.0.0", port: int = 8000):
    """启动服务"""
    uvicorn.run(app, host=host, port=port)

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description="启动多模型预测服务")
    parser.add_argument("--host", type=str, default="0.0.0.0", help="服务监听地址")
    parser.add_argument("--port", type=int, default=8001, help="服务监听端口")
    args = parser.parse_args()
    
    start_server(host=args.host, port=args.port)