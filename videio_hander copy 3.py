import cv2
import subprocess
import threading
from typing import Dict, <PERSON><PERSON>, <PERSON><PERSON>, List
from dataclasses import dataclass
from collections import deque, OrderedDict
import numpy as np
import queue
import time
import json
from utils.image_util import are_images_similar_fast
@dataclass
class VideoProcessingTask:
    task_id: str
    input_path: str
    output_path: str
    models: Dict
    process: Optional[threading.Thread] = None
    stop_event: Optional[threading.Event] = None
    ffmpeg_process: Optional[subprocess.Popen] = None
    max_parallel_frames: int = 4
    frame_buffer: Optional[OrderedDict] = None
    next_frame_idx: int = 0
    frame_queue: Optional[queue.Queue] = None
    processed_frame_queue: Optional[queue.Queue] = None
    writer_thread: Optional[threading.Thread] = None
    worker_threads: Optional[list] = None
    check_stream_thread: Optional[list] = None
    is_changed: bool = False
    error_occurred: bool = False
    ffmpeg_retry_count: int = 0
    max_ffmpeg_retries: int = 3
    video_width: int = 0
    video_height: int = 0
    video_fps: float = 0
    current_stream_info: Optional[dict] = None
    stream_check_interval: int = 90  # Check stream info every 30 frames
    last_stream_check: int = 0
    target_height = 720

def _monitor_ffmpeg(task):
    while task.ffmpeg_process and not task.stop_event.is_set():
        line = task.ffmpeg_process.stderr.readline()
        if not line:
            break
        if b"Conversion failed" in line:
            task.error_occurred = True
            task.stop_event.set()

class VideoProcessor:
    def __init__(self):
        self.active_tasks: Dict[str, VideoProcessingTask] = {}
        self.lock = threading.Lock()

    def get_stream_info(self, input_path: str) -> Optional[dict]:
        """Get stream information using ffprobe"""
        try:
            cmd = [
                'ffprobe', '-v', 'error',
                '-i', input_path,
                '-select_streams', 'v:0',
                '-show_entries', 'stream=width,height,avg_frame_rate',
                '-of', 'json'
            ]
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode != 0:
                return None
                
            info = json.loads(result.stdout)
            if 'streams' not in info or len(info['streams']) == 0:
                return None
                
            stream = info['streams'][0]
            width = int(stream['width'])
            height = int(stream['height'])
            
            # Parse frame rate (can be in fraction form like 30000/1001)
            frame_rate = stream['avg_frame_rate']
            if '/' in frame_rate:
                num, den = map(int, frame_rate.split('/'))
                fps = num / den
            else:
                fps = float(frame_rate)
                
            return {
                'width': width,
                'height': height,
                'fps': fps
            }
        except Exception as e:
            print(f"Error getting stream info: {e}")
            return None

    def start_processing(
        self,
        task_id: str,
        input_path: str,
        output_path: str,
        model_list: Dict,
        max_parallel_frames: int = 6
    ) -> bool:
        """Start a new video processing task in a separate thread"""
        with self.lock:
            if task_id in self.active_tasks:
                print(f"Task ID {task_id} already exists")
                return False

            # Get initial stream info
            stream_info = self.get_stream_info(input_path)
            if not stream_info:
                print(f"Failed to get stream info for {input_path}")
                return False

            stop_event = threading.Event()
            frame_queue = queue.Queue(maxsize=max_parallel_frames * 2)
            processed_frame_queue = queue.Queue()
            
            task = VideoProcessingTask(
                task_id=task_id,
                input_path=input_path,
                output_path=output_path,
                models=model_list,
                stop_event=stop_event,
                max_parallel_frames=max_parallel_frames,
                frame_buffer=OrderedDict(),
                frame_queue=frame_queue,
                processed_frame_queue=processed_frame_queue,
                worker_threads=[],
                video_width=stream_info['width'],
                video_height=stream_info['height'],
                video_fps=stream_info['fps'],
                current_stream_info=stream_info
            )
            
            process_thread = threading.Thread(
                target=self._process_video,
                args=(task,),
                daemon=True
            )
            process_thread.start()
            task.process = process_thread
            
            self.active_tasks[task_id] = task
            return True

    def _check_stream_changes(self, task: VideoProcessingTask) -> bool:
        while True:
            """Check if stream resolution or FPS has changed"""
            if task.last_stream_check + task.stream_check_interval > task.next_frame_idx:
               continue
                
            task.last_stream_check = task.next_frame_idx
            
            new_info = self.get_stream_info(task.input_path)
            print(new_info)
            if not new_info:
                print(f"Warning: Could not get updated stream info for {task.task_id}")
                continue
                
            if (new_info['width'] != task.current_stream_info['width'] or
                new_info['height'] != task.current_stream_info['height'] or
                abs(new_info['fps'] - task.current_stream_info['fps']) > 0.5):
                
                print(f"Stream change detected in {task.task_id}: "
                    f"from {task.current_stream_info['width']}x{task.current_stream_info['height']}@{task.current_stream_info['fps']} "
                    f"to {new_info['width']}x{new_info['height']}@{new_info['fps']}")
                
                task.current_stream_info = new_info
                task.video_width = new_info['width']
                task.video_height = new_info['height']
                task.video_fps = new_info['fps']
                task.is_changed=True
           
            
      

    def _process_video(self, task: VideoProcessingTask):
        """Main video processing method with stream monitoring"""
        try:
            # Initialize worker threads
            for _ in range(task.max_parallel_frames):
                worker = threading.Thread(
                    target=self._frame_processor_worker,
                    args=(task,),
                    daemon=True
                )
                worker.start()
                task.worker_threads.append(worker)
            
            # Start frame writer thread
            task.writer_thread = threading.Thread(
                target=self._frame_writer,
                args=(task,),
                daemon=True
            )
            task.writer_thread.start()
          # Start frame writer thread
            task.check_stream_thread = threading.Thread(
                target=self._check_stream_changes,
                args=(task,),
                daemon=True
            )
            task.check_stream_thread.start()
            cap = None
            frame_idx = 0
              # Default target height
            scaled_width, scaled_height = 0, 0
            
            while not task.stop_event.is_set() and not task.error_occurred:
                # Check for stream changes periodically
                # if task.is_changed:
                #     print(f"Stream changed, reinitializing capture for {task.task_id}")
                #     if cap:
                #         cap.release()
                #     self._restart_ffmpeg_process(task)
                #     task.ffmpeg_retry_count=0
                #     cap = None
                #     task.is_changed=False
                    
                # Reinitialize capture if needed
                if cap is None or not cap.isOpened():
                    if cap:
                        cap.release()
                    
                    # Get current stream info
                    stream_info = task.current_stream_info
                    if not stream_info:
                        print(f"Task {task.task_id} - No stream info available")
                        task.error_occurred = True
                        break
                        
                    # Open new capture
                    cap = cv2.VideoCapture(task.input_path, cv2.CAP_FFMPEG)
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                    
                    if not cap.isOpened():
                        print(f"Task {task.task_id} - Failed to reopen video stream")
                        task.error_occurred = True
                        break
                        
                    # Calculate scaling
                    if stream_info['height'] <= task.target_height:
                        scaled_width, scaled_height = stream_info['width'], stream_info['height']
                    else:
                        scale_ratio = task.target_height / stream_info['height']
                        scaled_width = int(stream_info['width'] * scale_ratio)
                        scaled_height = task.target_height
                    
                    # Restart FFmpeg
                    if not self._restart_ffmpeg_process(task):
                        task.error_occurred = True
                        break
                
                # Read frame
                ret, frame = cap.read()
                if not ret:
                    print(f"Task {task.task_id} - Frame read failed, reconnecting...")
                    cap.release()
                    time.sleep(1)
                    cap = None
                    continue
                height, width, channels = frame.shape
                print(f"读取到一帧，尺寸: {width}x{height}")
        
                
                # Resize frame
                resized_frame = cv2.resize(
                    frame, 
                    (scaled_width, scaled_height), 
                    interpolation=cv2.INTER_AREA
                )
                
                # Put frame in queue
                try:
                    task.frame_queue.put((frame_idx, resized_frame), timeout=1.0)
                    frame_idx += 1
                except queue.Full:
                    if task.stop_event.is_set():
                        break
                    time.sleep(0.1)
                except Exception as e:
                    print(f"Task {task.task_id} - Frame processing error: {e}")
                    task.error_occurred = True
                    break

            # Wait for completion
            self._wait_for_completion(task, frame_idx)

        except Exception as e:
            print(f"Task {task.task_id} - Video processing error: {e}")
            task.error_occurred = True
        finally:
            self._cleanup_resources(task, cap)


    def stop_processing(self, task_id: str) -> bool:
        """Stop an ongoing video processing task"""
        with self.lock:
            task = self.active_tasks.get(task_id)
            if not task:
                print(f"Task ID {task_id} not found")
                return False
            
            task.stop_event.set()
            
            if task.ffmpeg_process:
                try:
                    task.ffmpeg_process.stdin.close()
                    task.ffmpeg_process.terminate()
                    task.ffmpeg_process.wait(timeout=2.0)
                except Exception as e:
                    print(f"Error stopping ffmpeg process: {e}")
                    try:
                        task.ffmpeg_process.kill()
                    except:
                        pass
            
            if task.process and task.process.is_alive():
                task.process.join(timeout=2.0)
                if task.process.is_alive():
                    print(f"Warning: Thread for task {task_id} did not stop gracefully")
            
            for worker in task.worker_threads:
                if worker.is_alive():
                    worker.join(timeout=1.0)
            
            if task.writer_thread and task.writer_thread.is_alive():
                task.writer_thread.join(timeout=1.0)
            
            if task_id in self.active_tasks:
                del self.active_tasks[task_id]
            return True

    def _process_frame(self, task: VideoProcessingTask, frame: np.ndarray, frame_idx: int) -> Tuple[int, np.ndarray]:
        """Process a single frame with all models"""
        try:
            processed_frame = frame.copy()
            
            for model_id, model in task.models.items():
                resized_frame = frame
                predictions = model.predict_pipeline(resized_frame)
                processed_frame = model.visualize(processed_frame, predictions)
                
            return frame_idx, processed_frame
        except Exception as e:
            print(f"Error processing frame {frame_idx}: {e}")
            return frame_idx, frame

    def _frame_processor_worker(self, task: VideoProcessingTask):
        """Worker thread for processing frames"""
        while not task.stop_event.is_set() and not task.error_occurred:
            try:
                frame_data = task.frame_queue.get(timeout=0.5)
                if frame_data is None:
                    task.frame_queue.task_done()
                    break
                    
                frame_idx, frame = frame_data
                result = self._process_frame(task, frame, frame_idx)
                task.processed_frame_queue.put(result)
                task.frame_queue.task_done()
            except queue.Empty:
                continue
            except Exception as e:
                print(f"Error in frame processor worker: {e}")
                task.error_occurred = True
                task.stop_event.set()
                break

    def _frame_writer(self, task: VideoProcessingTask):
        """Write processed frames to ffmpeg in order with retry mechanism"""
        while not task.stop_event.is_set() and not task.error_occurred:
            try:
                frame_idx, processed_frame = task.processed_frame_queue.get(timeout=0.5)
                
                if frame_idx == task.next_frame_idx:
                    success = self._write_frame_to_ffmpeg(task, processed_frame)
                    if not success:
                        task.processed_frame_queue.task_done()
                        continue
                        
                    task.next_frame_idx += 1
                    
                    while task.next_frame_idx in task.frame_buffer:
                        buffered_frame = task.frame_buffer.pop(task.next_frame_idx)
                        success = self._write_frame_to_ffmpeg(task, buffered_frame)
                        if not success:
                            break
                        task.next_frame_idx += 1
                else:
                    task.frame_buffer[frame_idx] = processed_frame
                    
                task.processed_frame_queue.task_done()
            except queue.Empty:
                continue
            except Exception as e:
                print(f"Error in frame writer: {e}")
                task.error_occurred = True
                task.stop_event.set()
                break

    def _write_frame_to_ffmpeg(self, task: VideoProcessingTask, frame: np.ndarray) -> bool:
        """Helper method to write frame to ffmpeg with retry mechanism"""
        max_retries = 3
        retry_delay = 0.5
        
        for attempt in range(max_retries):
            try:
                if task.ffmpeg_process is None:
                    # 尝试重新启动FFmpeg进程
                    if not self._restart_ffmpeg_process(task):
                        return False
                try:
                    frame_yuv = cv2.cvtColor(frame, cv2.COLOR_BGR2YUV_I420)
                    task.ffmpeg_process.stdin.write(frame_yuv.tobytes())
                    task.ffmpeg_process.stdin.flush()
                except subprocess.TimeoutExpired:
                    task.ffmpeg_process.kill()
                    return False
                return True
                
            except (BrokenPipeError, ConnectionResetError, OSError) as e:
                print(f"Task {task.task_id} - FFmpeg write error (attempt {attempt+1}): {str(e)}")
                
                # 检查是否超过最大重试次数
                if attempt == max_retries - 1:
                    print(f"Task {task.task_id} - Max retries reached for FFmpeg write")
                    task.error_occurred = True
                    task.stop_event.set()
                    return False
                
                # 尝试重新启动FFmpeg进程
                if not self._restart_ffmpeg_process(task):
                    return False
                
                # 等待一段时间再重试
                time.sleep(retry_delay * (attempt + 1))
                
        return False

    def _restart_ffmpeg_process(self, task: VideoProcessingTask) -> bool:
        """Restart FFmpeg process with retry logic"""
        if task.ffmpeg_retry_count >= task.max_ffmpeg_retries:
            print(f"Task {task.task_id} - Max FFmpeg retries reached ({task.max_ffmpeg_retries})")
            task.error_occurred = True
            task.stop_event.set()
            return False
        
        # 关闭现有进程
        if task.ffmpeg_process:
            try:
                task.ffmpeg_process.stdin.close()
                task.ffmpeg_process.terminate()
                task.ffmpeg_process.wait(timeout=2.0)
            except:
                pass
        
        # 增加重试计数
        task.ffmpeg_retry_count += 1
        print(f"Task {task.task_id} - Attempting to restart FFmpeg (attempt {task.ffmpeg_retry_count})")
        if  task.video_height <= task.target_height:
            scaled_width, scaled_height = task.video_width,  task.video_height
        else:
            scale_ratio = task.target_height /  task.video_height
            scaled_width = int(task.video_width * scale_ratio)
            scaled_height = task.target_height
        # 启动新进程
        task.ffmpeg_process = self._start_ffmpeg_process(
            task, 
            scaled_width,
            scaled_height, 
            task.video_fps
        )
        
        if task.ffmpeg_process is None:
            print(f"Task {task.task_id} - Failed to restart FFmpeg")
            task.error_occurred = True
            task.stop_event.set()
            return False
        monitor_thread = threading.Thread(target=_monitor_ffmpeg, args=(task,), daemon=True)
        monitor_thread.start()
        print(f"Task {task.task_id} - FFmpeg restarted successfully")
        return True
    def _open_video_capture(self, task: VideoProcessingTask):
        """Helper method to open video capture"""
        cap = cv2.VideoCapture(task.input_path)
        if not cap.isOpened():
            print(f"Task {task.task_id} - Failed to open input video: {task.input_path}")
            task.error_occurred = True
            return None
        return cap
    
    def _start_ffmpeg_process(self, task: VideoProcessingTask, width: int, height: int, fps: float):
        """Helper method to start FFmpeg process with retry support"""
        command = [
           'ffmpeg',  '-re',
        '-y',  # 覆盖输出文件（无需确认）
        '-f', 'rawvideo',  # 输入格式：原始视频帧
        '-vcodec', 'rawvideo',  # 输入编解码器：原始视频
         '-pix_fmt', 'yuv420p',  # 输入改为 YUV
        '-s', f'{width}x{height}',  # 输入分辨率（使用实际尺寸）
        '-r', str(fps),  # 输入帧率
        '-i', '-',  # 从标准输入读取数据
        '-c:v', 'h264_nvenc',  # 硬件编码
    '-preset', 'p7',
    '-tune', 'll',
    '-crf', '18',
        '-f', 'flv',  # 输出格式：FLV（常用于流媒体）
            task.output_path
        ]
        print(command)

        try:
            
            return subprocess.Popen(
                command,
                stdin=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
        except Exception as e:
            print(f"Task {task.task_id} - Failed to start FFmpeg: {e}")
            task.error_occurred = True
            return None

    def _wait_for_completion(self, task: VideoProcessingTask, total_frames: int):
        """Wait for all frames to be processed"""
        task.frame_queue.join()
        task.processed_frame_queue.join()
        
        while len(task.frame_buffer) > 0 and not task.stop_event.is_set():
            time.sleep(0.1)
        
        print(f"Task {task.task_id} - Processed {total_frames} frames")

    def _cleanup_resources(self, task: VideoProcessingTask, cap):
        """Clean up all resources"""
        for _ in range(len(task.worker_threads)):
            task.frame_queue.put(None)
        
        if cap:
            cap.release()
        
        if task.ffmpeg_process:
            try:
                if task.ffmpeg_process.stdin:
                    task.ffmpeg_process.stdin.close()
                task.ffmpeg_process.wait(timeout=2.0)
            except:
                pass
        
        with self.lock:
            if task.task_id in self.active_tasks:
                del self.active_tasks[task.task_id]
        if hasattr(task, 'ffmpeg_process') and task.ffmpeg_process:
            task.ffmpeg_process.__del__ = lambda x: x.terminate()  # 防止未终止的进程

    def list_tasks(self) -> List[str]:
        """List all active task IDs"""
        with self.lock:
            return list(self.active_tasks.keys())