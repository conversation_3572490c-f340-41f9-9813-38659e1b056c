import cv2
import subprocess
import threading
from typing import Callable, Dict, Op<PERSON>, <PERSON><PERSON>, List
from dataclasses import dataclass
from collections import deque, OrderedDict
import numpy as np
import queue
import time
from av_video_capture import AVVideoCapture
import traceback
from model_manager import ModelManager
import json

@dataclass
class VideoProcessingTask:
    task_id: str
    input_path: str
    output_path: str
    device_sn: str
    callback_url: str
    models: Dict
    process: Optional[threading.Thread] = None
    stop_event: Optional[threading.Event] = None
    ffmpeg_process: Optional[subprocess.Popen] = None
    max_parallel_frames: int = 4
    frame_buffer: Optional[OrderedDict] = None
    next_frame_idx: int = 0
    frame_queue: Optional[queue.Queue] = None
    processed_frame_queue: Optional[queue.Queue] = None
    writer_thread: Optional[threading.Thread] = None
    worker_threads: Optional[list] = None
    get_stream_resolution_thread: Optional[threading.Thread] = None
    error_occurred: bool = False
    ffmpeg_retry_count: int = 0
    max_ffmpeg_retries: int = 3
    video_width: int = 0
    video_height: int = 0
    video_fps: float = 0
    writer_lock: threading.Lock = threading.Lock()
    callback_fun: Callable[[np.ndarray, np.ndarray, int], None] = None
def _monitor_ffmpeg(task):
    while task.ffmpeg_process and not task.stop_event.is_set():
        line = task.ffmpeg_process.stderr.readline()
        if b"Conversion failed" in line:
            task.error_occurred = True
            task.stop_event.set()
class VideoProcessor:
    def __init__(self):
        self.active_tasks: Dict[str, VideoProcessingTask] = {}
        self.lock = threading.Lock()

    def start_processing(
        self,
        task_id: str,
        device_sn: str,
        callback_url: str,
        input_path: str,
        output_path: str,
        model_list: Dict,
        max_parallel_frames: int = 6,
        callback_fun: Callable[[np.ndarray, np.ndarray, int], None] = None,
    ) -> bool:
        """Start a new video processing task in a separate thread"""
        with self.lock:
            if task_id in self.active_tasks:
                print(f"Task ID {task_id} already exists")
                return False

            stop_event = threading.Event()
            frame_queue = queue.Queue(maxsize=max_parallel_frames * 2)
            processed_frame_queue = queue.Queue()
            task = VideoProcessingTask(
                task_id=task_id,
                input_path=input_path,
                 device_sn=device_sn,
                  callback_url=callback_url,
                output_path=output_path,
                models=model_list,
                stop_event=stop_event,
                max_parallel_frames=max_parallel_frames,
                frame_buffer=OrderedDict(),
                frame_queue=frame_queue,
                processed_frame_queue=processed_frame_queue,
                worker_threads=[],
                callback_fun=callback_fun
            )
            
            process_thread = threading.Thread(
                target=self._process_video,
                args=(task,),
                daemon=True
            )
            process_thread.start()
            task.process = process_thread
            
            self.active_tasks[task_id] = task
            return True

    def stop_processing(self, task_id: str) -> bool:
        """Stop an ongoing video processing task"""
        with self.lock:
            task = self.active_tasks.get(task_id)
            if not task:
                print(f"Task ID {task_id} not found")
                return False
            
            task.stop_event.set()
            
            if task.ffmpeg_process:
                try:
                    task.ffmpeg_process.stdin.close()
                    task.ffmpeg_process.terminate()
                    task.ffmpeg_process.wait(timeout=2.0)
                except Exception as e:
                    print(f"Error stopping ffmpeg process: {e}")
                    try:
                        task.ffmpeg_process.kill()
                    except:
                        pass
            
            if task.process and task.process.is_alive():
                task.process.join(timeout=2.0)
                if task.process.is_alive():
                    print(f"Warning: Thread for task {task_id} did not stop gracefully")
            
            for worker in task.worker_threads:
                if worker.is_alive():
                    worker.join(timeout=1.0)
            
            if task.writer_thread and task.writer_thread.is_alive():
                task.writer_thread.join(timeout=1.0)
            
            if task_id in self.active_tasks:
                del self.active_tasks[task_id]
            return True

    def _process_frame(self, task: VideoProcessingTask, frame: np.ndarray, frame_idx: int,timestamp) -> Tuple[int, np.ndarray]:
        """Process a single frame with all models"""
        try:
            processed_frame = frame.copy()
            
            for model_id, model in task.models.items():
                resized_frame = frame
                predictions = model.predict_pipeline(resized_frame)
                processed_frame = model.visualize(processed_frame, predictions)
                model.callback(task,processed_frame, predictions,timestamp)
                if task.callback_fun  is not None:
                    task.callback_fun(task,processed_frame, predictions,timestamp)
            return frame_idx, processed_frame
        except Exception as e:
            error_stack = traceback.format_exc()
            print(f"Error processing frame {frame_idx}: {e}")
            print("Error Stack:\n", error_stack)
            return frame_idx, frame

    def _frame_processor_worker(self, task: VideoProcessingTask):
        """Worker thread for processing frames"""
        while not task.stop_event.is_set() and not task.error_occurred:
            try:
                frame_data = task.frame_queue.get(timeout=0.5)
                if frame_data is None:
                    task.frame_queue.task_done()
                    break
                    
                frame_idx, frame,timestamp = frame_data
                result = self._process_frame(task, frame, frame_idx,timestamp)

                task.processed_frame_queue.put(result)
                task.frame_queue.task_done()
            except queue.Empty:
                continue
            except Exception as e:
                print(f"Error in frame processor worker: {e}")
                task.error_occurred = True
                task.stop_event.set()
                break

    def _frame_writer(self, task: VideoProcessingTask):
        """Write processed frames to ffmpeg in order with retry mechanism"""
        while not task.stop_event.is_set() and not task.error_occurred:
            try:
                frame_idx, processed_frame = task.processed_frame_queue.get(timeout=0.5)
                if frame_idx == task.next_frame_idx:
                    success = self._write_frame_to_ffmpeg(task, processed_frame)
                    if not success:
                        task.processed_frame_queue.task_done()
                        continue
                        
                    task.next_frame_idx += 1
                    
                    while task.next_frame_idx in task.frame_buffer:
                        buffered_frame = task.frame_buffer.pop(task.next_frame_idx)
                        success = self._write_frame_to_ffmpeg(task, buffered_frame)
                        if not success:
                            break
                        task.next_frame_idx += 1
                else:
                    task.frame_buffer[frame_idx] = processed_frame
                    
                task.processed_frame_queue.task_done()
            except queue.Empty:
                continue
            except Exception as e:
                print(f"Error in frame writer: {e}")
                task.error_occurred = True
                task.stop_event.set()
                break

    def _write_frame_to_ffmpeg(self, task: VideoProcessingTask, frame: np.ndarray) -> bool:
        """Helper method to write frame to ffmpeg with retry mechanism"""
        max_retries = 3
        retry_delay = 0.5
        
        for attempt in range(max_retries):
            try:
                if task.ffmpeg_process is None:
                    # 尝试重新启动FFmpeg进程
                    if not self._restart_ffmpeg_process(task):
                        return False
                try:
                   
                    frame_yuv = cv2.cvtColor(frame, cv2.COLOR_BGR2YUV_I420)
                    task.ffmpeg_process.stdin.write(frame_yuv.tobytes())
                    task.ffmpeg_process.stdin.flush()
                except subprocess.TimeoutExpired:
                    task.ffmpeg_process.kill()
                    return False
                return True
                
            except (BrokenPipeError, ConnectionResetError, OSError) as e:
                print(f"Task {task.task_id} - FFmpeg write error (attempt {attempt+1}): {str(e)}")
                
                # 检查是否超过最大重试次数
                if attempt == max_retries - 1:
                    print(f"Task {task.task_id} - Max retries reached for FFmpeg write")
                    task.error_occurred = True
                    task.stop_event.set()
                    return False
                
                # 尝试重新启动FFmpeg进程
                if not self._restart_ffmpeg_process(task):
                    return False
                
                # 等待一段时间再重试
                time.sleep(retry_delay * (attempt + 1))
                
        return False
    def _get_stream_resolution(self, task: VideoProcessingTask):
        
        while not task.stop_event.is_set() and not task.error_occurred:
            print(f'{task.stop_event.is_set()},{task.error_occurred}')
            try:
                # 判断输入类型
                is_stream = any(task.input_path.startswith(proto) 
                            for proto in ('rtmp://', 'rtsp://', 'http://', 'https://', 'udp://', 'tcp://'))
                
                # 使用ffprobe获取流信息
                cmd = [
                    'ffprobe',
                    '-v', 'error',
                    '-select_streams', 'v:0',
                    '-show_entries', 'stream=width,height',
                    '-of', 'json',
                ]
                
                # # 如果是推流，添加超时和立即返回参数的选项
                # if is_stream:
                #     cmd.extend(['-timeout', '5000000'])  # 5秒超时(单位微秒)
                #     cmd.extend(['-rw_timeout', '5000000'])  # 5秒读写超时
                
                cmd.append(task.input_path)
                
                result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                
                if result.returncode != 0:
                    print(f"FFprobe error: {result.stderr}")
                    if is_stream:
                        # 如果是推流，等待后重试
                        time.sleep(1)
                        continue
                    else:
                        # 如果是文件，可能是错误
                        raise Exception(f"FFprobe failed: {result.stderr}")
                print(result.stdout)
                info = json.loads(result.stdout)
                task.video_height = info['streams'][0]['height']
                task.video_width = info['streams'][0]['width']
                
            except Exception as e:
                print(f"Error getting resolution: {e}")
                if not is_stream:  # 如果是文件出错，直接退出循环
                    task.error_occurred = True
                    break
                time.sleep(1)  # 如果是推流出错，等待后重试
    def _restart_ffmpeg_process(self, task: VideoProcessingTask) -> bool:
        if task.output_path is None:
            return False
        """Restart FFmpeg process with retry logic"""
        if task.ffmpeg_retry_count >= task.max_ffmpeg_retries:
            print(f"Task {task.task_id} - Max FFmpeg retries reached ({task.max_ffmpeg_retries})")
            task.error_occurred = True
            task.stop_event.set()
            return False
        
        # 关闭现有进程
        if task.ffmpeg_process:
            try:
                task.ffmpeg_process.stdin.close()
                task.ffmpeg_process.terminate()
                task.ffmpeg_process.wait(timeout=2.0)
            except:
                pass
        
        # 增加重试计数
        task.ffmpeg_retry_count += 1
        print(f"Task {task.task_id} - Attempting to restart FFmpeg (attempt {task.ffmpeg_retry_count})")
        target_height=720
        if task.video_height <= target_height:
                        # 如果原始高度小于目标高度，保持原尺寸
            scaled_width, scaled_height = task.video_width, task.video_height
        else:
            # 否则按比例缩放
            scale_ratio = target_height / task.video_height
            scaled_width = int(task.video_width * scale_ratio)
            scaled_height = target_height
        # 启动新进程
        task.ffmpeg_process = self._start_ffmpeg_process(
            task, 
            scaled_width, 
            scaled_height, 
            task.video_fps
        )
        
        if task.ffmpeg_process is None:
            print(f"Task {task.task_id} - Failed to restart FFmpeg")
            task.error_occurred = True
            task.stop_event.set()
            return False
        monitor_thread = threading.Thread(target=_monitor_ffmpeg, args=(task,), daemon=True)
        monitor_thread.start()
        print(f"Task {task.task_id} - FFmpeg restarted successfully")
        return True
    def _open_video_capture(self, task: VideoProcessingTask):
        """Helper method to open video capture"""
        cap = cv2.VideoCapture(task.input_path)
        if not cap.isOpened():
            print(f"Task {task.task_id} - Failed to open input video: {task.input_path}")
            task.error_occurred = True
            return None
        return cap
    def _process_video(self, task: VideoProcessingTask):
        """处理视频的主方法"""
        try:
            # 初始化工作线程
            for _ in range(task.max_parallel_frames):
                worker = threading.Thread(
                    target=self._frame_processor_worker,
                    args=(task,),
                    daemon=True
                )
                worker.start()
                task.worker_threads.append(worker)
            if task.output_path is not None:
                # 启动帧写入线程
                task.writer_thread = threading.Thread(
                    target=self._frame_writer,
                    args=(task,),
                    daemon=True
                )
                task.writer_thread.start()
            task.get_stream_resolution_thread = threading.Thread(
                target=self._get_stream_resolution,
                args=(task,),
                daemon=True
            )
            task.get_stream_resolution_thread.start()

            
            # 使用FFMPEG后端打开视频流，减少缓冲区
            cap = cv2.VideoCapture(task.input_path)
            if not cap.isOpened():
                print(f"任务 {task.task_id} - 无法打开视频流")
                task.error_occurred = True
                return
           
            task.video_height = cap.get(cv2.CAP_PROP_FRAME_HEIGHT)
            task.video_width = cap.get(cv2.CAP_PROP_FRAME_WIDTH)
            task.video_fps = cap.get(cv2.CAP_PROP_FPS)
            frame_idx = 0
            current_width, current_height = 0, 0
            target_height = 720  # 默认目标高度
            while not task.stop_event.is_set() and not task.error_occurred:
                # 读取帧
                ret, frame = cap.read()
                if not ret:
                    # 尝试重新连接
                    print("帧读取失败，尝试重新连接...")
                    cap.release()
                    time.sleep(1)
                    cap = cv2.VideoCapture(task.input_path)
                    if not cap.isOpened():
                        task.error_occurred = True
                        break
                    
                
                    continue
                
                # 检查分辨率变化
                new_height, new_width = frame.shape[:2]
                if new_width != task.video_width or new_height != task.video_height:
                    print(f"分辨率调整 {current_width}x{current_height} 变为 {new_width}x{new_height}")
                    cap.release()
                    cap = cv2.VideoCapture(task.input_path)
                    if not cap.isOpened():
                        task.error_occurred = True
                    task.processed_frame_queue.empty()
                    task.next_frame_idx = frame_idx
                    self._restart_ffmpeg_process(task)
                    task.ffmpeg_retry_count = 0
                    continue
                
                if new_width != current_width or new_height != current_height:
                    print(f"分辨率从 {current_width}x{current_height} 变为 {new_width}x{new_height}")
                    current_width, current_height = new_width, new_height
                    
                    # 计算新的缩放尺寸
                    if new_height <= target_height:
                        # 如果原始高度小于目标高度，保持原尺寸
                        scaled_width, scaled_height = new_width, new_height
                    else:
                        # 否则按比例缩放
                        scale_ratio = target_height / new_height
                        scaled_width = int(new_width * scale_ratio)
                        scaled_height = target_height
                    
                    # 重启FFmpeg进程
                    if task.ffmpeg_process:
                        try:
                            task.ffmpeg_process.stdin.close()
                            task.ffmpeg_process.terminate()
                        except:
                            pass
                    task.processed_frame_queue.empty()
                    task.next_frame_idx = frame_idx
                    if task.output_path is not None:
                        task.ffmpeg_process = self._start_ffmpeg_process(
                            task, 
                            scaled_width, 
                            scaled_height, 
                            cap.get(cv2.CAP_PROP_FPS) or 25
                        )
                        if not task.ffmpeg_process:
                            task.error_occurred = True
                            break

                try:
                    # 缩放帧
                    resized_frame = cv2.resize(
                        frame, 
                        (scaled_width, scaled_height), 
                        interpolation=cv2.INTER_AREA
                    )
                    
                    # 放入处理队列
                    task.frame_queue.put((frame_idx, resized_frame,time.time()), timeout=1.0)
                    frame_idx += 1
                except queue.Full:
                    if task.stop_event.is_set():
                        break
                    time.sleep(0.1)
                except Exception as e:
                    print(f"帧处理错误: {e}")
                    task.error_occurred = True
                    break

            # 等待处理完成
            self._wait_for_completion(task, frame_idx)

        except Exception as e:
            print(f"视频处理错误: {e}")
            task.error_occurred = True
        finally:
            self._cleanup_resources(task, cap)
    def _start_ffmpeg_process(self, task: VideoProcessingTask, width: int, height: int, fps: float):
        """Helper method to start FFmpeg process with retry support"""
        command = [
           'ffmpeg',  '-re',
        '-y',  # 覆盖输出文件（无需确认）
        '-f', 'rawvideo',  # 输入格式：原始视频帧
        '-vcodec', 'rawvideo',  # 输入编解码器：原始视频
         '-pix_fmt', 'yuv420p',  # 输入改为 YUV
        '-s', f'{width}x{height}',  # 输入分辨率（使用实际尺寸）
        '-r', str(fps),  # 输入帧率
        '-i', '-',  # 从标准输入读取数据
        '-c:v', 'h264_nvenc',  # 硬件编码
    '-preset', 'p7',
    '-tune', 'll',
    '-crf', '18',
    '-g', str(fps * 2),  # 关键帧间隔(每2秒一个关键帧)
        '-f', 'flv',  # 输出格式：FLV（常用于流媒体）
            task.output_path
        ]

        try:
            
            return subprocess.Popen(
                command,
                stdin=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
        except Exception as e:
            print(f"Task {task.task_id} - Failed to start FFmpeg: {e}")
            task.error_occurred = True
            return None

    def _wait_for_completion(self, task: VideoProcessingTask, total_frames: int):
        """Wait for all frames to be processed"""
        task.frame_queue.join()
        task.processed_frame_queue.join()
        
        while len(task.frame_buffer) > 0 and not task.stop_event.is_set():
            time.sleep(0.1)
        
        print(f"Task {task.task_id} - Processed {total_frames} frames")

    def _cleanup_resources(self, task: VideoProcessingTask, cap):
        """Clean up all resources"""
        for _ in range(len(task.worker_threads)):
            task.frame_queue.put(None)
        
        if cap:
            cap.release()
        
        if task.ffmpeg_process:
            try:
                if task.ffmpeg_process.stdin:
                    task.ffmpeg_process.stdin.close()
                task.ffmpeg_process.wait(timeout=2.0)
            except:
                pass
        
        with self.lock:
            if task.task_id in self.active_tasks:
                del self.active_tasks[task.task_id]
        if hasattr(task, 'ffmpeg_process') and task.ffmpeg_process:
            task.ffmpeg_process.__del__ = lambda x: x.terminate()  # 防止未终止的进程

    def list_tasks(self) -> List[str]:
        """List all active task IDs"""
        with self.lock:
            return list(self.active_tasks.keys())
if __name__ == "__main__":
    modelManager = ModelManager()
    
    processor = VideoProcessor()
    # a = {'a':modelManager.load_model('/home/<USER>/project/modelManger/models/yellow_grid.pki')}
    # a = {'a':modelManager.load_model('/home/<USER>/project/modelManger/models/person_crowd.pki')}
    a = {'a':modelManager.load_model('/home/<USER>/project/modelManger/models/car_direction.pki')}
    # processor.start_processing("output.mp4","sn12312","url","/home/<USER>/project/yellow-grid/yellowgrid.mp4", "rtmp://192.168.171.53:1935/live/stream4", dict(a),max_parallel_frames=1)
    # processor.start_processing("output.mp4","sn12312","url","/home/<USER>/project/yellow-grid/球场.mp4", "rtmp://192.168.171.53:1935/live/stream4", dict(a))
    processor.start_processing("output.mp4","sn12312","url","/home/<USER>/project/yellow-grid/693_1747830805_raw.mp4", "rtmp://192.168.171.53:1935/live/stream4", dict(a))
    while True:
        time.sleep(1)